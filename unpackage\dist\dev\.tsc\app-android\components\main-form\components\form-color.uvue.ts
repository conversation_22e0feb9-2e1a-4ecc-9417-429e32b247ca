
	import { FormFieldData, FormChangeEvent } from '@/components/main-form/form_type.uts'
	import FormContainer from './form-container.uvue'
	import MainColorPicker from './../tools/main-color-picker.uvue'

	const __sfc__ = defineComponent({
		name: "FormColor",
		emits: ['change'],
		components: {
			FormContainer,
			MainColorPicker
		},
		props: {
			data: {
				type: null as any as PropType<FormFieldData>
			},
			index: {
				type: Number,
				default: 0
			},
			keyName: {
				type: String,
				default: ""
			},
			labelColor: {
				type: String,
				default: "#000"
			},
			backgroundColor: {
				type: String,
				default: "#f1f4f9"
			}
		},
		data() {
			return {
				fieldName: "",
				fieldValue: "" as string,
				isSave: false,
				save_key: "",
				tip: "",
				varType: "hex",
				displayColor: "#000000",
				showError: false,
				errorMessage: ""
			}
		},
		computed: {

		},
		watch: {
			data: {
				handler(obj: FormFieldData) {
					// 只处理value的变化，当外部传入的value与当前fieldValue不同时，才更新fieldValue
					// 这避免了用户输入时的循环更新问题
					const newValue = obj.value as string 
					if (newValue !== this.fieldValue) {
						this.fieldValue = newValue
						this.updateDisplayColor()
					}
				},
				deep: true
			}
		},
		created(): void {
			// 初始化时调用一次即可
			const fieldObj = this.$props["data"] as FormFieldData
			this.initFieldData(fieldObj)
		},
		methods: {
			// 初始化字段数据（仅在首次加载时调用）
			initFieldData(fieldObj: FormFieldData): void {
				const fieldKey = fieldObj.key
				const fieldValue = fieldObj.value as string 

				// 设置基本信息
				this.fieldName = fieldObj.name
				this.fieldValue = fieldValue
				this.isSave = fieldObj.isSave ?? false
				this.save_key = this.keyName + "_" + fieldKey

				// 解析配置信息
				const extalJson = fieldObj.extra as UTSJSONObject
				this.tip = extalJson.getString("tip") ?? ""
				this.varType = extalJson.getString("varType") ?? "hex"

				// 更新显示颜色
				this.updateDisplayColor()

				// 获取缓存
				this.getCache()
			},

			// 更新显示颜色
			updateDisplayColor(): void {
				if (this.fieldValue != "") {
					this.displayColor = this.fieldValue as string
				} else {
					// 根据varType设置默认颜色
					if (this.varType == "rgba") {
						this.displayColor = "rgba(0, 0, 0, 1)"
						this.fieldValue = "rgba(0, 0, 0, 1)"
					} else {
						this.displayColor = "#000000"
						this.fieldValue = "#000000"
					}
				}
			},

			getCache(): void {
				if (this.isSave) {
					const that = this
					uni.getStorage({
						key: this.save_key,
						success: (res: GetStorageSuccess) => {
							const save_value = res.data
							if(typeof save_value === 'string'){
								that.fieldValue = save_value as string
								that.updateDisplayColor()
								const result: FormChangeEvent = {
									index: this.index,
									value: save_value
								}
								this.change(result)
							}
							
						}
					})
				}
			},

			setCache(): void { 
				if (this.isSave && typeof this.fieldValue ==="string") {
					uni.setStorage({
						key: this.save_key,
						data: this.fieldValue
					})
				}
			},

			validate(): boolean {
				// 颜色值验证
				if (this.fieldValue == "") {
					this.showError = true
					this.errorMessage = "请选择颜色"
					return false
				}

				// 根据varType验证颜色格式
				const colorValue = this.fieldValue as string
				if (this.varType == "hex") {
					const hexPattern = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/
					if (!hexPattern.test(colorValue)) {
						this.showError = true
						this.errorMessage = "颜色格式不正确"
						return false
					}
				} else if (this.varType == "rgba") {
					const rgbaPattern = /^rgba\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*,\s*[\d.]+\s*\)$/
					if (!rgbaPattern.test(colorValue)) {
						this.showError = true
						this.errorMessage = "颜色格式不正确"
						return false
					}
				}

				this.showError = false
				this.errorMessage = ""
				return true
			},

			change(event: FormChangeEvent): void {
				// 更新字段值
				this.fieldValue = event.value as string 
				// 更新显示颜色
				this.updateDisplayColor()
				// 保存缓存
				this.setCache()
				// 触发父组件事件
				this.$emit('change', event)
			},

			// 打开颜色选择器
			openColorPicker(): void {
				const colorPicker = this.$refs["colorPicker"] as ComponentPublicInstance
				colorPicker.$callMethod("open")
			},

			// 颜色选择确认
			onColorConfirm(colorData: UTSJSONObject): void {
				let selectedColor: string

				if (this.varType == "rgba") {
					// 使用rgba格式
					selectedColor = colorData.getString("color") ?? "rgba(0, 0, 0, 1)"
				} else {
					// 使用hex格式
					selectedColor = colorData.getString("hex") ?? "#000000"
				}

				const result: FormChangeEvent = {
					index: this.index,
					value: selectedColor
				}
				this.change(result)
			},

			// 颜色选择取消
			onColorCancel(): void {
				// 取消选择，不做任何操作
			}
		}
	})

export default __sfc__
function GenComponentsMainFormComponentsFormColorRender(this: InstanceType<typeof __sfc__>): any | null {
const _ctx = this
const _cache = this.$.renderCache
const _component_form_container = resolveComponent("form-container")
const _component_main_color_picker = resolveEasyComponent("main-color-picker",_easycom_main_color_picker)

  return _cE(Fragment, null, [
    _cV(_component_form_container, _uM({
      label: _ctx.fieldName,
      "show-error": _ctx.showError,
      tip: _ctx.tip,
      "error-message": _ctx.errorMessage,
      "label-color": _ctx.labelColor,
      "background-color": _ctx.backgroundColor
    }), _uM({
      "input-content": withSlotCtx((): any[] => [
        _cE("view", _uM({
          class: "color-display-container",
          onClick: _ctx.openColorPicker
        }), [
          _cE("view", _uM({
            class: "color-preview",
            style: _nS(_uM({ backgroundColor: _ctx.displayColor }))
          }), null, 4 /* STYLE */),
          _cE("text", _uM({ class: "color-text" }), _tD(_ctx.displayColor), 1 /* TEXT */)
        ], 8 /* PROPS */, ["onClick"])
      ]),
      _: 1 /* STABLE */
    }), 8 /* PROPS */, ["label", "show-error", "tip", "error-message", "label-color", "background-color"]),
    _cV(_component_main_color_picker, _uM({
      ref: "colorPicker",
      onConfirm: _ctx.onColorConfirm,
      onCancel: _ctx.onColorCancel
    }), null, 8 /* PROPS */, ["onConfirm", "onCancel"])
  ], 64 /* STABLE_FRAGMENT */)
}
const GenComponentsMainFormComponentsFormColorStyles = [_uM([["color-display-container", _pS(_uM([["flex", 1], ["display", "flex"], ["flexDirection", "row"], ["alignItems", "center"], ["minHeight", "60rpx"], ["paddingTop", "10rpx"], ["paddingRight", "10rpx"], ["paddingBottom", "10rpx"], ["paddingLeft", "10rpx"], ["borderTopLeftRadius", "10rpx"], ["borderTopRightRadius", "10rpx"], ["borderBottomRightRadius", "10rpx"], ["borderBottomLeftRadius", "10rpx"], ["backgroundColor", "rgba(255,255,255,0.8)"]]))], ["color-preview", _pS(_uM([["width", "60rpx"], ["height", "40rpx"], ["borderTopLeftRadius", "8rpx"], ["borderTopRightRadius", "8rpx"], ["borderBottomRightRadius", "8rpx"], ["borderBottomLeftRadius", "8rpx"], ["borderTopWidth", "1rpx"], ["borderRightWidth", "1rpx"], ["borderBottomWidth", "1rpx"], ["borderLeftWidth", "1rpx"], ["borderTopStyle", "solid"], ["borderRightStyle", "solid"], ["borderBottomStyle", "solid"], ["borderLeftStyle", "solid"], ["borderTopColor", "#e5e5e5"], ["borderRightColor", "#e5e5e5"], ["borderBottomColor", "#e5e5e5"], ["borderLeftColor", "#e5e5e5"], ["marginRight", "20rpx"], ["boxShadow", "0 2rpx 4rpx rgba(0, 0, 0, 0.1)"]]))], ["color-text", _pS(_uM([["flex", 1], ["fontSize", "28rpx"], ["color", "#333333"], ["fontFamily", "monospace"]]))]])]

import _easycom_main_color_picker from '@/components/main-color-picker/main-color-picker.uvue'
