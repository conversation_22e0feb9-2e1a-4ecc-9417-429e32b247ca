<template>
	<!-- 弹窗遮罩层 -->
	<view v-if="visible" class="picker-overlay" @click="onOverlayClick">
		<view class="picker-modal" @click.stop="">
			<view class="color-picker-container">
				<!-- 导航栏 -->
				<view class="navbar">
					<text class="nav-btn cancel-btn" @click="onCancel">取消</text>
					<text class="nav-title">颜色选择</text>
					<view class="confirm-btn-container">
						<text class="nav-btn confirm-btn" @click="onConfirm">确定</text>
					</view>
				</view>

				<!-- 颜色系列选择按钮 -->
				<view class="color-series-section">
					<view class="color-series-buttons">
						<view
							v-for="(series, index) in colorSeriesList"
							:key="index"
							class="series-button"
							:class="{
								'active': selectedSeriesIndex == index,
								'random-button': index == 0,
								'normal-button': index != 0
							}"
							:style="{ backgroundColor: series.color }"
							@click="onSeriesSelect(index)"
						>
							<text class="series-text">{{ series.name }}</text>
						</view>
					</view>
				</view>

				<!-- 颜色方块列表 -->
				<view class="color-grid-section">
					<view class="color-grid">
						<view v-for="(color, index) in colorList" :key="index" class="color-item"
							:class="{ 'selected': selectedColorIndex == index }" :style="{ backgroundColor: color }"
							@click="onColorSelect(index)">
						</view>
					</view>
				</view>

				<!-- 预览和透明度选择区域 -->
				<view class="preview-opacity-section">
					<view class="preview-area" @click="showRGBPicker">
						<view class="preview-color" :style="{ backgroundColor: finalColor }"></view>
						<text class="rgba-text">{{ finalColor }}</text>
					</view>
					<view class="opacity-area">
						<text class="opacity-label">透明度</text>
						<view class="opacity-button" @click="showOpacityPicker">
							<text class="opacity-value">{{ Math.round(opacity * 100) }}%</text>
						</view>
					</view>
				</view>

				<!-- RGB设置弹窗 -->
				<view v-if="showRGBModal" class="rgb-modal-overlay" @click="closeRGBPicker">
					<view class="rgb-modal" @click="onRGBModalClick">
						<view class="rgb-modal-header">
							<text class="rgb-modal-title">RGB颜色设置</text>
						</view>

						<view class="rgb-preview-section">
							<view class="rgb-preview-color" :style="{ backgroundColor: tempRGBColor }"></view>
							<text class="rgb-preview-text">{{ tempRGBColor }}</text>
						</view>

						<view class="rgb-controls">
							<!-- R值控制 -->
							<view class="rgb-control-item">
								<text class="rgb-label">R</text>
								<slider class="rgb-slider" :min="0" :max="255" :step="1" :value="tempR"
									@change="onTempRChange" />
								<input class="rgb-input" type="number" :value="tempR.toString()"
									@input="onTempRInput" placeholder="0-255" />
							</view>

							<!-- G值控制 -->
							<view class="rgb-control-item">
								<text class="rgb-label">G</text>
								<slider class="rgb-slider" :min="0" :max="255" :step="1" :value="tempG"
									@change="onTempGChange" />
								<input class="rgb-input" type="number" :value="tempG.toString()"
									@input="onTempGInput" placeholder="0-255" />
							</view>

							<!-- B值控制 -->
							<view class="rgb-control-item">
								<text class="rgb-label">B</text>
								<slider class="rgb-slider" :min="0" :max="255" :step="1" :value="tempB"
									@change="onTempBChange" />
								<input class="rgb-input" type="number" :value="tempB.toString()"
									@input="onTempBInput" placeholder="0-255" />
							</view>
						</view>

						<view class="rgb-modal-buttons">
							<view class="rgb-button rgb-cancel" @click="closeRGBPicker">
								<text class="rgb-button-text">取消</text>
							</view>
							<view class="rgb-button rgb-confirm" @click="confirmRGBPicker">
								<text class="rgb-button-text">确定</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	// 定义颜色类型
	type ColorInfo = {
		r : number,
		g : number,
		b : number
	}
	type RGBAValues = {
	  r: number,
	  g: number,
	  b: number,
	  a: number
	}
	type RGBValues = {
	  r: number,
	  g: number,
	  b: number
	}
	type ColorSeries = {
	  name: string,
	  color: string
	}
	export default {
		name: "main-color-picker",
		emits: ['cancel', 'confirm'],
		data() {
			return {
				// 控制弹窗显示
				visible: false as boolean,
				// 当前选中的颜色系列索引
				selectedSeriesIndex: 0 as number,
				// 透明度，范围0-1
				opacity: 1.0 as number,
				// 当前选中的颜色索引
				selectedColorIndex: 0 as number,
				// 基础颜色（可以根据需要修改）
				baseColor: { r: 255.0, g: 0.0, b: 0.0 } as ColorInfo,
				// 随机色种子，用于重新生成随机色
				randomSeed: 0 as number,
				// RGB设置弹窗相关
				showRGBModal: false as boolean,
				tempR: 255 as number,
				tempG: 0 as number,
				tempB: 0 as number,
				// 自定义颜色
				customColor: "" as string
			}
		},
		computed: {
			// 颜色系列列表
			colorSeriesList(): ColorSeries[] {
				return [
					{ name: "随机色", color: "#FF6B35" },
					{ name: "黑白灰", color: "#808080" },
					{ name: "红色", color: "#FF4444" },
					{ name: "橙色", color: "#FF8844" },
					{ name: "黄色", color: "#FFDD44" },
					{ name: "绿色", color: "#44FF44" },
					{ name: "青色", color: "#44FFFF" },
					{ name: "蓝色", color: "#4444FF" },
					{ name: "紫色", color: "#AA44FF" },
					{ name: "粉色", color: "#FF88CC" },
					{ name: "棕色", color: "#AA6644" }
				]
			},

			// 根据选中的系列生成120个颜色（10行12列）
			colorList() : string[] {
				const colors : string[] = []

				for (let i = 0; i < 120; i++) {
					const row = Math.floor(i / 12) // 当前行（0-9）
					const col = i % 12 // 当前列（0-11）

					// 计算位置因子
					const rowFactor = row / 9.0 // 行因子 0-1
					const colFactor = col / 11.0 // 列因子 0-1

					// 基于选中的系列索引确定颜色系列
					let r: number, g: number, b: number

					if (this.selectedSeriesIndex == 0) {
						// 随机色系列 - 每个方块完全随机的RGB值
						const seed1 = (row * 12 + col + this.randomSeed) * 0.1
						const seed2 = (row * 12 + col + this.randomSeed + 100) * 0.13
						const seed3 = (row * 12 + col + this.randomSeed + 200) * 0.17
						r = Math.round((Math.sin(seed1) * 0.5 + 0.5) * 255)
						g = Math.round((Math.sin(seed2) * 0.5 + 0.5) * 255)
						b = Math.round((Math.sin(seed3) * 0.5 + 0.5) * 255)
					} else if (this.selectedSeriesIndex == 1) {
						// 黑白灰系列 - 更细腻的灰度变化
						const totalFactor = (row * 12 + col) / 119.0 // 0到1的完整渐变
						const grayValue = Math.round(totalFactor * 255)
						r = grayValue
						g = grayValue
						b = grayValue
					} else if (this.selectedSeriesIndex == 2) {
						// 红色系列 - 更丰富的红色变化
						const totalFactor = (row * 12 + col) / 119.0 // 0到1
						const brightness = 0.2 + totalFactor * 0.8 // 0.2-1.0的亮度范围
						const saturation = 0.3 + (1 - Math.abs(totalFactor - 0.5) * 2) * 0.7 // 中间饱和度高
						r = Math.round(brightness * 255)
						g = Math.round(brightness * (1 - saturation) * 255)
						b = Math.round(brightness * (1 - saturation) * 255)
					} else {
						// 其他颜色系列 - 确保包含纯色且避免黑色
						const totalFactor = (row * 12 + col) / 119.0 // 0到1

						// 根据系列索引确定基础色相
						let baseHue: number
						if (this.selectedSeriesIndex == 3) baseHue = 30      // 橙色
						else if (this.selectedSeriesIndex == 4) baseHue = 60 // 黄色
						else if (this.selectedSeriesIndex == 5) baseHue = 120 // 绿色
						else if (this.selectedSeriesIndex == 6) baseHue = 180 // 青色
						else if (this.selectedSeriesIndex == 7) baseHue = 240 // 蓝色
						else if (this.selectedSeriesIndex == 8) baseHue = 300 // 紫色
						else if (this.selectedSeriesIndex == 9) baseHue = 330 // 粉色
						else baseHue = 25 // 棕色

						// 色相微调：在基础色相±10度范围内变化
						const hue = baseHue + (colFactor - 0.5) * 20

						// 创造三种类型的颜色变化
						if (totalFactor < 0.4) {
							// 前40%：深色调 - 高饱和度，中低明度（避免太暗）
							const localFactor = totalFactor / 0.4
							const saturation = 0.8 + localFactor * 0.2 // 0.8-1.0
							const value = 0.4 + localFactor * 0.3 // 0.4-0.7（避免太暗）
							const rgb = this.hsvToRgb(hue, saturation, value)
							r = rgb.r
							g = rgb.g
							b = rgb.b
						} else if (totalFactor < 0.6) {
							// 中20%：纯色调 - 最高饱和度，最佳明度
							const localFactor = (totalFactor - 0.4) / 0.2
							const saturation = 1.0 // 最高饱和度
							const value = 0.8 + localFactor * 0.2 // 0.8-1.0（确保亮度足够）
							const rgb = this.hsvToRgb(hue, saturation, value)
							r = rgb.r
							g = rgb.g
							b = rgb.b
						} else {
							// 后40%：浅色调 - 降低饱和度，保持高明度
							const localFactor = (totalFactor - 0.6) / 0.4
							const saturation = 0.8 - localFactor * 0.6 // 0.8-0.2（逐渐降低饱和度）
							const value = 0.9 + localFactor * 0.1 // 0.9-1.0（保持高明度）
							const rgb = this.hsvToRgb(hue, saturation, value)
							r = rgb.r
							g = rgb.g
							b = rgb.b
						}
					}

					// 确保RGB值在0-255范围内
					r = Math.max(0, Math.min(255, r))
					g = Math.max(0, Math.min(255, g))
					b = Math.max(0, Math.min(255, b))

					colors.push(`rgb(${r}, ${g}, ${b})`)
				}

				return colors
			},



			// 最终的RGBA颜色值
			finalColor() : string {
				// 优先使用自定义颜色
				let colorToUse = ""
				if (this.customColor != "") {
					colorToUse = this.customColor
				} else if (this.colorList.length > this.selectedColorIndex) {
					colorToUse = this.colorList[this.selectedColorIndex]
				}

				if (colorToUse != "") {
					// 提取RGB值并添加透明度
					const rgbMatch = colorToUse.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/)
					if (rgbMatch != null) {
						const r = parseInt(rgbMatch[1] as string)
						const g = parseInt(rgbMatch[2] as string)
						const b = parseInt(rgbMatch[3] as string)
						return `rgba(${r}, ${g}, ${b}, ${this.opacity})`
					}
				}
				return `rgba(255, 0, 0, ${this.opacity})`
			},

			// 临时RGB颜色预览
			tempRGBColor() : string {
				return `rgb(${this.tempR}, ${this.tempG}, ${this.tempB})`
			}
		},
		methods: {
			// 颜色系列选择事件
			onSeriesSelect(index: number) {
				this.selectedSeriesIndex = index
				this.selectedColorIndex = 0 // 重置选中的颜色
				this.customColor = "" // 清除自定义颜色

				// 如果选择的是随机色系列，生成新的随机种子
				if (index == 0) { // 随机色现在是第1个按钮，索引为0
					this.randomSeed = Math.floor(Math.random() * 1000)
				}
			},

			// 显示透明度选择器
			showOpacityPicker() {
				const opacityOptions = [
					'100%', '95%', '90%', '85%', '80%', '75%', '70%', '65%', '60%', '55%',
					'50%', '45%', '40%', '35%', '30%', '25%', '20%', '15%', '10%', '5%'
				]

				uni.showActionSheet({
					itemList: opacityOptions,
					success: (res) => {
						const selectedOpacity = (100 - res.tapIndex * 5) / 100
						this.opacity = selectedOpacity
					}
				})
			},

			// 颜色选择事件
			onColorSelect(index : number) {
				this.selectedColorIndex = index
				// 清除自定义颜色，使用新选中的颜色
				this.customColor = ""
			},

			// 显示RGB设置弹窗
			showRGBPicker() {
				// 获取当前颜色的RGB值（优先使用自定义颜色）
				let colorToUse = ""
				if (this.customColor != "") {
					colorToUse = this.customColor
				} else if (this.colorList.length > this.selectedColorIndex) {
					colorToUse = this.colorList[this.selectedColorIndex]
				}

				if (colorToUse != "") {
					const rgbMatch = colorToUse.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/)
					if (rgbMatch != null) {
						this.tempR = parseInt(rgbMatch[1] as string)
						this.tempG = parseInt(rgbMatch[2] as string)
						this.tempB = parseInt(rgbMatch[3] as string)
					} else {
						this.tempR = 255
						this.tempG = 0
						this.tempB = 0
					}
				} else {
					this.tempR = 255
					this.tempG = 0
					this.tempB = 0
				}
				this.showRGBModal = true
			},

			// 关闭RGB设置弹窗
			closeRGBPicker() {
				this.showRGBModal = false
			},

			// RGB弹窗点击事件（阻止冒泡）
			onRGBModalClick() {
				// 空方法，用于阻止事件冒泡
			},

			// 确认RGB设置
			confirmRGBPicker() {
				// 设置自定义颜色
				this.customColor = `rgb(${this.tempR}, ${this.tempG}, ${this.tempB})`
				this.showRGBModal = false
			},

			// R值滑块变化
			onTempRChange(event: UniSliderChangeEvent) {
				this.tempR = event.detail.value as number
			},

			// G值滑块变化
			onTempGChange(event: UniSliderChangeEvent) {
				this.tempG = event.detail.value as number
			},

			// B值滑块变化
			onTempBChange(event: UniSliderChangeEvent) {
				this.tempB = event.detail.value as number
			},

			// R值输入框变化
			onTempRInput(event: UniInputEvent) {
				const value = parseInt(event.detail.value)
				if (!isNaN(value)) {
					this.tempR = Math.max(0, Math.min(255, value))
				}
			},

			// G值输入框变化
			onTempGInput(event: UniInputEvent) {
				const value = parseInt(event.detail.value)
				if (!isNaN(value)) {
					this.tempG = Math.max(0, Math.min(255, value))
				}
			},

			// B值输入框变化
			onTempBInput(event: UniInputEvent) {
				const value = parseInt(event.detail.value)
				if (!isNaN(value)) {
					this.tempB = Math.max(0, Math.min(255, value))
				}
			},

			// 打开弹窗
			open() {
				this.visible = true
			},

			// 关闭弹窗
			close() {
				this.visible = false
			},

			// 点击遮罩层关闭弹窗
			onOverlayClick() {
				this.close()
				this.$emit('cancel')
			},

			// 取消按钮点击事件
			onCancel() {
				this.close()
				this.$emit('cancel')
			},

			// 确定按钮点击事件
			onConfirm() {
				this.close()
				const rgbaValues = this.getRGBAValues()
				this.$emit('confirm', {
					color: this.finalColor,
					rgba: rgbaValues,
					hex: this.rgbToHex(rgbaValues.r, rgbaValues.g, rgbaValues.b)
				})
			},
 
			// 获取RGBA数值
			getRGBAValues() : RGBAValues {
				const rgbaMatch = this.finalColor.match(/rgba\((\d+),\s*(\d+),\s*(\d+),\s*([\d.]+)\)/)
				if (rgbaMatch != null) {
					return {
						r: parseInt(rgbaMatch[1] as string),
						g: parseInt(rgbaMatch[2] as string),
						b: parseInt(rgbaMatch[3] as string),
						a: parseFloat(rgbaMatch[4] as string)
					}
				}
				return { r: 255, g: 0, b: 0, a: 1.0 }
			},

			// HSV转RGB
			hsvToRgb(h: number, s: number, v: number): RGBValues {
				const c: number = v * s
				const x: number = c * (1.0 - Math.abs(((h / 60.0) % 2.0) - 1.0))
				const m: number = v - c

				let r: number = 0.0
				let g: number = 0.0
				let b: number = 0.0

				if (h >= 0 && h < 60) {
					r = c
					g = x
					b = 0.0
				} else if (h >= 60 && h < 120) {
					r = x
					g = c
					b = 0.0
				} else if (h >= 120 && h < 180) {
					r = 0.0
					g = c
					b = x
				} else if (h >= 180 && h < 240) {
					r = 0.0
					g = x
					b = c
				} else if (h >= 240 && h < 300) {
					r = x
					g = 0.0
					b = c
				} else if (h >= 300 && h < 360) {
					r = c
					g = 0.0
					b = x
				}

				const result: RGBValues = {
					r: Math.round((r + m) * 255.0),
					g: Math.round((g + m) * 255.0),
					b: Math.round((b + m) * 255.0)
				}
				return result
			},

			// RGB转十六进制
			rgbToHex(r: number, g: number, b: number): string {
				const toHex = (value: number): string => {
					const hex = Math.round(Math.max(0, Math.min(255, value))).toString(16)
					return hex.length == 1 ? '0' + hex : hex
				}
				return '#' + toHex(r) + toHex(g) + toHex(b)
			},


		}
	}
</script>

<style>
	/* 弹窗遮罩层 */
	.picker-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: flex-end;
		z-index: 1000;
	}

	.picker-modal {
		width: 100%;
		background-color: #ffffff;
		border-top-left-radius: 20rpx;
		border-top-right-radius: 20rpx;
		overflow: hidden;
		box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
	}

	.color-picker-container {
		width: 100%;
		height: 100%;
		background-color: #ffffff;
		display: flex;
		flex-direction: column;
	}

	/* 导航栏样式 */
	.navbar {
		height: 44px;
		background-color: #f8f8f8;
		border-bottom: 1px solid #e5e5e5;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		padding: 0 10px;
	}

	.nav-btn {
		font-size: 16px;
		color: #007aff;
		padding: 8px 12px;
	}

	.cancel-btn {
		color: #999999;
	}

	.confirm-btn-container {
		height: 30px;
		background-color: #007aff;
		border-radius: 8rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);
	}

	.confirm-btn {
		color: #ffffff;
		font-weight: bold;
	}

	.nav-title {
		font-size: 17px;
		color: #333333;
	}

	/* 区域标题样式 */
	.section-title {
		font-size: 14px;
		color: #666666;
		margin-bottom: 10px;
	}

	/* 颜色系列选择区域 */
	.color-series-section {
		padding: 20rpx;
		border-bottom: 1px solid #f0f0f0;
	}

	.color-series-buttons {
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;
		justify-content: space-between;
		margin-top: 10rpx;
		padding: 0 10rpx;
	}

	.series-button {
		height: 60rpx;
		border-radius: 8rpx;
		border: 2px solid transparent;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 10rpx;
		box-sizing: border-box;
	}

	/* 随机色按钮：两个普通按钮宽度 + 间距 */
	.random-button {
		width: 220rpx;
	}

	/* 其他按钮正常宽度 */
	.normal-button {
		width: 100rpx;
	}

	.series-button.active {
		border-color: #007aff;
		box-shadow: 0 0 0 1px #007aff;
	}

	.series-text {
		font-size: 24rpx;
		color: #ffffff;
		font-weight: bold;
		text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
	}

	/* 颜色网格区域 */
	.color-grid-section {
		padding: 20rpx;
		border-bottom: 1px solid #f0f0f0;
	}

	.color-grid {
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;
		justify-content: space-around;
		align-items: flex-start;
		padding: 15rpx;
	}

	.color-item {
		width: 55rpx;
		height: 40rpx;
		border-radius: 4px;
		border: 2px solid transparent;
		margin-bottom: 4px;
		flex-shrink: 0;
		flex-grow: 0;
		box-sizing: border-box;
	}

	.color-item.selected {
		border-color: #007aff;
		box-shadow: 0 0 0 1px #007aff;
	}

	/* 预览和透明度选择区域 */
	.preview-opacity-section {
		padding: 15rpx 20rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1px solid #f0f0f0;
	}

	.preview-area {
		display: flex;
		flex-direction: row;
		align-items: center;
		flex: 1;
	}

	.preview-color {
		width: 60rpx;
		height: 60rpx;
		border-radius: 30rpx;
		border: 1px solid #e5e5e5;
		margin-right: 15rpx;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	}

	.rgba-text {
		font-size: 24rpx;
		color: #666666;
		font-family: monospace;
		background-color: #f5f5f5;
		padding: 8rpx 12rpx;
		border-radius: 6rpx;
	}

	.opacity-area {
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.opacity-label {
		font-size: 24rpx;
		color: #666666;
		margin-bottom: 8rpx;
	}

	.opacity-button {
		background-color: #007aff;
		padding: 12rpx 20rpx;
		border-radius: 8rpx;
		border: none;
	}

	.opacity-value {
		font-size: 26rpx;
		color: #ffffff;
		font-weight: bold;
	}

	/* RGB设置弹窗样式 */
	.rgb-modal-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 1000;
	}

	.rgb-modal {
		background-color: #ffffff;
		border-radius: 12rpx;
		width: 600rpx;
		max-height: 800rpx;
		padding: 30rpx;
		box-sizing: border-box;
	}

	.rgb-modal-header {
		display: flex;
		justify-content: center;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.rgb-modal-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
	}

	.rgb-preview-section {
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
		margin-bottom: 25rpx;
		padding: 15rpx;
		background-color: #f8f8f8;
		border-radius: 8rpx;
	}

	.rgb-preview-color {
		width: 60rpx;
		height: 60rpx;
		border-radius: 8rpx;
		border: 1px solid #e5e5e5;
		margin-right: 15rpx;
	}

	.rgb-preview-text {
		font-size: 24rpx;
		color: #666666;
		font-family: monospace;
	}

	.rgb-controls {
		margin-bottom: 25rpx;
	}

	.rgb-control-item {
		display: flex;
		flex-direction: row;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.rgb-label {
		width: 40rpx;
		font-size: 28rpx;
		font-weight: bold;
		color: #333333;
		text-align: center;
	}

	.rgb-slider {
		flex: 1;
		margin: 0 15rpx;
	}

	.rgb-input {
		width: 120rpx;
		height: 60rpx;
		border: 1px solid #e5e5e5;
		border-radius: 6rpx;
		text-align: center;
		font-size: 24rpx;
		padding: 0 10rpx;
		box-sizing: border-box;
	}

	.rgb-modal-buttons {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
	}

	.rgb-button {
		width: 45%;
		height: 70rpx;
		border-radius: 8rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.rgb-cancel {
		background-color: #f5f5f5;
		border: 1px solid #e5e5e5;
	}

	.rgb-confirm {
		background-color: #007aff;
	}

	.rgb-button-text {
		font-size: 28rpx;
		font-weight: bold;
	}

	.rgb-cancel .rgb-button-text {
		color: #666666;
	}

	.rgb-confirm .rgb-button-text {
		color: #ffffff;
	}
</style>