{"version": 3, "sources": ["components/main-form/components/form-numberbox.uvue", "components/main-form/components/form-input.uvue"], "sourcesContent": ["<template>\n\t<form-container :label=\"fieldName\" :show-error=\"showError\" :tip=\"tip\" :error-message=\"errorMessage\" :label-color=\"labelColor\"\n\t\t:background-color=\"backgroundColor\">\n\t\t<template #input-content>\n\t\t\t<view class=\"numberbox-container\">\n\t\t\t\t<view class=\"numberbox-btn qShadow1\" @click=\"numberFun('-')\">\n\t\t\t\t\t<text class=\"numberbox-btn-text\">-</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"numberbox-input-wrapper\">\n\t\t\t\t\t<input\n\t\t\t\t\t\tclass=\"numberbox-input\"\n\t\t\t\t\t\ttype=\"number\"\n\t\t\t\t\t\tv-model=\"inputValue\"\n\t\t\t\t\t\t@input=\"onInputChange\"\n\t\t\t\t\t\t@blur=\"onInputBlur\"\n\t\t\t\t\t/>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"numberbox-btn qShadow1\" @click=\"numberFun('+')\">\n\t\t\t\t\t<text class=\"numberbox-btn-text\">+</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"numberbox-unit\" v-if=\"unitText\">\n\t\t\t\t\t<text class=\"numberbox-unit-text\">{{ unitText }}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</template>\n\t</form-container>\n</template>\n\n<script lang=\"uts\">\n\timport { FormFieldData, FormChangeEvent } from '@/components/main-form/form_type.uts'\n\timport FormContainer from './form-container.uvue'\n\n\texport default {\n\t\tname: \"FormNumberbox\",\n\t\temits: ['change'],\n\t\tcomponents: {\n\t\t\tFormContainer\n\t\t},\n\t\tprops: {\n\t\t\tdata: {\n\t\t\t\ttype: null as any as PropType<FormFieldData>\n\t\t\t},\n\t\t\tindex: {\n\t\t\t\ttype: Number,\n\t\t\t\tdefault: 0\n\t\t\t},\n\t\t\tkeyName: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"\"\n\t\t\t},\n\t\t\tlabelColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#000\"\n\t\t\t},\n\t\t\tbackgroundColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#f1f4f9\"\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tfieldName: \"\",\n\t\t\t\tfieldValue: 0,\n\t\t\t\tisSave: false,\n\t\t\t\tsave_key: \"\",\n\t\t\t\ttip: \"\",\n\t\t\t\tminValue: 0,\n\t\t\t\tmaxValue: 100,\n\t\t\t\tstepValue: 1,\n\t\t\t\tinputValue: \"0\",\n\t\t\t\tunitText: \"\",\n\t\t\t\tshowError: false,\n\t\t\t\terrorMessage: \"\"\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\n\t\t},\n\t\twatch: {\n\t\t\tdata: {\n\t\t\t\thandler(obj: FormFieldData) {\n\t\t\t\t\t// 只处理value的变化，当外部传入的value与当前fieldValue不同时，才更新fieldValue\n\t\t\t\t\tconst newValue = obj.value as number\n\t\t\t\t\tif (newValue !== this.fieldValue) {\n\t\t\t\t\t\tthis.fieldValue = newValue\n\t\t\t\t\t\tthis.updateInputValue()\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tdeep: true\n\t\t\t}\n\t\t},\n\t\tcreated(): void {\n\t\t\t// 初始化时调用一次即可\n\t\t\tconst fieldObj = this.$props[\"data\"] as FormFieldData\n\t\t\tthis.initFieldData(fieldObj)\n\t\t},\n\t\tmethods: {\n\t\t\t// 初始化字段数据（仅在首次加载时调用）\n\t\t\tinitFieldData(fieldObj: FormFieldData): void {\n\t\t\t\tconst fieldKey = fieldObj.key\n\t\t\t\tconst fieldValue = fieldObj.value as number\n\n\t\t\t\t// 设置基本信息\n\t\t\t\tthis.fieldName = fieldObj.name\n\t\t\t\tthis.fieldValue = fieldValue\n\t\t\t\tthis.isSave = fieldObj.isSave ?? false\n\t\t\t\tthis.save_key = this.keyName + \"_\" + fieldKey\n\n\t\t\t\t// 解析配置信息\n\t\t\t\tconst extalJson = fieldObj.extra as UTSJSONObject\n\t\t\t\tthis.minValue = extalJson.getNumber(\"min\") ?? 0\n\t\t\t\tthis.maxValue = extalJson.getNumber(\"max\") ?? 100\n\t\t\t\tthis.stepValue = extalJson.getNumber(\"step\") ?? 1\n\t\t\t\tthis.tip = extalJson.getString(\"tip\") ?? \"\"\n\t\t\t\tthis.unitText = extalJson.getString(\"unit\") ?? \"\"\n\n\t\t\t\t// 更新输入框的值\n\t\t\t\tthis.updateInputValue()\n\n\t\t\t\t// 获取缓存\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.getCache()\n\t\t\t\t}, 500)\n\t\t\t},\n\n\t\t\t// 更新输入框的值\n\t\t\tupdateInputValue(): void {\n\t\t\t\tthis.inputValue = this.fieldValue.toString()\n\t\t\t},\n\n\t\t\t// 验证值是否在有效范围内\n\t\t\tvalidateValue(value: number): number {\n\t\t\t\tif (value < this.minValue) {\n\t\t\t\t\treturn this.minValue\n\t\t\t\t}\n\t\t\t\tif (value > this.maxValue) {\n\t\t\t\t\treturn this.maxValue\n\t\t\t\t}\n\n\t\t\t\t// 处理步长\n\t\t\t\tif (this.stepValue % 1 == 0) {\n\t\t\t\t\t// 整数步长\n\t\t\t\t\treturn Math.round(value)\n\t\t\t\t} else {\n\t\t\t\t\t// 小数步长，保留一位小数\n\t\t\t\t\treturn Number.from(value.toFixed(1))\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tgetCache(): void {\n\t\t\t\tif (this.isSave) {\n\t\t\t\t\tconst that = this\n\t\t\t\t\tuni.getStorage({\n\t\t\t\t\t\tkey: this.save_key,\n\t\t\t\t\t\tsuccess: (res: GetStorageSuccess) => {\n\t\t\t\t\t\t\tconst save_value = res.data as number\n\t\t\t\t\t\t\tconst validatedValue = that.validateValue(save_value)\n\t\t\t\t\t\t\tthat.fieldValue = validatedValue\n\t\t\t\t\t\t\tthat.updateInputValue()\n\t\t\t\t\t\t\tconst result: FormChangeEvent = {\n\t\t\t\t\t\t\t\tindex: this.index,\n\t\t\t\t\t\t\t\tvalue: validatedValue\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tthis.change(result)\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tsetCache(): void {\n\t\t\t\tif (this.isSave) {\n\t\t\t\t\tuni.setStorage({\n\t\t\t\t\t\tkey: this.save_key,\n\t\t\t\t\t\tdata: this.fieldValue\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tvalidate(): boolean {\n\t\t\t\t// 数字输入框组件通常不需要额外验证，因为值已经被限制在min-max范围内\n\t\t\t\tthis.showError = false\n\t\t\t\tthis.errorMessage = \"\"\n\t\t\t\treturn true\n\t\t\t},\n\n\t\t\tchange(event: FormChangeEvent): void {\n\t\t\t\t// 更新字段值\n\t\t\t\tthis.fieldValue = event.value as number\n\t\t\t\t// 保存缓存\n\t\t\t\tthis.setCache()\n\t\t\t\t// 触发父组件事件\n\t\t\t\tthis.$emit('change', event)\n\t\t\t},\n\n\t\t\t// 按钮点击事件\n\t\t\tnumberFun(type: string): void {\n\t\t\t\tlet value: number\n\t\t\t\tlet num: number = this.fieldValue\n\n\t\t\t\tif (type == \"+\") {\n\t\t\t\t\tnum = num + this.stepValue\n\t\t\t\t} else {\n\t\t\t\t\tnum = num - this.stepValue\n\t\t\t\t}\n\n\t\t\t\t// 验证并限制值\n\t\t\t\tvalue = this.validateValue(num)\n\n\t\t\t\t// 更新输入框显示\n\t\t\t\tthis.inputValue = value.toString()\n\n\t\t\t\tconst result: FormChangeEvent = {\n\t\t\t\t\tindex: this.index,\n\t\t\t\t\tvalue: value\n\t\t\t\t}\n\t\t\t\tthis.change(result)\n\t\t\t},\n\n\t\t\tonInputChange(event: UniInputEvent): void {\n\t\t\t\tconst inputStr = event.detail.value as string\n\t\t\t\tconst inputNum = parseFloat(inputStr)\n\n\t\t\t\tif (!isNaN(inputNum)) {\n\t\t\t\t\tconst validatedValue = this.validateValue(inputNum)\n\n\t\t\t\t\tconst result: FormChangeEvent = {\n\t\t\t\t\t\tindex: this.index,\n\t\t\t\t\t\tvalue: validatedValue\n\t\t\t\t\t}\n\t\t\t\t\tthis.change(result)\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tonInputBlur(): void {\n\t\t\t\t// 在失去焦点时进行验证和格式化\n\t\t\t\tconst inputNum = parseFloat(this.inputValue)\n\t\t\t\tif (isNaN(inputNum)) {\n\t\t\t\t\t// 如果输入无效，恢复到当前字段值\n\t\t\t\t\tthis.inputValue = this.fieldValue.toString()\n\t\t\t\t} else {\n\t\t\t\t\t// 验证并格式化输入值\n\t\t\t\t\tconst validatedValue = this.validateValue(inputNum)\n\t\t\t\t\tthis.inputValue = validatedValue.toString()\n\n\t\t\t\t\tif (validatedValue !== this.fieldValue) {\n\t\t\t\t\t\tconst result: FormChangeEvent = {\n\t\t\t\t\t\t\tindex: this.index,\n\t\t\t\t\t\t\tvalue: validatedValue\n\t\t\t\t\t\t}\n\t\t\t\t\t\tthis.change(result)\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthis.validate()\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t.numberbox-container {\n\t\twidth: 100%;\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t}\n\n\t.numberbox-btn {\n\t\twidth: 80rpx;\n\t\theight: 80rpx;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tbackground-color: #e1e1e1;\n\t\tborder-radius: 10rpx;\n\t}\n\n\t.numberbox-btn-text {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t}\n\n\t.numberbox-input-wrapper {\n\t\tflex: 1;\n\t\theight: 80rpx;\n\t\tbackground-color: #f2f4f8;\n\t\tmargin: 0 20rpx;\n\t\tborder-radius: 10rpx;\n\t}\n\n\t.numberbox-input {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\ttext-align: center;\n\t\tfont-size: 32rpx;\n\t\tborder: none;\n\t\tbackground-color: transparent;\n\t}\n\n\t.numberbox-unit {\n\t\tpadding: 0 10rpx;\n\t\theight: 80rpx;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tbackground-color: #333;\n\t\tborder-radius: 10rpx;\n\t\tmargin-left: 20rpx;\n\t}\n\n\t.numberbox-unit-text {\n\t\tcolor: #fff;\n\t\tfont-size: 28rpx;\n\t}\n\n\t/* 阴影样式 */\n\t.qShadow1 {\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n\t}\n</style>", null], "names": [], "mappings": ";;;;;;;;;;;;;+BAyHS;+BAmBG;AA5GN;;kBA2DJ,OAAW,IAAG,CAAA;YAEb,IAAM,WAAW,IAAI,CAAC,QAAM,CAAC,OAAM,CAAA,EAAA;YACnC,IAAI,CAAC,aAAa,CAAC;QACpB;;;;;UAfE,IAAQ,kBAAkB,EAAA;YAEzB,IAAM,WAAW,IAAI,KAAI,CAAA,EAAA,CAAK,MAAK;YACnC,IAAI,aAAa,IAAI,CAAC,UAAU,EAAE;gBACjC,IAAI,CAAC,UAAS,GAAI;gBAClB,IAAI,CAAC,gBAAgB;;QAEvB;uBACA,OAAM,IAAG;;;;;;;eAvFZ,IAwBiB,2BAAA,IAxBA,WAAO,KAAA,SAAS,EAAG,gBAAY,KAAA,SAAS,EAAG,SAAK,KAAA,GAAG,EAAG,mBAAe,KAAA,YAAY,EAAG,iBAAa,KAAA,UAAU,EAC1H,sBAAkB,KAAA,eAAe,OACvB,mBAAa,YACvB,gBAmBO,GAAA;mBAAA;gBAnBP,IAmBO,QAAA,IAnBD,WAAM,wBAAqB;oBAChC,IAEO,QAAA,IAFD,WAAM,0BAA0B,aAAK,KAAA;wBAAE,KAAA,SAAS,CAAA;oBAAA;;wBACrD,IAAyC,QAAA,IAAnC,WAAM,uBAAqB;;;;oBAElC,IAQO,QAAA,IARD,WAAM,4BAAyB;wBACpC,IAME,SAAA,IALD,WAAM,mBACN,UAAK,0BACI,KAAA,UAAU;;gCAAV,KAAA,UAAU,GAAA,SAAA,MAAA,CAAA,KAAA;4BAAA;;4BACX,KAAA,aAAa;yBAAA,EACpB,YAAM,KAAA,WAAW;;;;;;oBAGpB,IAEO,QAAA,IAFD,WAAM,0BAA0B,aAAK,KAAA;wBAAE,KAAA,SAAS,CAAA;oBAAA;;wBACrD,IAAyC,QAAA,IAAnC,WAAM,uBAAqB;;;;+BAEC,KAAA,QAAQ;wBAA3C,IAEO,QAAA,gBAFD,WAAM;4BACX,IAAuD,QAAA,IAAjD,WAAM,wBAAqB,IAAI,KAAA,QAAQ,GAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;aAwC9C;aACA;aACA;aACA;aACA;aACA;aACA;aACA;aACA;aACA;aACA;aACA;;;mBAXA,eAAW,IACX,gBAAY,CAAC,EACb,YAAQ,KAAK,EACb,cAAU,IACV,SAAK,IACL,cAAU,CAAC,EACX,cAAU,GAAG,EACb,eAAW,CAAC,EACZ,gBAAY,KACZ,cAAU,IACV,eAAW,KAAK,EAChB,kBAAc;;aA0Bf;aAAA,qBAAc,uBAAuB,GAAG,IAAG,CAAA;QAC1C,IAAM,WAAW,SAAS,GAAE;QAC5B,IAAM,aAAa,SAAS,KAAI,CAAA,EAAA,CAAK,MAAK;QAG1C,IAAI,CAAC,SAAQ,GAAI,SAAS,IAAG;QAC7B,IAAI,CAAC,UAAS,GAAI;QAClB,IAAI,CAAC,MAAK,GAAI,SAAS,MAAK,IAAK,KAAI;QACrC,IAAI,CAAC,QAAO,GAAI,IAAI,CAAC,OAAM,GAAI,MAAM;QAGrC,IAAM,YAAY,SAAS,KAAI,CAAA,EAAA,CAAK;QACpC,IAAI,CAAC,QAAO,GAAI,UAAU,SAAS,CAAC,UAAU,CAAA;QAC9C,IAAI,CAAC,QAAO,GAAI,UAAU,SAAS,CAAC,UAAU,GAAE;QAChD,IAAI,CAAC,SAAQ,GAAI,UAAU,SAAS,CAAC,WAAW,CAAA;QAChD,IAAI,CAAC,GAAE,GAAI,UAAU,SAAS,CAAC,UAAU;QACzC,IAAI,CAAC,QAAO,GAAI,UAAU,SAAS,CAAC,WAAW;QAG/C,IAAI,CAAC,gBAAgB;QAGrB,WAAW,KAAI;YACd,IAAI,CAAC,QAAQ;QACd;UAAG,GAAG;IACP;aAGA;aAAA,2BAAoB,IAAG,CAAA;QACtB,IAAI,CAAC,UAAS,GAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAA,EAAA;IAC3C;aAGA;aAAA,qBAAc,OAAO,MAAM,GAAG,MAAK,CAAA;QAClC,IAAI,QAAQ,IAAI,CAAC,QAAQ,EAAE;YAC1B,OAAO,IAAI,CAAC,QAAO;;QAEpB,IAAI,QAAQ,IAAI,CAAC,QAAQ,EAAE;YAC1B,OAAO,IAAI,CAAC,QAAO;;QAIpB,IAAI,IAAI,CAAC,SAAQ,GAAI,CAAA,IAAK,CAAC,EAAE;YAE5B,OAAO,KAAK,KAAK,CAAC;eACZ;YAEN,OAAO,UAAO,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC;;IAEpC;aAEA;aAAA,mBAAY,IAAG,CAAA;QACd,IAAI,IAAI,CAAC,MAAM,EAAE;YAChB,IAAM,OAAO,IAAG;YAChB,iCACC,MAAK,IAAI,CAAC,QAAQ,EAClB,UAAS,IAAC,KAAK,kBAAoB;gBAClC,IAAM,aAAa,IAAI,IAAG,CAAA,EAAA,CAAK,MAAK;gBACpC,IAAM,iBAAiB,KAAK,aAAa,CAAC;gBAC1C,KAAK,UAAS,GAAI;gBAClB,KAAK,gBAAgB;gBACrB,IAAM,yBACL,QAAO,IAAI,CAAC,KAAK,EACjB,QAAO;gBAER,IAAI,CAAC,MAAM,CAAC;YACb;;;IAGH;aAEA;aAAA,mBAAY,IAAG,CAAA;QACd,IAAI,IAAI,CAAC,MAAM,EAAE;YAChB,iCACC,MAAK,IAAI,CAAC,QAAQ,EAClB,OAAM,IAAI,CAAC,UAAS;;IAGvB;aAEA;aAAA,mBAAY,OAAM,CAAA;QAEjB,IAAI,CAAC,SAAQ,GAAI,KAAI;QACrB,IAAI,CAAC,YAAW,GAAI;QACpB,OAAO,IAAG;IACX;aAEA;aAAA,cAAO,sBAAsB,GAAG,IAAG,CAAA;QAElC,IAAI,CAAC,UAAS,GAAI,MAAM,KAAI,CAAA,EAAA,CAAK,MAAK;QAEtC,IAAI,CAAC,QAAQ;QAEb,IAAI,CAAC,OAAK,CAAC,UAAU;IACtB;aAGA;aAAA,iBAAU,MAAM,MAAM,GAAG,IAAG,CAAA;QAC3B,IAAI,OAAO,MAAK;QAChB,IAAI,KAAK,MAAK,GAAI,IAAI,CAAC,UAAS;QAEhC,IAAI,QAAQ,KAAK;YAChB,MAAM,MAAM,IAAI,CAAC,SAAQ;eACnB;YACN,MAAM,MAAM,IAAI,CAAC,SAAQ;;QAI1B,QAAQ,IAAI,CAAC,aAAa,CAAC;QAG3B,IAAI,CAAC,UAAS,GAAI,MAAM,QAAQ,CAAA,EAAA;QAEhC,IAAM,yBACL,QAAO,IAAI,CAAC,KAAK,EACjB,QAAO;QAER,IAAI,CAAC,MAAM,CAAC;IACb;aAEA;aAAA,qBAAc,OAAO,aAAa,GAAG,IAAG,CAAA;QACvC,IAAM,WAAW,MAAM,MAAM,CAAC,KAAI,CAAA,EAAA,CAAK,MAAK;QAC5C,IAAM,WAAW,WAAW;QAE5B,IAAI,CAAC,MAAM,WAAW;YACrB,IAAM,iBAAiB,IAAI,CAAC,aAAa,CAAC;YAE1C,IAAM,yBACL,QAAO,IAAI,CAAC,KAAK,EACjB,QAAO;YAER,IAAI,CAAC,MAAM,CAAC;;IAEd;aAEA;aAAA,sBAAe,IAAG,CAAA;QAEjB,IAAM,WAAW,WAAW,IAAI,CAAC,UAAU;QAC3C,IAAI,MAAM,WAAW;YAEpB,IAAI,CAAC,UAAS,GAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAA,EAAA;eACpC;YAEN,IAAM,iBAAiB,IAAI,CAAC,aAAa,CAAC;YAC1C,IAAI,CAAC,UAAS,GAAI,eAAe,QAAQ,CAAA,EAAA;YAEzC,IAAI,mBAAmB,IAAI,CAAC,UAAU,EAAE;gBACvC,IAAM,yBACL,QAAO,IAAI,CAAC,KAAK,EACjB,QAAO;gBAER,IAAI,CAAC,MAAM,CAAC;;;QAGd,IAAI,CAAC,QAAQ;IACd;;mBA5NK;;;;;;;;;;;;;6FAWK,CAAA,qDAIA,0DAIA,mEAIA;;;;;;;;;AAuMZ"}