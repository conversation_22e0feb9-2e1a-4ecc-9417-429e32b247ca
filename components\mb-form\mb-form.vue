<template>
	<view class="mb-form" :style="{marginTop:mt}">


		<view class="mb-title" v-if="title!=''" :style="{color:color}">
			{{title}}
		</view>

		<view class="mb-item" v-for="(item,index) in data">

			<Minput :data="item" :color="color" :keyName="keyName" :index="index" v-if="item.type=='input' &&check(item)" @change="change"></Minput>

			<Mslider :data="item" :color="color" :keyName="keyName" :index="index" v-if="item.type=='slider' &&check(item)" @change="change"></Mslider>

			<Mnumberbox :data="item" :color="color" :keyName="keyName" :index="index" v-if="item.type=='numberbox' &&check(item)" @change="change"> </Mnumberbox>
			<Mswitch :data="item" :color="color" :keyName="keyName" :index="index" v-if="item.type=='switch' &&check(item)" @change="change"> </Mswitch>
			<Mselect :data="item" :color="color" :keyName="keyName" :index="index" v-if="item.type=='select'&&check(item)" @change="change"> </Mselect>
			<Mtextarea :data="item" :color="color" :keyName="keyName" :index="index" v-if="item.type=='textarea'&&check(item)" @change="change"> </Mtextarea>
			<Mcolor :data="item" :color="color" :keyName="keyName" :index="index" v-if="item.type=='color'&&check(item)" @change="change"> </Mcolor>
			<Mimage :data="item" :color="color" :keyName="keyName" :index="index" v-if="item.type=='image'&&check(item)" @change="change"> </Mimage>
			<Mdate :data="item" :color="color" :keyName="keyName" :index="index" v-if="item.type=='date'&&check(item)" @change="change"> </Mdate>
			<Mtime :data="item" :color="color" :keyName="keyName" :index="index" v-if="item.type=='time'&&check(item)" @change="change"> </Mtime>
			<Micon :data="item" :color="color" :keyName="keyName" :index="index" v-if="item.type=='icon'&&check(item)" @change="change"> </Micon>
			<Mfont :data="item" :color="color" :keyName="keyName" :index="index" v-if="item.type=='font'&&check(item)" @change="change"> </Mfont>
			<Mvideo :data="item" :color="color" :keyName="keyName" :index="index" v-if="item.type=='video'&&check(item)" @change="change"> </Mvideo>
	
		</view>


	</view>
</template>

<script>
	import Minput from "./Minput"
	import Mslider from "./Mslider"
	import Mnumberbox from "./Mnumberbox"
	import Mswitch from "./Mswitch"
	import Mselect from "./Mselect"
	import Mtextarea from "./Mtextarea"
	import Mcolor from "./Mcolor"
	import Mimage from "./Mimage"
	import Mdate from "./Mdate.vue"
	import Mtime from "./Mtime.vue"
	import Micon from "./Micon.vue"
	import Mfont from "./Mfont.vue"
	import Mvideo from "./Mvideo.vue"
	
	const getVal = (val) => {
		const reg = /^[0-9]*$/g
		return (typeof val === 'number' || reg.test(val)) ? val + 'px' : val;
	}
	export default {
		components: {
			Minput,
			Mslider,
			Mnumberbox,
			Mswitch,
			Mselect,
			Mtextarea,
			Mcolor,
			Mimage,
			Mdate,
			Mtime,
			Micon,
			Mfont,
			Mvideo
		},
		name: "mb-form",
		data() {
			return {
				color:"#1F6ED4"
			};
		},
		props: {
			data: {
				type: Array,
				default () {
					return []
				}
			},
			keyName: {
				type: String,
				default: ""

			},
			bgColor: {
				type: String,
				default: "#f1f4f9"

			},
			title: {
				type: String,
				default: ""

			},
			marginTop: {
				type: [Number, String],
				default: 0

			}

		},
		computed: {
			mt() {
				return getVal(this.marginTop)
			}

		},
		methods: {
			check(e){
				if(e.condition){
					
					let condition=e.condition.split(":")
					
					let key=condition[0]
					let values=condition[1].split(",")

					const objectWithMode = this.data.find(obj => obj.key === key);
					const modeValue = objectWithMode ? objectWithMode.value : null;
					if(modeValue!=null && values.indexOf(String(modeValue))!=-1){
						return true
						
					}else{
						false
					}
					
					
					
				}else{
					return true
				}
				
				
				
			},
			change(e) {
				this.data[e.index]["value"] = e.value
				
				const data=this.data[e.index]
				
				this.$emit("change",{
					key:data.key,
					value:e.value
				})
			}
		}
	}
</script>

<style>
	.mb-form {
		width: 100%;
		display: flex;
		flex-direction: column;
	}

	.mb-title {
		width: 710rpx;
		margin: 0 auto;
		padding: 0 20rpx;
		font-size: 35rpx;
		font-weight: 700;
		margin-bottom: 20rpx;
		border-left: 10rpx solid #1F6ED4;

	}

	.mb-item {
		width: 100%;
	}
</style>