@file:Suppress("UNCHECKED_CAST", "USELESS_CAST", "INAPPLICABLE_JVM_NAME", "UNUSED_ANONYMOUS_PARAMETER", "NAME_SHADOWING", "UNNECESSARY_NOT_NULL_ASSERTION")
package uni.UNIC178CB1
import io.dcloud.uniapp.*
import io.dcloud.uniapp.extapi.*
import io.dcloud.uniapp.framework.*
import io.dcloud.uniapp.runtime.*
import io.dcloud.uniapp.vue.*
import io.dcloud.uniapp.vue.shared.*
import io.dcloud.unicloud.*
import io.dcloud.uts.*
import io.dcloud.uts.Map
import io.dcloud.uts.Set
import io.dcloud.uts.UTSAndroid
import io.dcloud.uniapp.extapi.showToast as uni_showToast
open class GenComponentsMainCalendarPicker : VueComponent {
    constructor(__ins: ComponentInternalInstance) : super(__ins) {
        onCreated(fun() {
            this.initializeData()
        }
        , __ins)
    }
    @Suppress("UNUSED_PARAMETER", "UNUSED_VARIABLE")
    override fun `$render`(): Any? {
        val _ctx = this
        val _cache = this.`$`.renderCache
        val _component_main_yearmonth_picker = resolveComponent("main-yearmonth-picker")
        return _cE(Fragment, null, _uA(
            if (isTrue(_ctx.visible)) {
                _cE("view", _uM("key" to 0, "class" to "picker-overlay", "onClick" to _ctx.onOverlayClick), _uA(
                    _cE("view", _uM("class" to "picker-modal", "onClick" to withModifiers(fun(){}, _uA(
                        "stop"
                    ))), _uA(
                        _cE("view", _uM("class" to "calendar-picker-container"), _uA(
                            _cE("view", _uM("class" to "navbar"), _uA(
                                _cE("text", _uM("class" to "nav-btn cancel-btn", "onClick" to _ctx.onCancel), "取消", 8, _uA(
                                    "onClick"
                                )),
                                _cE("text", _uM("class" to "nav-title"), "选择日期"),
                                _cE("view", _uM("class" to "confirm-btn-container"), _uA(
                                    _cE("text", _uM("class" to "nav-btn confirm-btn", "onClick" to _ctx.onConfirm), "确定", 8, _uA(
                                        "onClick"
                                    ))
                                ))
                            )),
                            _cE("view", _uM("class" to "calendar-header"), _uA(
                                _cE("view", _uM("class" to "month-nav-btn", "onClick" to _ctx.prevMonth), _uA(
                                    _cE("text", _uM("class" to "nav-arrow"), "‹")
                                ), 8, _uA(
                                    "onClick"
                                )),
                                _cE("view", _uM("class" to "year-month-display", "onClick" to _ctx.openYearMonthPicker), _uA(
                                    _cE("text", _uM("class" to "year-month-text"), _tD(_ctx.currentYearMonth), 1)
                                ), 8, _uA(
                                    "onClick"
                                )),
                                _cE("view", _uM("class" to "month-nav-btn", "onClick" to _ctx.nextMonth), _uA(
                                    _cE("text", _uM("class" to "nav-arrow"), "›")
                                ), 8, _uA(
                                    "onClick"
                                ))
                            )),
                            _cE("view", _uM("class" to "week-header"), _uA(
                                _cE(Fragment, null, RenderHelpers.renderList(_ctx.weekDays, fun(day, index, __index, _cached): Any {
                                    return _cE("view", _uM("key" to index, "class" to "week-day"), _uA(
                                        _cE("text", _uM("class" to "week-day-text"), _tD(day), 1)
                                    ))
                                }), 128)
                            )),
                            _cE("view", _uM("class" to "calendar-grid"), _uA(
                                _cE(Fragment, null, RenderHelpers.renderList(_ctx.weeks, fun(week, weekIndex, __index, _cached): Any {
                                    return _cE("view", _uM("key" to weekIndex, "class" to "calendar-week"), _uA(
                                        _cE(Fragment, null, RenderHelpers.renderList(week, fun(day, dayIndex, __index, _cached): Any {
                                            return _cE("view", _uM("key" to dayIndex, "class" to _nC(_uA(
                                                "calendar-day",
                                                _uM("day-disabled" to day.disabled, "day-today" to day.is_today, "day-selected" to _ctx.isSelectedDay(day))
                                            )), "onClick" to fun(){
                                                _ctx.onDaySelect(day)
                                            }), _uA(
                                                _cE("text", _uM("class" to _nC(_uA(
                                                    "day-number",
                                                    _uM("day-number-disabled" to day.disabled, "day-number-today" to day.is_today, "day-number-selected" to _ctx.isSelectedDay(day))
                                                ))), _tD(day.date), 3),
                                                _cE("text", _uM("class" to _nC(_uA(
                                                    "day-lunar",
                                                    _uM("day-lunar-disabled" to day.disabled, "day-lunar-today" to day.is_today, "day-lunar-selected" to _ctx.isSelectedDay(day))
                                                ))), _tD(day.lunar), 3)
                                            ), 10, _uA(
                                                "onClick"
                                            ))
                                        }), 128)
                                    ))
                                }), 128)
                            )),
                            _cE("view", _uM("class" to "current-selection"), _uA(
                                _cE("text", _uM("class" to "selection-label"), "当前选择："),
                                _cE("text", _uM("class" to "selection-value"), _tD(_ctx.selectedDateText), 1)
                            ))
                        ))
                    ), 8, _uA(
                        "onClick"
                    ))
                ), 8, _uA(
                    "onClick"
                ))
            } else {
                _cC("v-if", true)
            }
            ,
            _cV(_component_main_yearmonth_picker, _uM("ref" to "yearmonthPicker", "initial-year" to _ctx.currentYear, "initial-month" to _ctx.currentMonth, "onConfirm" to _ctx.onYearMonthConfirm, "onCancel" to _ctx.onYearMonthCancel), null, 8, _uA(
                "initial-year",
                "initial-month",
                "onConfirm",
                "onCancel"
            ))
        ), 64)
    }
    open var initialDate: String by `$props`
    open var visible: Boolean by `$data`
    open var calendar: Calendar by `$data`
    open var currentYear: Number by `$data`
    open var currentMonth: Number by `$data`
    open var selectedDate: String by `$data`
    open var weeks: UTSArray<UTSArray<DateType>> by `$data`
    open var weekDays: UTSArray<String> by `$data`
    open var currentYearMonth: String by `$data`
    open var selectedDateText: String by `$data`
    @Suppress("USELESS_CAST")
    override fun data(): Map<String, Any?> {
        return _uM("visible" to false as Boolean, "calendar" to Calendar() as Calendar, "currentYear" to Date().getFullYear() as Number, "currentMonth" to (Date().getMonth() + 1) as Number, "selectedDate" to "" as String, "weeks" to _uA<UTSArray<DateType>>(), "weekDays" to _uA<String>("一", "二", "三", "四", "五", "六", "日"), "currentYearMonth" to computed<String>(fun(): String {
            return this.calendar.formatYearMonth(this.currentYear, this.currentMonth)
        }
        ), "selectedDateText" to computed<String>(fun(): String {
            if (this.selectedDate === "") {
                return "未选择"
            }
            val parts = this.selectedDate.split("-")
            if (parts.length == 3) {
                return "" + parts[0] + "\u5E74" + parseInt(parts[1]) + "\u6708" + parseInt(parts[2]) + "\u65E5"
            }
            return this.selectedDate
        }
        ))
    }
    open var initializeData = ::gen_initializeData_fn
    open fun gen_initializeData_fn() {
        val dateParts = this.initialDate.split("-")
        if (dateParts.length == 3) {
            this.currentYear = parseInt(dateParts[0])
            this.currentMonth = parseInt(dateParts[1])
            this.selectedDate = this.initialDate
        }
        this.updateCalendar()
    }
    open var updateCalendar = ::gen_updateCalendar_fn
    open fun gen_updateCalendar_fn() {
        val dateStr = "" + this.currentYear + "-" + this.currentMonth + "-1"
        this.weeks = this.calendar.getWeeks(dateStr)
    }
    open var isSelectedDay = ::gen_isSelectedDay_fn
    open fun gen_isSelectedDay_fn(day: DateType): Boolean {
        if (this.selectedDate === "" || day.disabled) {
            return false
        }
        return day.fullDate === this.selectedDate
    }
    open var onDaySelect = ::gen_onDaySelect_fn
    open fun gen_onDaySelect_fn(day: DateType) {
        if (day.disabled) {
            return
        }
        this.selectedDate = day.fullDate
    }
    open var prevMonth = ::gen_prevMonth_fn
    open fun gen_prevMonth_fn() {
        if (this.currentMonth == 1) {
            this.currentYear--
            this.currentMonth = 12
        } else {
            this.currentMonth--
        }
        this.updateCalendar()
    }
    open var nextMonth = ::gen_nextMonth_fn
    open fun gen_nextMonth_fn() {
        if (this.currentMonth == 12) {
            this.currentYear++
            this.currentMonth = 1
        } else {
            this.currentMonth++
        }
        this.updateCalendar()
    }
    open var openYearMonthPicker = ::gen_openYearMonthPicker_fn
    open fun gen_openYearMonthPicker_fn() {
        val yearmonthPicker = this.`$refs`["yearmonthPicker"] as ComponentPublicInstance
        yearmonthPicker.`$callMethod`("open")
    }
    open var onYearMonthConfirm = ::gen_onYearMonthConfirm_fn
    open fun gen_onYearMonthConfirm_fn(yearMonthData: UTSJSONObject) {
        val year = yearMonthData.getNumber("year")
        val month = yearMonthData.getNumber("month")
        if (year != null && month != null) {
            this.currentYear = year
            this.currentMonth = month
            this.updateCalendar()
        }
    }
    open var onYearMonthCancel = ::gen_onYearMonthCancel_fn
    open fun gen_onYearMonthCancel_fn() {}
    open var open = ::gen_open_fn
    open fun gen_open_fn() {
        this.visible = true
    }
    open var close = ::gen_close_fn
    open fun gen_close_fn() {
        this.visible = false
    }
    open var onOverlayClick = ::gen_onOverlayClick_fn
    open fun gen_onOverlayClick_fn() {
        this.close()
        this.`$emit`("cancel")
    }
    open var onCancel = ::gen_onCancel_fn
    open fun gen_onCancel_fn() {
        this.close()
        this.`$emit`("cancel")
    }
    open var onConfirm = ::gen_onConfirm_fn
    open fun gen_onConfirm_fn() {
        if (this.selectedDate === "") {
            uni_showToast(ShowToastOptions(title = "请选择日期", icon = "none"))
            return
        }
        var selectedDateObj: DateType? = null
        for(week in resolveUTSValueIterator(this.weeks)){
            for(day in resolveUTSValueIterator(week)){
                if (day.fullDate === this.selectedDate) {
                    selectedDateObj = day as DateType
                    break
                }
            }
            if (selectedDateObj != null) {
                break
            }
        }
        this.close()
        if (selectedDateObj != null) {
            var utsJson = UTSAndroid.consoleDebugError(JSON.parseObject(JSON.stringify(selectedDateObj)!!), " at components/main-calendar-picker.uvue:275")
            this.`$emit`("confirm", utsJson)
        } else {
            val today = Date()
            val todayStr = "" + today.getFullYear() + "-" + (today.getMonth() + 1) + "-" + today.getDate()
            val todayDateObj = this.calendar.getDate(todayStr)
            var utsJson = UTSAndroid.consoleDebugError(JSON.parseObject(JSON.stringify(todayDateObj)!!), " at components/main-calendar-picker.uvue:282")
            this.`$emit`("confirm", utsJson)
        }
    }
    companion object {
        var name = "main-calendar-picker"
        val styles: Map<String, Map<String, Map<String, Any>>> by lazy {
            _nCS(_uA(
                styles0
            ))
        }
        val styles0: Map<String, Map<String, Map<String, Any>>>
            get() {
                return _uM("picker-overlay" to _pS(_uM("position" to "fixed", "top" to 0, "left" to 0, "right" to 0, "bottom" to 0, "backgroundColor" to "rgba(0,0,0,0.5)", "display" to "flex", "alignItems" to "center", "justifyContent" to "center", "zIndex" to 1000)), "picker-modal" to _pS(_uM("width" to "90%", "maxWidth" to "700rpx", "backgroundColor" to "#ffffff", "borderTopLeftRadius" to "20rpx", "borderTopRightRadius" to "20rpx", "borderBottomRightRadius" to "20rpx", "borderBottomLeftRadius" to "20rpx", "overflow" to "hidden", "boxShadow" to "0 8px 32px rgba(0, 0, 0, 0.3)")), "calendar-picker-container" to _pS(_uM("width" to "100%", "backgroundColor" to "#ffffff", "display" to "flex", "flexDirection" to "column")), "navbar" to _pS(_uM("height" to 44, "backgroundColor" to "#f8f8f8", "borderBottomWidth" to 1, "borderBottomStyle" to "solid", "borderBottomColor" to "#e5e5e5", "display" to "flex", "flexDirection" to "row", "alignItems" to "center", "justifyContent" to "space-between", "paddingTop" to 0, "paddingRight" to 10, "paddingBottom" to 0, "paddingLeft" to 10)), "nav-btn" to _pS(_uM("fontSize" to 16, "color" to "#007aff", "paddingTop" to 8, "paddingRight" to 12, "paddingBottom" to 8, "paddingLeft" to 12)), "cancel-btn" to _pS(_uM("color" to "#999999")), "confirm-btn-container" to _pS(_uM("height" to 30, "backgroundColor" to "#007aff", "borderTopLeftRadius" to "8rpx", "borderTopRightRadius" to "8rpx", "borderBottomRightRadius" to "8rpx", "borderBottomLeftRadius" to "8rpx", "display" to "flex", "justifyContent" to "center", "alignItems" to "center", "boxShadow" to "0 2rpx 8rpx rgba(0, 122, 255, 0.3)")), "confirm-btn" to _pS(_uM("color" to "#ffffff", "fontWeight" to "bold")), "nav-title" to _pS(_uM("fontSize" to 17, "color" to "#333333")), "calendar-header" to _pS(_uM("height" to "80rpx", "backgroundColor" to "#f8f9fa", "borderBottomWidth" to 1, "borderBottomStyle" to "solid", "borderBottomColor" to "#e5e5e5", "display" to "flex", "flexDirection" to "row", "alignItems" to "center", "justifyContent" to "space-between", "paddingTop" to 0, "paddingRight" to "20rpx", "paddingBottom" to 0, "paddingLeft" to "20rpx")), "month-nav-btn" to _pS(_uM("width" to "60rpx", "height" to "60rpx", "backgroundColor" to "rgba(0,122,255,0.1)", "borderTopLeftRadius" to "30rpx", "borderTopRightRadius" to "30rpx", "borderBottomRightRadius" to "30rpx", "borderBottomLeftRadius" to "30rpx", "display" to "flex", "justifyContent" to "center", "alignItems" to "center")), "nav-arrow" to _pS(_uM("fontSize" to "32rpx", "color" to "#007aff", "fontWeight" to "bold")), "year-month-display" to _pS(_uM("flex" to 1, "display" to "flex", "justifyContent" to "center", "alignItems" to "center", "paddingTop" to 0, "paddingRight" to "20rpx", "paddingBottom" to 0, "paddingLeft" to "20rpx")), "year-month-text" to _pS(_uM("fontSize" to "36rpx", "fontWeight" to "bold", "color" to "#333333")), "week-header" to _pS(_uM("height" to "60rpx", "backgroundColor" to "#f0f0f0", "display" to "flex", "flexDirection" to "row", "alignItems" to "center")), "week-day" to _pS(_uM("flex" to 1, "display" to "flex", "justifyContent" to "center", "alignItems" to "center")), "week-day-text" to _pS(_uM("fontSize" to "24rpx", "color" to "#666666", "fontWeight" to "bold")), "calendar-grid" to _pS(_uM("paddingTop" to "10rpx", "paddingRight" to "10rpx", "paddingBottom" to "10rpx", "paddingLeft" to "10rpx", "backgroundColor" to "#ffffff")), "calendar-week" to _pS(_uM("display" to "flex", "flexDirection" to "row", "marginBottom" to "8rpx")), "calendar-day" to _uM("" to _uM("flex" to 1, "height" to "80rpx", "display" to "flex", "flexDirection" to "column", "justifyContent" to "center", "alignItems" to "center", "marginTop" to 0, "marginRight" to "2rpx", "marginBottom" to 0, "marginLeft" to "2rpx", "borderTopLeftRadius" to "8rpx", "borderTopRightRadius" to "8rpx", "borderBottomRightRadius" to "8rpx", "borderBottomLeftRadius" to "8rpx", "position" to "relative"), ".day-today" to _uM("backgroundColor" to "rgba(255,69,58,0.1)", "borderTopWidth" to "2rpx", "borderRightWidth" to "2rpx", "borderBottomWidth" to "2rpx", "borderLeftWidth" to "2rpx", "borderTopStyle" to "solid", "borderRightStyle" to "solid", "borderBottomStyle" to "solid", "borderLeftStyle" to "solid", "borderTopColor" to "#ff453a", "borderRightColor" to "#ff453a", "borderBottomColor" to "#ff453a", "borderLeftColor" to "#ff453a"), ".day-selected" to _uM("backgroundColor" to "#007aff", "transform" to "scale(1.05)"), ".day-disabled" to _uM("opacity" to 0.3)), "day-number" to _pS(_uM("fontSize" to "28rpx", "color" to "#333333", "fontWeight" to "bold", "lineHeight" to 1)), "day-number-today" to _pS(_uM("color" to "#ff453a")), "day-number-selected" to _pS(_uM("color" to "#ffffff")), "day-number-disabled" to _pS(_uM("color" to "#cccccc")), "day-lunar" to _pS(_uM("fontSize" to "20rpx", "color" to "#999999", "lineHeight" to 1, "marginTop" to "4rpx")), "day-lunar-today" to _pS(_uM("color" to "#ff453a")), "day-lunar-selected" to _pS(_uM("color" to "#ffffff")), "day-lunar-disabled" to _pS(_uM("color" to "#cccccc")), "current-selection" to _pS(_uM("paddingTop" to "20rpx", "paddingRight" to "20rpx", "paddingBottom" to "20rpx", "paddingLeft" to "20rpx", "backgroundColor" to "#f8f9fa", "display" to "flex", "flexDirection" to "row", "alignItems" to "center", "justifyContent" to "center", "borderTopWidth" to 1, "borderTopStyle" to "solid", "borderTopColor" to "#e5e5e5")), "selection-label" to _pS(_uM("fontSize" to "28rpx", "color" to "#666666", "marginRight" to "10rpx")), "selection-value" to _pS(_uM("fontSize" to "32rpx", "color" to "#007aff", "fontWeight" to "bold")))
            }
        var inheritAttrs = true
        var inject: Map<String, Map<String, Any?>> = _uM()
        var emits: Map<String, Any?> = _uM("cancel" to null, "confirm" to null)
        var props = _nP(_uM("initialDate" to _uM("type" to "String", "default" to fun(): String {
            val today = Date()
            val year = today.getFullYear()
            val month = today.getMonth() + 1
            val date = today.getDate()
            return "" + year + "-" + (if (month < 10) {
                "0" + month
            } else {
                month
            }
            ) + "-" + (if (date < 10) {
                "0" + date
            } else {
                date
            }
            )
        }
        )))
        var propsNeedCastKeys = _uA(
            "initialDate"
        )
        var components: Map<String, CreateVueComponent> = _uM("MainYearmonthPicker" to GenComponentsMainFormToolsMainYearmonthPickerClass)
    }
}
