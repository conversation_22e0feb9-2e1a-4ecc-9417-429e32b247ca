{"version": 3, "file": "calendar.uts", "sourceRoot": "", "sources": ["pages/calendar/calendar.uts"], "names": [], "mappings": "AAAA;;;;MAIG;AACH;;;;;;;;;;;GAWG;AAGH,MAAM,UAAU,GAAG;IAClB,OAAO;IACP,YAAY;IACZ,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;IACxF,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;IACxF,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;IACxF,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;IACxF,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;IACxF,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;IACxF,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;IACxF,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;IACxF,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;IACxF,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;IACxF,YAAY;IACZ,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;IACxF,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;IACxF,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;IACxF,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;IACxF,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;IACxF,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;IACxF,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;IACxF,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;IACxF,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;IACxF,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;CACxF,CAAA;AAED,6DAA6D;AAC7D,MAAM,OAAO,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAA;AAClJ,gDAAgD;AAChD,MAAM,OAAO,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAA;AAE9H,wBAAwB;AACxB,MAAM,OAAO,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAA;AAGlE,MAAM,MAAM,QAAQ,GAAG;IACtB,MAAM,EAAG,MAAM,CAAC;IAChB,MAAM,EAAG,MAAM,CAAC;IAChB,MAAM,EAAG,MAAM,CAAC;IAChB,MAAM,EAAG,OAAO,CAAC;CACjB,CAAA;AAED,MAAM,MAAM,aAAa,GAAG;IAC3B,KAAK,EAAG,MAAM,CAAC;IACf,MAAM,EAAG,MAAM,CAAC;IAChB,IAAI,EAAG,MAAM,CAAC;IACd,QAAQ,EAAG,MAAM,CAAC;IAClB,MAAM,EAAG,MAAM,CAAC;IAChB,KAAK,EAAG,MAAM,CAAC;IACf,MAAM,EAAG,MAAM,CAAC;IAChB,IAAI,EAAG,MAAM,CAAC;IACd,MAAO,CAAC,EAAE,MAAM,CAAC;IACjB,OAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,KAAM,CAAC,EAAE,MAAM,CAAC;IAChB,OAAO,EAAG,OAAO,CAAC;IAClB,MAAM,EAAG,OAAO,CAAC;IACjB,KAAM,CAAC,EAAE,MAAM,CAAC;IAChB,MAAO,CAAC,EAAE,MAAM,CAAC;IACjB,MAAO,CAAC,EAAE,OAAO,CAAC;IAClB,IAAK,CAAC,EAAE,MAAM,CAAC;IACf,KAAM,CAAC,EAAE,MAAM,CAAA;CACf,CAAA;AAGD,MAAM,OAAO,KAAK;IACjB,OAAO,CAAC,gBAAgB,GAAG,IAAI,GAAG,CAAC,MAAM,EAAE,MAAM,GAAG,CAAA;IACpD,OAAO,CAAC,iBAAiB,GAAG,IAAI,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA;IACvD,gBAAgB,CAAC;IACjB;;;;;UAKG;IACH,YAAY,CAAC,CAAC,EAAG,MAAM,EAAE,IAAI,EAAG,OAAO,GAAG,KAAK,GAAI,MAAM;QACxD,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;IAClF,CAAC;IAED;;;;;UAKG;IACH,UAAU,CAAC,CAAC,EAAG,MAAM,GAAI,MAAM;QAC9B,IAAI,CAAC,EAAG,MAAM,CAAA;QACd,QAAQ,CAAC,EAAE;YACV,KAAK,EAAE;gBACN,CAAC,GAAG,cAAc,CAAC;gBACnB,MAAK;YACN,KAAK,EAAE;gBACN,CAAC,GAAG,cAAc,CAAC;gBACnB,MAAK;YACN,KAAK,EAAE;gBACN,CAAC,GAAG,cAAc,CAAC;gBACnB,MAAK;YACN;gBACC,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAA;gBAC/B,CAAC,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,CAAA;SACrB;QACD,OAAO,CAAC,CAAC,CAAC,CAAA;IACX,CAAC;IAGD;;;;;UAKG;IACH,SAAS,CAAC,IAAI,EAAG,MAAM,GAAI,MAAM;QAChC,OAAO,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;IACtC,CAAC;IAED;;;;;UAKG;IACH,QAAQ,CAAC,IAAI,EAAG,MAAM,GAAI,MAAM;QAC/B,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;YAC7B,OAAO,CAAC,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;SAC1D;QACD,OAAO,CAAC,CAAC;IACV,CAAC;IAED,YAAY;IACZ,cAAc,CAAC,IAAI,EAAG,MAAM,GAAI,MAAM,EAAE;QAEtC,IAAI,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;QAChD,IAAI,SAAS,IAAI,IAAI,EAAE;YACrB,OAAO,SAAS,CAAA;SACjB;QAED,SAAS,GAAG,EAAE,CAAC;QAEf,IAAI,SAAS,GAAG,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;QAExC,KAAK,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;YAC5B,IAAI,QAAQ,GAAG,CAAC,SAAS,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;YACrD,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SAC1B;QAED,OAAO;QACP,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAEjC,IAAI,KAAK,GAAG,CAAC;YAAE,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;QAC/D,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CAAA;QAE3C,OAAO,SAAS,CAAC;IACnB,CAAC;IAGD,SAAS;IACT,aAAa,CAAC,IAAI,EAAG,MAAM,GAAI,MAAM;QACpC,IAAI,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YACpC,OAAO,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAA;SACvC;QACD,IAAI,GAAG,GAAG,CAAC,CAAC;QACZ,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACxC,GAAG,IAAI,IAAI,CAAC;QACb,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;QACpC,OAAO,GAAG,CAAC;IAEZ,CAAC;IAGD;;;;;;;UAOG;IACH,WAAW,CAAC,CAAC,EAAG,MAAM,EAAE,CAAC,EAAG,MAAM,EAAE,CAAC,EAAG,MAAM,GAAI,aAAa;QAC9D,IAAI,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACvC,IAAI,KAAK,GAAG,OAAO,CAAC,MAAM,CAAA;QAC1B,IAAI,MAAM,GAAG,OAAO,CAAC,MAAM,CAAA;QAC3B,IAAI,IAAI,GAAG,OAAO,CAAC,MAAM,CAAA;QACzB,IAAI,MAAM,GAAG,OAAO,CAAC,MAAM,CAAA;QAE3B,SAAS;QACT,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;QAElD,IAAI,MAAM,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;QAEzD,OAAO;QACP,IAAI,UAAU,GAAG,IAAI,IAAI,EAAE,CAAA;QAC3B,IAAI,OAAO,GAAG,KAAK,CAAA;QACnB,IAAI,UAAU,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI,UAAU,CAAC,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE;YACjG,OAAO,GAAG,IAAI,CAAA;SACd;QAED,IAAI,IAAI,EAAG,aAAa,GAAG;YAC1B,OAAO,EAAE,KAAK;YACd,QAAQ,EAAE,MAAM;YAChB,MAAM,EAAE,IAAI;YACZ,UAAU,EAAE,QAAQ;YACpB,QAAQ,EAAE,MAAM;YAChB,OAAO,EAAE,CAAC;YACV,QAAQ,EAAE,CAAC;YACX,MAAM,EAAE,CAAC;YACT,SAAS,EAAE,OAAO;YAClB,QAAQ,EAAE,MAAM;SAChB,CAAA;QACD,OAAO,IAAI,CAAA;IACZ,CAAC;IAED,UAAU,CAAC,CAAC,EAAG,MAAM,EAAE,CAAC,EAAG,MAAM,EAAE,CAAC,EAAG,MAAM,GAAI,QAAQ;QAExD,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAEjC,uBAAuB;QACvB,IAAI,MAAM,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,QAAQ,CAAC;QAChH,IAAI,IAAI,GAAG,CAAC,CAAA;QACZ,IAAI,CAAC,EAAG,MAAM,CAAC;QACf,KAAK,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,IAAI,IAAI,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC3C,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;YAC7B,MAAM,IAAI,IAAI,CAAC;SACf;QACD,IAAI,MAAM,GAAG,CAAC,EAAE;YACf,MAAM,IAAI,IAAI,CAAC;YACf,CAAC,EAAE,CAAC;SACJ;QAED,UAAU;QACV,IAAI,MAAM,EAAG,OAAO,GAAG,KAAK,CAAA;QAC5B,IAAI,CAAC,EAAG,MAAM,GAAG,CAAC,CAAC;QACnB,IAAI,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QACvC,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAE9B,IAAI,MAAM,GAAG,CAAC,EAAE;YACf,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;gBACpD,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;gBACpB,MAAM,IAAI,IAAI,CAAC;aACf;YACD,IAAI,MAAM,IAAI,CAAC,EAAE;gBAChB,CAAC,EAAE,CAAC;aACJ;YACD,IAAI,MAAM,GAAG,CAAC,EAAE;gBACf,MAAM,IAAI,IAAI,CAAC;aACf;SACD;aAAM;YACN,mBAAmB;YACnB,IAAI,MAAM,IAAI,CAAC,EAAE,EAAE;gBAClB,IAAI,IAAI,EAAG,QAAQ,GAAG;oBACrB,MAAM,EAAE,CAAC;oBACT,MAAM,EAAE,EAAE;oBACV,MAAM,EAAE,CAAC;oBACT,MAAM,EAAE,KAAK;iBACb,CAAA;gBACD,IAAI,GAAG,IAAI,CAAA;aACX;SACD;QAED,QAAQ;QACR,IAAI,KAAK,GAAG,CAAC,EAAE;YACd,IAAI,CAAC,IAAI,KAAK,GAAG,CAAC,EAAE;gBACnB,MAAM,GAAG,IAAI,CAAA;aACb;YACD,IAAI,CAAC,IAAI,KAAK,GAAG,CAAC,EAAE;gBACnB,CAAC,EAAE,CAAA;aACH;SACD;QACD,MAAM,IAAI,EAAG,QAAQ,GAAG;YACvB,MAAM,EAAE,CAAC;YACT,MAAM,EAAE,CAAC;YACT,MAAM,EAAE,EAAE,MAAM;YAChB,MAAM,EAAE,MAAM;SACd,CAAA;QAGD,OAAO,IAAI,CAAA;IACZ,CAAC;CACD", "sourcesContent": ["/**\r\n\t* 农历1900-2100的润大小信息表\r\n\t* @Array Of Property\r\n\t* @return Hex\r\n\t*/\r\n/* 源数据说明：\r\n *   lunarYear数据来自香港天文台提供的源数据反向推导得出，其中201项数据分别对应1900-2100年。\r\n *   示例： 2021年 -- 0x06aa0\r\n *   ╭-------┰-------┰-------┰-------┰--------╮\r\n *   ┆ 0000  ┆ 0110  ┆ 1010  ┆ 1010  ┆ 0000   ┆\r\n *   ┠-------╊-------╊-------╊-------╊--------┨\r\n *   ┆ 20-17 ┆ 16-12 ┆ 12-9  ┆  8-5  ┆  4-1   ┆\r\n *   ╰-------┸-------┸-------┸-------┸--------╯\r\n *   1-4: 表示当年有无闰年，有的话，为闰月的月份，没有的话，为0。 2021年无闰月\r\n *   5-16：为除了闰月外的正常月份是大月还是小月，1为30天，0为29天。从1月到12月对应的是第16位到第5位，2021年各月天数[29,30,30,29,30,29,30,29,30,29,30,29]\r\n *   17-20： 表示闰月是大月还是小月，仅当存在闰月的情况下有意义。(0/1,即闰大/小月)\r\n */\r\n\r\n\r\nconst lunarYears = [\r\n\t0x04bd8,\r\n\t// 1901-2000\r\n\t0x04ae0, 0x0a570, 0x054d5, 0x0d260, 0x0d950, 0x16554, 0x056a0, 0x09ad0, 0x055d2, 0x04ae0,\r\n\t0x0a5b6, 0x0a4d0, 0x0d250, 0x1d255, 0x0b540, 0x0d6a0, 0x0ada2, 0x095b0, 0x14977, 0x04970,\r\n\t0x0a4b0, 0x0b4b5, 0x06a50, 0x06d40, 0x1ab54, 0x02b60, 0x09570, 0x052f2, 0x04970, 0x06566,\r\n\t0x0d4a0, 0x0ea50, 0x16a95, 0x05ad0, 0x02b60, 0x186e3, 0x092e0, 0x1c8d7, 0x0c950, 0x0d4a0,\r\n\t0x1d8a6, 0x0b550, 0x056a0, 0x1a5b4, 0x025d0, 0x092d0, 0x0d2b2, 0x0a950, 0x0b557, 0x06ca0,\r\n\t0x0b550, 0x15355, 0x04da0, 0x0a5b0, 0x14573, 0x052b0, 0x0a9a8, 0x0e950, 0x06aa0, 0x0aea6,\r\n\t0x0ab50, 0x04b60, 0x0aae4, 0x0a570, 0x05260, 0x0f263, 0x0d950, 0x05b57, 0x056a0, 0x096d0,\r\n\t0x04dd5, 0x04ad0, 0x0a4d0, 0x0d4d4, 0x0d250, 0x0d558, 0x0b540, 0x0b6a0, 0x195a6, 0x095b0,\r\n\t0x049b0, 0x0a974, 0x0a4b0, 0x0b27a, 0x06a50, 0x06d40, 0x0af46, 0x0ab60, 0x09570, 0x04af5,\r\n\t0x04970, 0x064b0, 0x074a3, 0x0ea50, 0x06b58, 0x05ac0, 0x0ab60, 0x096d5, 0x092e0, 0x0c960,\r\n\t// 2001-2100\r\n\t0x0d954, 0x0d4a0, 0x0da50, 0x07552, 0x056a0, 0x0abb7, 0x025d0, 0x092d0, 0x0cab5, 0x0a950,\r\n\t0x0b4a0, 0x0baa4, 0x0ad50, 0x055d9, 0x04ba0, 0x0a5b0, 0x15176, 0x052b0, 0x0a930, 0x07954,\r\n\t0x06aa0, 0x0ad50, 0x05b52, 0x04b60, 0x0a6e6, 0x0a4e0, 0x0d260, 0x0ea65, 0x0d530, 0x05aa0,\r\n\t0x076a3, 0x096d0, 0x04afb, 0x04ad0, 0x0a4d0, 0x1d0b6, 0x0d250, 0x0d520, 0x0dd45, 0x0b5a0,\r\n\t0x056d0, 0x055b2, 0x049b0, 0x0a577, 0x0a4b0, 0x0aa50, 0x1b255, 0x06d20, 0x0ada0, 0x14b63,\r\n\t0x09370, 0x049f8, 0x04970, 0x064b0, 0x168a6, 0x0ea50, 0x06b20, 0x1a6c4, 0x0aae0, 0x092e0,\r\n\t0x0d2e3, 0x0c960, 0x0d557, 0x0d4a0, 0x0da50, 0x05d55, 0x056a0, 0x0a6d0, 0x055d4, 0x052d0,\r\n\t0x0a9b8, 0x0a950, 0x0b4a0, 0x0b6a6, 0x0ad50, 0x055a0, 0x0aba4, 0x0a5b0, 0x052b0, 0x0b273,\r\n\t0x06930, 0x07337, 0x06aa0, 0x0ad50, 0x14b55, 0x04b60, 0x0a570, 0x054e4, 0x0d160, 0x0e968,\r\n\t0x0d520, 0x0daa0, 0x16aa6, 0x056d0, 0x04ae0, 0x0a9d4, 0x0a2d0, 0x0d150, 0x0f252, 0x0d520\r\n]\r\n\r\n// ['月','正','一','二','三','四','五','六','七','八','九','十','冬','腊'];\r\nconst N_STR_3 = [\"\\u6708\", \"\\u6b63\", \"\\u4e8c\", \"\\u4e09\", \"\\u56db\", \"\\u4e94\", \"\\u516d\", \"\\u4e03\", \"\\u516b\", \"\\u4e5d\", \"\\u5341\", \"\\u51ac\", \"\\u814a\"]\r\n// ['日','一','二','三','四','五','六','七','八','九','十']\r\nconst N_STR_1 = [\"\\u65e5\", \"\\u4e00\", \"\\u4e8c\", \"\\u4e09\", \"\\u56db\", \"\\u4e94\", \"\\u516d\", \"\\u4e03\", \"\\u516b\", \"\\u4e5d\", \"\\u5341\"]\r\n\r\n// ['初','十','廿','卅','闰']\r\nconst N_STR_2 = [\"\\u521d\", \"\\u5341\", \"\\u5eff\", \"\\u5345\", \"\\u95f0\"]\r\n\r\n\r\nexport type InfoType = {\r\n\tlunarY : number;\r\n\tlunarM : number;\r\n\tlunarD : number;\r\n\tisLeap : boolean;\r\n}\r\n\r\nexport type LunarInfoType = {\r\n\tlYear : number;\r\n\tlMonth : number;\r\n\tlDay : number;\r\n\tIMonthCn : string;\r\n\tIDayCn : string;\r\n\tcYear : number;\r\n\tcMonth : number;\r\n\tcDay : number;\r\n\tgzYear ?: string;\r\n\tgzMonth ?: string;\r\n\tgzDay ?: string;\r\n\tisToday : boolean;\r\n\tisLeap : boolean;\r\n\tnWeek ?: number;\r\n\tncWeek ?: string;\r\n\tisTerm ?: boolean;\r\n\tTerm ?: string;\r\n\tastro ?: string\r\n}\r\n\r\n\r\nexport class Lunar {\r\n\tprivate lunarYearDaysMap = new Map<number, number>()\r\n\tprivate lunarMonthDaysMap = new Map<number, number[]>()\r\n\tconstructor() { }\r\n\t/**\r\n\t\t* 传入农历数字月份返回汉语通俗表示法\r\n\t\t* @param lunar month\r\n\t\t* @return Cn string\r\n\t\t* @eg:let cnMonth = calendar.toChinaMonth(12) ;//cnMonth='腊月'\r\n\t\t*/\r\n\ttoChinaMonth(m : number, leap : boolean = false) : string { // 月 => \\u6708\r\n\t\treturn leap ? (N_STR_3[4] + N_STR_3[m] + N_STR_3[0]) : (N_STR_3[m] + N_STR_3[0]);\r\n\t}\r\n\r\n\t/**\r\n\t\t* 传入农历日期数字返回汉字表示法\r\n\t\t* @param lunar day\r\n\t\t* @return Cn string\r\n\t\t* @eg:let cnDay = calendar.toChinaDay(21) ;//cnMonth='廿一'\r\n\t\t*/\r\n\ttoChinaDay(d : number) : string { // 日 => \\u65e5\r\n\t\tlet s : string\r\n\t\tswitch (d) {\r\n\t\t\tcase 10:\r\n\t\t\t\ts = '\\u521d\\u5341';\r\n\t\t\t\tbreak\r\n\t\t\tcase 20:\r\n\t\t\t\ts = '\\u4e8c\\u5341';\r\n\t\t\t\tbreak\r\n\t\t\tcase 30:\r\n\t\t\t\ts = '\\u4e09\\u5341';\r\n\t\t\t\tbreak\r\n\t\t\tdefault:\r\n\t\t\t\ts = N_STR_2[Math.floor(d / 10)]\r\n\t\t\t\ts += N_STR_1[d % 10]\r\n\t\t}\r\n\t\treturn (s)\r\n\t}\r\n\r\n\r\n\t/**\r\n\t\t* 返回农历y年闰月是哪个月；若y年没有闰月 则返回0\r\n\t\t* @param lunar Year\r\n\t\t* @return Number (0-12)\r\n\t\t* @eg:let leapMonth = calendar.leapMonth(1987) ;//leapMonth=6\r\n\t\t*/\r\n\tleapMonth(year : number) : number {\r\n\t\treturn lunarYears[year - 1900] & 0xF;\r\n\t}\r\n\r\n\t/**\r\n\t\t* 返回农历y年闰月的天数 若该年没有闰月则返回0\r\n\t\t* @param lunar Year\r\n\t\t* @return Number (0、29、30)\r\n\t\t* @eg:let leapMonthDay = calendar.leapDays(1987) ;//leapMonthDay=29\r\n\t\t*/\r\n\tleapDays(year : number) : number {\r\n\t\tif (this.leapMonth(year) > 0) {\r\n\t\t\treturn (lunarYears[year - 1900] & 0x10000) != 0 ? 30 : 29;\r\n\t\t}\r\n\t\treturn 0;\r\n\t}\r\n\r\n\t// 某年份农历各月天数\r\n\tlunarMonthDays(year : number) : number[] {\r\n\n\t\t let monthDays = this.lunarMonthDaysMap.get(year)\n\t\t\tif (monthDays != null) {\n\t\t\t\t\treturn monthDays\n\t\t\t}\n\n\t\t\tmonthDays = [];\n\n\t\t\tlet lunarYear = lunarYears[year - 1900];\n\n\t\t\tfor (let i = 15; i >= 4; i--) {\n\t\t\t\t\tlet monthDay = (lunarYear >> i & 0x1) != 0 ? 30 : 29;\n\t\t\t\t\tmonthDays.push(monthDay);\n\t\t\t}\n\n\t\t\t// 添加闰月\n\t\t\tlet leapM = this.leapMonth(year);\n\n\t\t\tif (leapM > 0) monthDays.splice(leapM, 0, this.leapDays(year));\n\t\t\tthis.lunarMonthDaysMap.set(year, monthDays)\n\n\t\t\treturn monthDays;\r\n\t}\r\n\r\n\r\n\t// 某年农历天数\r\n\tlunarYearDays(year : number) : number {\r\n\t\tif (this.lunarYearDaysMap.has(year)) {\r\n\t\t\treturn this.lunarYearDaysMap.get(year)!\r\n\t\t}\r\n\t\tlet num = 0;\r\n\t\tthis.lunarMonthDays(year).forEach(item => {\r\n\t\t\tnum += item;\r\n\t\t});\r\n\t\tthis.lunarYearDaysMap.set(year, num)\r\n\t\treturn num;\r\n\r\n\t}\r\n\r\n\r\n\t/**\r\n\t\t* 传入阳历年月日获得详细的公历、农历object信息 <=>JSON\r\n\t\t* @param y  solar year\r\n\t\t* @param m  solar month\r\n\t\t* @param d  solar day\r\n\t\t* @return JSON object\r\n\t\t* @eg:__f__('log','at pages/calendar/calendar.uts:195',calendar.solar2lunar(1987,11,01));\r\n\t\t*/\r\n\tsolar2lunar(y : number, m : number, d : number) : LunarInfoType { // 参数区间1900.1.31~2100.12.31\r\n\t\tlet moonDay = this.solar_date(y, m, d);\r\n\t\tlet lYear = moonDay.lunarY\r\n\t\tlet lMonth = moonDay.lunarM\r\n\t\tlet lDay = moonDay.lunarD\r\n\t\tlet isLeap = moonDay.isLeap\r\n\r\n\t\t// 计算农历日期\r\n\t\tconst IMonthCn = this.toChinaMonth(lMonth, isLeap)\r\n\r\n\t\tlet IDayCn = lDay == 1 ? IMonthCn : this.toChinaDay(lDay)\r\n\r\n\t\t// 是否今天\r\n\t\tlet isTodayObj = new Date()\r\n\t\tlet isToday = false\r\n\t\tif (isTodayObj.getFullYear() == y && isTodayObj.getMonth() + 1 == m && isTodayObj.getDate() == d) {\r\n\t\t\tisToday = true\r\n\t\t}\r\n\r\n\t\tlet info : LunarInfoType = {\r\n\t\t\t'lYear': lYear,\r\n\t\t\t'lMonth': lMonth,\r\n\t\t\t'lDay': lDay,\r\n\t\t\t'IMonthCn': IMonthCn,\r\n\t\t\t'IDayCn': IDayCn,\r\n\t\t\t'cYear': y,\r\n\t\t\t'cMonth': m,\r\n\t\t\t'cDay': d,\r\n\t\t\t'isToday': isToday,\r\n\t\t\t'isLeap': isLeap,\r\n\t\t}\r\n\t\treturn info\r\n\t}\r\n\r\n\tsolar_date(y : number, m : number, d : number) : InfoType { // 参数区间1900.1.31~2100.12.31\r\n\r\n\t\tlet date = new Date(y, m - 1, d);\r\n\r\n\t\t// 参照日期 1901-02-19 正月初一\r\n\t\tlet offset = (Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()) - Date.UTC(1901, 1, 19)) / 86400000;\r\n\t\tlet temp = 0\r\n\t\tlet i : number;\r\n\t\tfor (i = 1901; i < 2101 && offset > 0; i++) {\r\n\t\t\ttemp = this.lunarYearDays(i);\r\n\t\t\toffset -= temp;\r\n\t\t}\r\n\t\tif (offset < 0) {\r\n\t\t\toffset += temp;\r\n\t\t\ti--;\r\n\t\t}\r\n\r\n\t\t// 农历年、月、日\r\n\t\tlet isLeap : boolean = false\r\n\t\tlet j : number = 0;\r\n\t\tlet monthDays = this.lunarMonthDays(i);\r\n\t\tlet leapM = this.leapMonth(i);\r\n\r\n\t\tif (offset > 0) {\r\n\t\t\tfor (j = 0; j < monthDays.length && offset > 0; j++) {\r\n\t\t\t\ttemp = monthDays[j];\r\n\t\t\t\toffset -= temp;\r\n\t\t\t}\r\n\t\t\tif (offset == 0) {\r\n\t\t\t\tj++;\r\n\t\t\t}\r\n\t\t\tif (offset < 0) {\r\n\t\t\t\toffset += temp;\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\t// 补偿公历1901年2月的农历信息\r\n\t\t\tif (offset == -23) {\r\n\t\t\t\tlet info : InfoType = {\r\n\t\t\t\t\tlunarY: i,\r\n\t\t\t\t\tlunarM: 12,\r\n\t\t\t\t\tlunarD: 8,\r\n\t\t\t\t\tisLeap: false\r\n\t\t\t\t}\r\n\t\t\t\tinfo = info\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// 矫正闰年月\r\n\t\tif (leapM > 0) {\r\n\t\t\tif (j == leapM + 1) {\r\n\t\t\t\tisLeap = true\r\n\t\t\t}\r\n\t\t\tif (j >= leapM + 1) {\r\n\t\t\t\tj--\r\n\t\t\t}\r\n\t\t}\r\n\t\tconst info : InfoType = {\r\n\t\t\tlunarY: i,\r\n\t\t\tlunarM: j,\r\n\t\t\tlunarD: ++offset,\r\n\t\t\tisLeap: isLeap\r\n\t\t}\r\n\r\n\r\n\t\treturn info\r\n\t}\r\n}\n"]}