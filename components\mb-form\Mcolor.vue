<template>
	<view class="McolorBody">
		<view class="Mcolor">
			<view class="Mcolor-name" :style="{color:color}">
				{{data.name}}
			</view>
		
			<view class="Mcolor-box qShadow1" :style="{backgroundColor:mValue,borderColor:'#ccc'}" @click="selectColor">
				
			</view>
		
		</view>
		<mb-colorPicker ref="formcolorPicker" :color="ucolor" @confirm="confirm"></mb-colorPicker>
		
	</view>
	
	
	
	
	
</template>

<script>
	export default {
		name: "Mcolor",
		props: {
			data: {
				type: Object,
				default: {}
			},
			keyName: {
				type: String,
				default: ""
			
			},
			index: {
				type: Number,
				default: 0
			},
			color: {
				type: String,
				default: '#000'
			},
			bgColor: {
				type: String,
				default: "#f1f4f9"
			
			}
		},
		created() {
			
			
			if(this.data.isSave){
				
				this.saveKey=this.keyName+"_"+this.data.key
				
				uni.getStorage({
					key: this.saveKey,
					success:  (res)=> {
						this.mValue=res.data
						this.initColor(this.mValue)
						this.$emit("change",{index:this.index,value:res.data})
					},
					fail: () => {
						
						
						
						this.mValue=this.data.value
						this.initColor(this.mValue)
						
					}
				});
				
				
			}else{
				this.mValue=this.data.value
				this.initColor(this.mValue)
			}
		
		},
		data() {
			return {
				showPickerColor:false,
				saveKey:"",
				mValue:"#000",
				ucolor: {
					r: 255,
					g: 0,
					b: 0,
					a: 0.6
				}
			};
		},
		watch:{
	
			
		},
		methods:{

			
			initColor(color){		 
				setTimeout(()=>{
					this.$refs.formcolorPicker.initColor(color)
				},500)
			},
			
			confirm(e){
				
				if(this.data.varType=='rgba'){
					const values = Object.values(e.rgba);
					
					
					
					let color='rgba('+values.join(",")+')'
					
					this.mValue =color
					this.$emit("change",{index:this.index,value:color})
					
					if(this.data.isSave){
						try {
							uni.setStorageSync(this.saveKey, color);
						} catch (e) {
							// error
						}
						
					}
				}else{
					this.mValue = e.hex
					let color=e.hex
					this.$emit("change",{index:this.index,value:color})
					if(this.data.isSave){
						try {
							uni.setStorageSync(this.saveKey, color);
						} catch (e) {
							// error
						}
						
					}
				}
				
				
				
			},
			selectColor(){
				this.$refs.formcolorPicker.open();
			}
		}
	}
</script>

<style>
.Mcolor {
		width: 100%;
		display: flex;
		flex-direction: column;
		padding: 0 20rpx;
		margin-bottom: 20rpx;
	
	}
	
	.Mcolor-name {
		width: 100%;
		font-size: 32rpx;
		margin-bottom: 10rpx;
	}
	
	.Mcolor-box {
		width: 100%;
		height: 100rpx;
		border: 1rpx solid #fff;
		box-sizing: border-box;
		padding: 0 10rpx;
		border-radius: 20rpx;
	}
</style>