{"version": 3, "sources": ["components/main-color-picker/main-color-picker.uvue"], "names": [], "mappings": "AAmHC,SAAQ;AACR,KAAK,SAAQ,GAAI;IAAA,mBAAA,CAAA,EAAA,oBAAA,CAAA,WAAA,EAAA,qDAAA,EAAA,GAAA,EAAA,CAAA,CAAA,CAAA;IAChB,CAAA,EAAI,MAAM,CAAA;IACV,CAAA,EAAI,MAAM,CAAA;IACV,CAAA,EAAI,MAAK,CAAA;CACV,CAAA;AACA,KAAK,UAAS,GAAI;IAAA,mBAAA,CAAA,EAAA,oBAAA,CAAA,YAAA,EAAA,qDAAA,EAAA,GAAA,EAAA,CAAA,CAAA,CAAA;IAChB,CAAC,EAAE,MAAM,CAAA;IACT,CAAC,EAAE,MAAM,CAAA;IACT,CAAC,EAAE,MAAM,CAAA;IACT,CAAC,EAAE,MAAK,CAAA;CACV,CAAA;AACA,KAAK,SAAQ,GAAI;IAAA,mBAAA,CAAA,EAAA,oBAAA,CAAA,WAAA,EAAA,qDAAA,EAAA,GAAA,EAAA,CAAA,CAAA,CAAA;IACf,CAAC,EAAE,MAAM,CAAA;IACT,CAAC,EAAE,MAAM,CAAA;IACT,CAAC,EAAE,MAAK,CAAA;CACV,CAAA;AACA,KAAK,WAAU,GAAI;IAAA,mBAAA,CAAA,EAAA,oBAAA,CAAA,aAAA,EAAA,qDAAA,EAAA,GAAA,EAAA,CAAA,CAAA,CAAA;IACjB,IAAI,EAAE,MAAM,CAAA;IACZ,KAAK,EAAE,MAAK,CAAA;CACd,CAAA;AACA,MAAK,OAAQ,GAAE,eAAA,CAAA;IACd,IAAI,EAAE,mBAAmB;IACzB,KAAK,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAC;IAC5B,IAAI;QACH,OAAO;YACN,SAAQ;YACR,OAAO,EAAE,KAAI,IAAK,OAAO;YACzB,cAAa;YACb,mBAAmB,EAAE,CAAA,IAAK,MAAM;YAChC,YAAW;YACX,OAAO,EAAE,GAAE,IAAK,MAAM;YACtB,YAAW;YACX,kBAAkB,EAAE,CAAA,IAAK,MAAM;YAC/B,iBAAgB;YAChB,SAAS,EAAE,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAE,EAAE,IAAK,SAAS;YACpD,kBAAiB;YACjB,UAAU,EAAE,CAAA,IAAK,MAAM;YACvB,YAAW;YACX,YAAY,EAAE,KAAI,IAAK,OAAO;YAC9B,KAAK,EAAE,GAAE,IAAK,MAAM;YACpB,KAAK,EAAE,CAAA,IAAK,MAAM;YAClB,KAAK,EAAE,CAAA,IAAK,MAAM;YAClB,QAAO;YACP,WAAW,EAAE,EAAC,IAAK,MAAK;SACzB,CAAA;IACD,CAAC;IACD,QAAQ,EAAE;QACT,SAAQ;QACR,eAAe,IAAI,WAAW,EAAC;YAC9B,OAAO;gBACN,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,SAAQ,EAAG;gBACjC,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,SAAQ,EAAG;gBACjC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,SAAQ,EAAG;gBAChC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,SAAQ,EAAG;gBAChC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,SAAQ,EAAG;gBAChC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,SAAQ,EAAG;gBAChC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,SAAQ,EAAG;gBAChC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,SAAQ,EAAG;gBAChC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,SAAQ,EAAG;gBAChC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,SAAQ,EAAG;gBAChC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,SAAQ,EAAE;aAChC,CAAA;QACD,CAAC;QAED,0BAAyB;QACzB,SAAS,IAAK,MAAM,EAAC;YACpB,MAAM,MAAK,EAAI,MAAM,EAAC,GAAI,EAAC,CAAA;YAE3B,KAAK,IAAI,CAAA,GAAI,CAAC,EAAE,CAAA,GAAI,GAAG,EAAE,CAAC,EAAE,EAAE;gBAC7B,MAAM,GAAE,GAAI,IAAI,CAAC,KAAK,CAAC,CAAA,GAAI,EAAE,CAAA,CAAA,CAAE,WAAU;gBACzC,MAAM,GAAE,GAAI,CAAA,GAAI,EAAC,CAAA,CAAE,YAAW;gBAE9B,SAAQ;gBACR,MAAM,SAAQ,GAAI,GAAE,GAAI,GAAE,CAAA,CAAE,UAAS;gBACrC,MAAM,SAAQ,GAAI,GAAE,GAAI,IAAG,CAAA,CAAE,UAAS;gBAEtC,kBAAiB;gBACjB,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAK,CAAA;gBAElC,IAAI,IAAI,CAAC,mBAAkB,IAAK,CAAC,EAAE;oBAClC,wBAAuB;oBACvB,MAAM,KAAI,GAAI,CAAC,GAAE,GAAI,EAAC,GAAI,GAAE,GAAI,IAAI,CAAC,UAAU,CAAA,GAAI,GAAE,CAAA;oBACrD,MAAM,KAAI,GAAI,CAAC,GAAE,GAAI,EAAC,GAAI,GAAE,GAAI,IAAI,CAAC,UAAS,GAAI,GAAG,CAAA,GAAI,IAAG,CAAA;oBAC5D,MAAM,KAAI,GAAI,CAAC,GAAE,GAAI,EAAC,GAAI,GAAE,GAAI,IAAI,CAAC,UAAS,GAAI,GAAG,CAAA,GAAI,IAAG,CAAA;oBAC5D,CAAA,GAAI,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAA,GAAI,GAAE,GAAI,GAAG,CAAA,GAAI,GAAG,CAAA,CAAA;oBAClD,CAAA,GAAI,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAA,GAAI,GAAE,GAAI,GAAG,CAAA,GAAI,GAAG,CAAA,CAAA;oBAClD,CAAA,GAAI,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAA,GAAI,GAAE,GAAI,GAAG,CAAA,GAAI,GAAG,CAAA,CAAA;iBACnD;qBAAO,IAAI,IAAI,CAAC,mBAAkB,IAAK,CAAC,EAAE;oBACzC,mBAAkB;oBAClB,MAAM,WAAU,GAAI,CAAC,GAAE,GAAI,EAAC,GAAI,GAAG,CAAA,GAAI,KAAI,CAAA,CAAE,WAAU;oBACvD,MAAM,SAAQ,GAAI,IAAI,CAAC,KAAK,CAAC,WAAU,GAAI,GAAG,CAAA,CAAA;oBAC9C,CAAA,GAAI,SAAQ,CAAA;oBACZ,CAAA,GAAI,SAAQ,CAAA;oBACZ,CAAA,GAAI,SAAQ,CAAA;iBACb;qBAAO,IAAI,IAAI,CAAC,mBAAkB,IAAK,CAAC,EAAE;oBACzC,kBAAiB;oBACjB,MAAM,WAAU,GAAI,CAAC,GAAE,GAAI,EAAC,GAAI,GAAG,CAAA,GAAI,KAAI,CAAA,CAAE,MAAK;oBAClD,MAAM,UAAS,GAAI,GAAE,GAAI,WAAU,GAAI,GAAE,CAAA,CAAE,eAAc;oBACzD,MAAM,UAAS,GAAI,GAAE,GAAI,CAAC,CAAA,GAAI,IAAI,CAAC,GAAG,CAAC,WAAU,GAAI,GAAG,CAAA,GAAI,CAAC,CAAA,GAAI,GAAE,CAAA,CAAE,SAAQ;oBAC7E,CAAA,GAAI,IAAI,CAAC,KAAK,CAAC,UAAS,GAAI,GAAG,CAAA,CAAA;oBAC/B,CAAA,GAAI,IAAI,CAAC,KAAK,CAAC,UAAS,GAAI,CAAC,CAAA,GAAI,UAAU,CAAA,GAAI,GAAG,CAAA,CAAA;oBAClD,CAAA,GAAI,IAAI,CAAC,KAAK,CAAC,UAAS,GAAI,CAAC,CAAA,GAAI,UAAU,CAAA,GAAI,GAAG,CAAA,CAAA;iBACnD;qBAAO;oBACN,uBAAsB;oBACtB,MAAM,WAAU,GAAI,CAAC,GAAE,GAAI,EAAC,GAAI,GAAG,CAAA,GAAI,KAAI,CAAA,CAAE,MAAK;oBAElD,eAAc;oBACd,IAAI,OAAO,EAAE,MAAK,CAAA;oBAClB,IAAI,IAAI,CAAC,mBAAkB,IAAK,CAAC;wBAAE,OAAM,GAAI,EAAC,CAAA,CAAO,KAAI;yBACpD,IAAI,IAAI,CAAC,mBAAkB,IAAK,CAAC;wBAAE,OAAM,GAAI,EAAC,CAAA,CAAE,KAAI;yBACpD,IAAI,IAAI,CAAC,mBAAkB,IAAK,CAAC;wBAAE,OAAM,GAAI,GAAE,CAAA,CAAE,KAAI;yBACrD,IAAI,IAAI,CAAC,mBAAkB,IAAK,CAAC;wBAAE,OAAM,GAAI,GAAE,CAAA,CAAE,KAAI;yBACrD,IAAI,IAAI,CAAC,mBAAkB,IAAK,CAAC;wBAAE,OAAM,GAAI,GAAE,CAAA,CAAE,KAAI;yBACrD,IAAI,IAAI,CAAC,mBAAkB,IAAK,CAAC;wBAAE,OAAM,GAAI,GAAE,CAAA,CAAE,KAAI;yBACrD,IAAI,IAAI,CAAC,mBAAkB,IAAK,CAAC;wBAAE,OAAM,GAAI,GAAE,CAAA,CAAE,KAAI;;wBACrD,OAAM,GAAI,EAAC,CAAA,CAAE,KAAI;oBAEtB,sBAAqB;oBACrB,MAAM,GAAE,GAAI,OAAM,GAAI,CAAC,SAAQ,GAAI,GAAG,CAAA,GAAI,EAAC,CAAA;oBAE3C,cAAa;oBACb,IAAI,WAAU,GAAI,GAAG,EAAE;wBACtB,6BAA4B;wBAC5B,MAAM,WAAU,GAAI,WAAU,GAAI,GAAE,CAAA;wBACpC,MAAM,UAAS,GAAI,GAAE,GAAI,WAAU,GAAI,GAAE,CAAA,CAAE,UAAS;wBACpD,MAAM,KAAI,GAAI,GAAE,GAAI,WAAU,GAAI,GAAE,CAAA,CAAE,gBAAe;wBACrD,MAAM,GAAE,GAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,UAAU,EAAE,KAAK,CAAA,CAAA;wBAChD,CAAA,GAAI,GAAG,CAAC,CAAA,CAAA;wBACR,CAAA,GAAI,GAAG,CAAC,CAAA,CAAA;wBACR,CAAA,GAAI,GAAG,CAAC,CAAA,CAAA;qBACT;yBAAO,IAAI,WAAU,GAAI,GAAG,EAAE;wBAC7B,wBAAuB;wBACvB,MAAM,WAAU,GAAI,CAAC,WAAU,GAAI,GAAG,CAAA,GAAI,GAAE,CAAA;wBAC5C,MAAM,UAAS,GAAI,GAAE,CAAA,CAAE,QAAO;wBAC9B,MAAM,KAAI,GAAI,GAAE,GAAI,WAAU,GAAI,GAAE,CAAA,CAAE,kBAAiB;wBACvD,MAAM,GAAE,GAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,UAAU,EAAE,KAAK,CAAA,CAAA;wBAChD,CAAA,GAAI,GAAG,CAAC,CAAA,CAAA;wBACR,CAAA,GAAI,GAAG,CAAC,CAAA,CAAA;wBACR,CAAA,GAAI,GAAG,CAAC,CAAA,CAAA;qBACT;yBAAO;wBACN,yBAAwB;wBACxB,MAAM,WAAU,GAAI,CAAC,WAAU,GAAI,GAAG,CAAA,GAAI,GAAE,CAAA;wBAC5C,MAAM,UAAS,GAAI,GAAE,GAAI,WAAU,GAAI,GAAE,CAAA,CAAE,mBAAkB;wBAC7D,MAAM,KAAI,GAAI,GAAE,GAAI,WAAU,GAAI,GAAE,CAAA,CAAE,iBAAgB;wBACtD,MAAM,GAAE,GAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,UAAU,EAAE,KAAK,CAAA,CAAA;wBAChD,CAAA,GAAI,GAAG,CAAC,CAAA,CAAA;wBACR,CAAA,GAAI,GAAG,CAAC,CAAA,CAAA;wBACR,CAAA,GAAI,GAAG,CAAC,CAAA,CAAA;qBACT;iBACD;gBAEA,kBAAiB;gBACjB,CAAA,GAAI,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAA,CAAA;gBAChC,CAAA,GAAI,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAA,CAAA;gBAChC,CAAA,GAAI,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAA,CAAA;gBAEhC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAA,CAAA;aACpC;YAEA,OAAO,MAAK,CAAA;QACb,CAAC;QAID,aAAY;QACZ,UAAU,IAAK,MAAK;YACnB,YAAW;YACX,IAAI,UAAS,GAAI,EAAC,CAAA;YAClB,IAAI,IAAI,CAAC,WAAU,IAAK,EAAE,EAAE;gBAC3B,UAAS,GAAI,IAAI,CAAC,WAAU,CAAA;aAC7B;iBAAO,IAAI,IAAI,CAAC,SAAS,CAAC,MAAK,GAAI,IAAI,CAAC,kBAAkB,EAAE;gBAC3D,UAAS,GAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,kBAAkB,CAAA,CAAA;aACpD;YAEA,IAAI,UAAS,IAAK,EAAE,EAAE;gBACrB,eAAc;gBACd,MAAM,QAAO,GAAI,UAAU,CAAC,KAAK,CAAC,gCAAgC,CAAA,CAAA;gBAClE,IAAI,QAAO,IAAK,IAAI,EAAE;oBACrB,MAAM,CAAA,GAAI,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAA,IAAK,MAAM,CAAA,CAAA;oBACxC,MAAM,CAAA,GAAI,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAA,IAAK,MAAM,CAAA,CAAA;oBACxC,MAAM,CAAA,GAAI,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAA,IAAK,MAAM,CAAA,CAAA;oBACxC,OAAO,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,OAAO,GAAE,CAAA;iBAChD;aACD;YACA,OAAO,mBAAmB,IAAI,CAAC,OAAO,GAAE,CAAA;QACzC,CAAC;QAED,YAAW;QACX,YAAY,IAAK,MAAK;YACrB,OAAO,OAAO,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,GAAE,CAAA;QACzD,CAAA;KACA;IACD,OAAO,EAAE;QACR,WAAU;QACV,cAAc,CAAC,KAAK,EAAE,MAAM;YAC3B,IAAI,CAAC,mBAAkB,GAAI,KAAI,CAAA;YAC/B,IAAI,CAAC,kBAAiB,GAAI,CAAA,CAAA,CAAE,UAAS;YACrC,IAAI,CAAC,WAAU,GAAI,EAAC,CAAA,CAAE,UAAS;YAE/B,uBAAsB;YACtB,IAAI,KAAI,IAAK,CAAC,EAAE,EAAE,mBAAkB;gBACnC,IAAI,CAAC,UAAS,GAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAC,GAAI,IAAI,CAAA,CAAA;aAClD;QACD,CAAC;QAED,WAAU;QACV,iBAAiB;YAChB,MAAM,cAAa,GAAI;gBACtB,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK;gBACrE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAG;aACnE,CAAA;YAEA,GAAG,CAAC,eAAe,CAAC;gBACnB,QAAQ,EAAE,cAAc;gBACxB,OAAO,EAAE,CAAC,GAAG,EAAE,EAAC;oBACf,MAAM,eAAc,GAAI,CAAC,GAAE,GAAI,GAAG,CAAC,QAAO,GAAI,CAAC,CAAA,GAAI,GAAE,CAAA;oBACrD,IAAI,CAAC,OAAM,GAAI,eAAc,CAAA;gBAC9B,CAAA;aACA,CAAA,CAAA;QACF,CAAC;QAED,SAAQ;QACR,aAAa,CAAC,KAAI,EAAI,MAAM;YAC3B,IAAI,CAAC,kBAAiB,GAAI,KAAI,CAAA;YAC9B,mBAAkB;YAClB,IAAI,CAAC,WAAU,GAAI,EAAC,CAAA;QACrB,CAAC;QAED,YAAW;QACX,aAAa;YACZ,yBAAwB;YACxB,IAAI,UAAS,GAAI,EAAC,CAAA;YAClB,IAAI,IAAI,CAAC,WAAU,IAAK,EAAE,EAAE;gBAC3B,UAAS,GAAI,IAAI,CAAC,WAAU,CAAA;aAC7B;iBAAO,IAAI,IAAI,CAAC,SAAS,CAAC,MAAK,GAAI,IAAI,CAAC,kBAAkB,EAAE;gBAC3D,UAAS,GAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,kBAAkB,CAAA,CAAA;aACpD;YAEA,IAAI,UAAS,IAAK,EAAE,EAAE;gBACrB,MAAM,QAAO,GAAI,UAAU,CAAC,KAAK,CAAC,gCAAgC,CAAA,CAAA;gBAClE,IAAI,QAAO,IAAK,IAAI,EAAE;oBACrB,IAAI,CAAC,KAAI,GAAI,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAA,IAAK,MAAM,CAAA,CAAA;oBAC3C,IAAI,CAAC,KAAI,GAAI,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAA,IAAK,MAAM,CAAA,CAAA;oBAC3C,IAAI,CAAC,KAAI,GAAI,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAA,IAAK,MAAM,CAAA,CAAA;iBAC5C;qBAAO;oBACN,IAAI,CAAC,KAAI,GAAI,GAAE,CAAA;oBACf,IAAI,CAAC,KAAI,GAAI,CAAA,CAAA;oBACb,IAAI,CAAC,KAAI,GAAI,CAAA,CAAA;iBACd;aACD;iBAAO;gBACN,IAAI,CAAC,KAAI,GAAI,GAAE,CAAA;gBACf,IAAI,CAAC,KAAI,GAAI,CAAA,CAAA;gBACb,IAAI,CAAC,KAAI,GAAI,CAAA,CAAA;aACd;YACA,IAAI,CAAC,YAAW,GAAI,IAAG,CAAA;QACxB,CAAC;QAED,YAAW;QACX,cAAc;YACb,IAAI,CAAC,YAAW,GAAI,KAAI,CAAA;QACzB,CAAC;QAED,kBAAiB;QACjB,eAAe;YACd,eAAc;QACf,CAAC;QAED,UAAS;QACT,gBAAgB;YACf,UAAS;YACT,IAAI,CAAC,WAAU,GAAI,OAAO,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,GAAE,CAAA;YACpE,IAAI,CAAC,YAAW,GAAI,KAAI,CAAA;QACzB,CAAC;QAED,SAAQ;QACR,aAAa,CAAC,KAAK,EAAE,oBAAoB;YACxC,IAAI,CAAC,KAAI,GAAI,KAAK,CAAC,MAAM,CAAC,KAAI,IAAK,MAAK,CAAA;QACzC,CAAC;QAED,SAAQ;QACR,aAAa,CAAC,KAAK,EAAE,oBAAoB;YACxC,IAAI,CAAC,KAAI,GAAI,KAAK,CAAC,MAAM,CAAC,KAAI,IAAK,MAAK,CAAA;QACzC,CAAC;QAED,SAAQ;QACR,aAAa,CAAC,KAAK,EAAE,oBAAoB;YACxC,IAAI,CAAC,KAAI,GAAI,KAAK,CAAC,MAAM,CAAC,KAAI,IAAK,MAAK,CAAA;QACzC,CAAC;QAED,UAAS;QACT,YAAY,CAAC,KAAK,EAAE,aAAa;YAChC,MAAM,KAAI,GAAI,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAA,CAAA;YACzC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;gBAClB,IAAI,CAAC,KAAI,GAAI,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA,CAAA;aAC9C;QACD,CAAC;QAED,UAAS;QACT,YAAY,CAAC,KAAK,EAAE,aAAa;YAChC,MAAM,KAAI,GAAI,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAA,CAAA;YACzC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;gBAClB,IAAI,CAAC,KAAI,GAAI,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA,CAAA;aAC9C;QACD,CAAC;QAED,UAAS;QACT,YAAY,CAAC,KAAK,EAAE,aAAa;YAChC,MAAM,KAAI,GAAI,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAA,CAAA;YACzC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;gBAClB,IAAI,CAAC,KAAI,GAAI,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA,CAAA;aAC9C;QACD,CAAC;QAED,OAAM;QACN,IAAI;YACH,IAAI,CAAC,OAAM,GAAI,IAAG,CAAA;QACnB,CAAC;QAED,OAAM;QACN,KAAK;YACJ,IAAI,CAAC,OAAM,GAAI,KAAI,CAAA;QACpB,CAAC;QAED,YAAW;QACX,cAAc;YACb,IAAI,CAAC,KAAK,EAAC,CAAA;YACX,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAA,CAAA;QACpB,CAAC;QAED,WAAU;QACV,QAAQ;YACP,IAAI,CAAC,KAAK,EAAC,CAAA;YACX,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAA,CAAA;QACpB,CAAC;QAED,WAAU;QACV,SAAS;YACR,IAAI,CAAC,KAAK,EAAC,CAAA;YACX,MAAM,UAAS,GAAI,IAAI,CAAC,aAAa,EAAC,CAAA;YACtC,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE;gBACrB,KAAK,EAAE,IAAI,CAAC,UAAU;gBACtB,IAAI,EAAE,UAAU;gBAChB,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAA;aAC3D,CAAA,CAAA;QACF,CAAC;QAED,WAAU;QACV,aAAa,IAAK,UAAS;YAC1B,MAAM,SAAQ,GAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,6CAA6C,CAAA,CAAA;YACrF,IAAI,SAAQ,IAAK,IAAI,EAAE;gBACtB,OAAO;oBACN,CAAC,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAA,IAAK,MAAM,CAAC;oBACnC,CAAC,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAA,IAAK,MAAM,CAAC;oBACnC,CAAC,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAA,IAAK,MAAM,CAAC;oBACnC,CAAC,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC,CAAA,IAAK,MAAM,CAAA;iBACrC,eAAA;aACD;YACA,OAAO,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAE,EAAE,eAAA;QACrC,CAAC;QAED,UAAS;QACT,QAAQ,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,GAAG,SAAQ;YAClD,MAAM,CAAC,EAAE,MAAK,GAAI,CAAA,GAAI,CAAA,CAAA;YACtB,MAAM,CAAC,EAAE,MAAK,GAAI,CAAA,GAAI,CAAC,GAAE,GAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA,GAAI,IAAI,CAAA,GAAI,GAAG,CAAA,GAAI,GAAG,CAAC,CAAA,CAAA;YAC/D,MAAM,CAAC,EAAE,MAAK,GAAI,CAAA,GAAI,CAAA,CAAA;YAEtB,IAAI,CAAC,EAAE,MAAK,GAAI,GAAE,CAAA;YAClB,IAAI,CAAC,EAAE,MAAK,GAAI,GAAE,CAAA;YAClB,IAAI,CAAC,EAAE,MAAK,GAAI,GAAE,CAAA;YAElB,IAAI,CAAA,IAAK,CAAA,IAAK,CAAA,GAAI,EAAE,EAAE;gBACrB,CAAA,GAAI,CAAA,CAAA;gBACJ,CAAA,GAAI,CAAA,CAAA;gBACJ,CAAA,GAAI,GAAE,CAAA;aACP;iBAAO,IAAI,CAAA,IAAK,EAAC,IAAK,CAAA,GAAI,GAAG,EAAE;gBAC9B,CAAA,GAAI,CAAA,CAAA;gBACJ,CAAA,GAAI,CAAA,CAAA;gBACJ,CAAA,GAAI,GAAE,CAAA;aACP;iBAAO,IAAI,CAAA,IAAK,GAAE,IAAK,CAAA,GAAI,GAAG,EAAE;gBAC/B,CAAA,GAAI,GAAE,CAAA;gBACN,CAAA,GAAI,CAAA,CAAA;gBACJ,CAAA,GAAI,CAAA,CAAA;aACL;iBAAO,IAAI,CAAA,IAAK,GAAE,IAAK,CAAA,GAAI,GAAG,EAAE;gBAC/B,CAAA,GAAI,GAAE,CAAA;gBACN,CAAA,GAAI,CAAA,CAAA;gBACJ,CAAA,GAAI,CAAA,CAAA;aACL;iBAAO,IAAI,CAAA,IAAK,GAAE,IAAK,CAAA,GAAI,GAAG,EAAE;gBAC/B,CAAA,GAAI,CAAA,CAAA;gBACJ,CAAA,GAAI,GAAE,CAAA;gBACN,CAAA,GAAI,CAAA,CAAA;aACL;iBAAO,IAAI,CAAA,IAAK,GAAE,IAAK,CAAA,GAAI,GAAG,EAAE;gBAC/B,CAAA,GAAI,CAAA,CAAA;gBACJ,CAAA,GAAI,GAAE,CAAA;gBACN,CAAA,GAAI,CAAA,CAAA;aACL;YAEA,MAAM,MAAM,EAAE,SAAQ,GAAI;gBACzB,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAA,GAAI,CAAC,CAAA,GAAI,KAAK,CAAC;gBAC9B,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAA,GAAI,CAAC,CAAA,GAAI,KAAK,CAAC;gBAC9B,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAA,GAAI,CAAC,CAAA,GAAI,KAAK,CAAA;aAC9B,CAAA;YACA,OAAO,MAAK,CAAA;QACb,CAAC;QAED,WAAU;QACV,QAAQ,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,GAAG,MAAK;YAC/C,MAAM,KAAI,GAAI,CAAC,KAAK,EAAE,MAAM,GAAG,MAAK,CAAE,EAAC;gBACtC,MAAM,GAAE,GAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAA,CAAA;gBACrE,OAAO,GAAG,CAAC,MAAK,IAAK,CAAA,CAAE,CAAA,CAAE,GAAE,GAAI,GAAE,CAAE,CAAA,CAAE,GAAE,CAAA;YACxC,CAAA,CAAA;YACA,OAAO,GAAE,GAAI,KAAK,CAAC,CAAC,CAAA,GAAI,KAAK,CAAC,CAAC,CAAA,GAAI,KAAK,CAAC,CAAC,CAAA,CAAA;QAC3C,CAAC;KAGF;CACD,CAAA,CAAA;;;;;;kBAjhBY,IAAA,CAAA,OAAO,CAAA;UAAnB,GAAA,CA6GO,MAAA,EAAA,GAAA,CAAA;;YA7Gc,KAAK,EAAC,gBAAgB;YAAE,OAAK,EAAE,IAAA,CAAA,cAAc;;YACjE,GAAA,CA2GO,MAAA,EAAA,GAAA,CAAA;gBA3GD,KAAK,EAAC,cAAc;gBAAE,OAAK,EAAA,aAAA,CAAN,GAAA,EAAA,GAAA,CAAc,EAAA,CAAA,MAAA,CAAA,CAAA;;gBACxC,GAAA,CAyGO,MAAA,EAAA,GAAA,CAAA,EAzGD,KAAK,EAAC,wBAAwB,EAAA,CAAA,EAAA;oBAEnC,GAAA,CAMO,MAAA,EAAA,GAAA,CAAA,EAND,KAAK,EAAC,QAAQ,EAAA,CAAA,EAAA;wBACnB,GAAA,CAA4D,MAAA,EAAA,GAAA,CAAA;4BAAtD,KAAK,EAAC,oBAAoB;4BAAE,OAAK,EAAE,IAAA,CAAA,QAAQ;4BAAE,IAAE,EAAA,CAAA,CAAA,WAAA,EAAA,CAAA,SAAA,CAAA,CAAA;wBACrD,GAAA,CAAmC,MAAA,EAAA,GAAA,CAAA,EAA7B,KAAK,EAAC,WAAW,EAAA,CAAA,EAAC,MAAI,CAAA;wBAC5B,GAAA,CAEO,MAAA,EAAA,GAAA,CAAA,EAFD,KAAK,EAAC,uBAAuB,EAAA,CAAA,EAAA;4BAClC,GAAA,CAA8D,MAAA,EAAA,GAAA,CAAA;gCAAxD,KAAK,EAAC,qBAAqB;gCAAE,OAAK,EAAE,IAAA,CAAA,SAAS;gCAAE,IAAE,EAAA,CAAA,CAAA,WAAA,EAAA,CAAA,SAAA,CAAA,CAAA;;;oBAKzD,GAAA,CAiBO,MAAA,EAAA,GAAA,CAAA,EAjBD,KAAK,EAAC,sBAAsB,EAAA,CAAA,EAAA;wBACjC,GAAA,CAeO,MAAA,EAAA,GAAA,CAAA,EAfD,KAAK,EAAC,sBAAsB,EAAA,CAAA,EAAA;4BACjC,GAAA,CAaO,QAAA,EAAA,IAAA,EAAA,aAAA,CAAA,UAAA,CAZoB,IAAA,CAAA,eAAe,EAAA,CAAjC,MAAM,EAAE,KAAK,EAAb,OAAM,EAAA,OAAA,GAAA,GAAA,CAAA,EAAA;uCADf,GAAA,CAaO,MAAA,EAAA,GAAA,CAAA;oCAXL,GAAG,EAAE,KAAK;oCACX,KAAK,EAAA,GAAA,CAAA,CAAC,eAAe,EACb,GAAA,CAAA;;;;0CAIP,CAAA,CAAA;oCACA,KAAK,EAAA,GAAA,CAAE,GAAA,CAAA,EAAA,eAAA,EAAA,MAAA,CAAA,KAAA,EAAA,CAAiC,CAAA;oCACxC,OAAK,EAAA,GAAA,EAAA,GAAE,IAAA,CAAA,cAAc,CAAC,KAAK,CAAA,CAAA,CAAA,CAAA;;oCAE5B,GAAA,CAAkD,MAAA,EAAA,GAAA,CAAA,EAA5C,KAAK,EAAC,aAAa,EAAA,CAAA,EAAA,GAAA,CAAI,MAAM,CAAC,IAAI,CAAA,EAAA,CAAA,CAAA,UAAA,CAAA;;;;;oBAM3C,GAAA,CAOO,MAAA,EAAA,GAAA,CAAA,EAPD,KAAK,EAAC,oBAAoB,EAAA,CAAA,EAAA;wBAC/B,GAAA,CAKO,MAAA,EAAA,GAAA,CAAA,EALD,KAAK,EAAC,YAAY,EAAA,CAAA,EAAA;4BACvB,GAAA,CAGO,QAAA,EAAA,IAAA,EAAA,aAAA,CAAA,UAAA,CAHwB,IAAA,CAAA,SAAS,EAAA,CAA1B,KAAK,EAAE,KAAK,EAAZ,OAAK,EAAA,OAAA,GAAA,GAAA,CAAA,EAAA;uCAAnB,GAAA,CAGO,MAAA,EAAA,GAAA,CAAA;oCAHoC,GAAG,EAAE,KAAK;oCAAE,KAAK,EAAA,GAAA,CAAA,CAAC,YAAY,EAChE,GAAA,CAAA,EAAA,UAAA,EAAA,IAAA,CAAA,kBAAA,IAAA,KAAA,EAAA,CAA2C,CAAA,CAAA;oCAAG,KAAK,EAAA,GAAA,CAAE,GAAA,CAAA,EAAA,eAAA,EAAA,KAAA,EAAA,CAA0B,CAAA;oCACtF,OAAK,EAAA,GAAA,EAAA,GAAE,IAAA,CAAA,aAAa,CAAC,KAAK,CAAA,CAAA,CAAA,CAAA;;;;;oBAM9B,GAAA,CAWO,MAAA,EAAA,GAAA,CAAA,EAXD,KAAK,EAAC,yBAAyB,EAAA,CAAA,EAAA;wBACpC,GAAA,CAGO,MAAA,EAAA,GAAA,CAAA;4BAHD,KAAK,EAAC,cAAc;4BAAE,OAAK,EAAE,IAAA,CAAA,aAAa;;4BAC/C,GAAA,CAA4E,MAAA,EAAA,GAAA,CAAA;gCAAtE,KAAK,EAAC,eAAe;gCAAE,KAAK,EAAA,GAAA,CAAE,GAAA,CAAA,EAAA,eAAA,EAAA,IAAA,CAAA,UAAA,EAAA,CAA+B,CAAA;;4BACnE,GAAA,CAA+C,MAAA,EAAA,GAAA,CAAA,EAAzC,KAAK,EAAC,WAAW,EAAA,CAAA,EAAA,GAAA,CAAI,IAAA,CAAA,UAAU,CAAA,EAAA,CAAA,CAAA,UAAA,CAAA;;wBAEtC,GAAA,CAKO,MAAA,EAAA,GAAA,CAAA,EALD,KAAK,EAAC,cAAc,EAAA,CAAA,EAAA;4BACzB,GAAA,CAAsC,MAAA,EAAA,GAAA,CAAA,EAAhC,KAAK,EAAC,eAAe,EAAA,CAAA,EAAC,KAAG,CAAA;4BAC/B,GAAA,CAEO,MAAA,EAAA,GAAA,CAAA;gCAFD,KAAK,EAAC,gBAAgB;gCAAE,OAAK,EAAE,IAAA,CAAA,iBAAiB;;gCACrD,GAAA,CAAmE,MAAA,EAAA,GAAA,CAAA,EAA7D,KAAK,EAAC,eAAe,EAAA,CAAA,EAAA,GAAA,CAAI,IAAI,CAAC,KAAK,CAAC,IAAA,CAAA,OAAO,GAAA,GAAA,CAAA,CAAA,GAAU,GAAC,EAAA,CAAA,CAAA,UAAA,CAAA;;;;2BAMnD,IAAA,CAAA,YAAY,CAAA;0BAAxB,GAAA,CAiDO,MAAA,EAAA,GAAA,CAAA;;4BAjDmB,KAAK,EAAC,mBAAmB;4BAAE,OAAK,EAAE,IAAA,CAAA,cAAc;;4BACzE,GAAA,CA+CO,MAAA,EAAA,GAAA,CAAA;gCA/CD,KAAK,EAAC,WAAW;gCAAE,OAAK,EAAE,IAAA,CAAA,eAAe;;gCAC9C,GAAA,CAEO,MAAA,EAAA,GAAA,CAAA,EAFD,KAAK,EAAC,kBAAkB,EAAA,CAAA,EAAA;oCAC7B,GAAA,CAA4C,MAAA,EAAA,GAAA,CAAA,EAAtC,KAAK,EAAC,iBAAiB,EAAA,CAAA,EAAC,SAAO,CAAA;;gCAGtC,GAAA,CAGO,MAAA,EAAA,GAAA,CAAA,EAHD,KAAK,EAAC,qBAAqB,EAAA,CAAA,EAAA;oCAChC,GAAA,CAAkF,MAAA,EAAA,GAAA,CAAA;wCAA5E,KAAK,EAAC,mBAAmB;wCAAE,KAAK,EAAA,GAAA,CAAE,GAAA,CAAA,EAAA,eAAA,EAAA,IAAA,CAAA,YAAA,EAAA,CAAiC,CAAA;;oCACzE,GAAA,CAAwD,MAAA,EAAA,GAAA,CAAA,EAAlD,KAAK,EAAC,kBAAkB,EAAA,CAAA,EAAA,GAAA,CAAI,IAAA,CAAA,YAAY,CAAA,EAAA,CAAA,CAAA,UAAA,CAAA;;gCAG/C,GAAA,CA2BO,MAAA,EAAA,GAAA,CAAA,EA3BD,KAAK,EAAC,cAAc,EAAA,CAAA,EAAA;oCAEzB,GAAA,CAMO,MAAA,EAAA,GAAA,CAAA,EAND,KAAK,EAAC,kBAAkB,EAAA,CAAA,EAAA;wCAC7B,GAAA,CAAgC,MAAA,EAAA,GAAA,CAAA,EAA1B,KAAK,EAAC,WAAW,EAAA,CAAA,EAAC,GAAC,CAAA;wCACzB,GAAA,CAC2B,iBAAA,EAAA,GAAA,CAAA;4CADnB,KAAK,EAAC,YAAY;4CAAE,GAAG,EAAE,CAAC;4CAAG,GAAG,EAAE,GAAG;4CAAG,IAAI,EAAE,CAAC;4CAAG,KAAK,EAAE,IAAA,CAAA,KAAK;4CACpE,QAAM,EAAE,IAAA,CAAA,aAAa;;wCACvB,GAAA,CAC6C,OAAA,EAAA,GAAA,CAAA;4CADtC,KAAK,EAAC,WAAW;4CAAC,IAAI,EAAC,QAAQ;4CAAE,KAAK,EAAE,IAAA,CAAA,KAAK,CAAC,QAAQ,IAAA;4CAC3D,OAAK,EAAE,IAAA,CAAA,YAAY;4CAAE,WAAW,EAAC,OAAO;;;oCAI3C,GAAA,CAMO,MAAA,EAAA,GAAA,CAAA,EAND,KAAK,EAAC,kBAAkB,EAAA,CAAA,EAAA;wCAC7B,GAAA,CAAgC,MAAA,EAAA,GAAA,CAAA,EAA1B,KAAK,EAAC,WAAW,EAAA,CAAA,EAAC,GAAC,CAAA;wCACzB,GAAA,CAC2B,iBAAA,EAAA,GAAA,CAAA;4CADnB,KAAK,EAAC,YAAY;4CAAE,GAAG,EAAE,CAAC;4CAAG,GAAG,EAAE,GAAG;4CAAG,IAAI,EAAE,CAAC;4CAAG,KAAK,EAAE,IAAA,CAAA,KAAK;4CACpE,QAAM,EAAE,IAAA,CAAA,aAAa;;wCACvB,GAAA,CAC6C,OAAA,EAAA,GAAA,CAAA;4CADtC,KAAK,EAAC,WAAW;4CAAC,IAAI,EAAC,QAAQ;4CAAE,KAAK,EAAE,IAAA,CAAA,KAAK,CAAC,QAAQ,IAAA;4CAC3D,OAAK,EAAE,IAAA,CAAA,YAAY;4CAAE,WAAW,EAAC,OAAO;;;oCAI3C,GAAA,CAMO,MAAA,EAAA,GAAA,CAAA,EAND,KAAK,EAAC,kBAAkB,EAAA,CAAA,EAAA;wCAC7B,GAAA,CAAgC,MAAA,EAAA,GAAA,CAAA,EAA1B,KAAK,EAAC,WAAW,EAAA,CAAA,EAAC,GAAC,CAAA;wCACzB,GAAA,CAC2B,iBAAA,EAAA,GAAA,CAAA;4CADnB,KAAK,EAAC,YAAY;4CAAE,GAAG,EAAE,CAAC;4CAAG,GAAG,EAAE,GAAG;4CAAG,IAAI,EAAE,CAAC;4CAAG,KAAK,EAAE,IAAA,CAAA,KAAK;4CACpE,QAAM,EAAE,IAAA,CAAA,aAAa;;wCACvB,GAAA,CAC6C,OAAA,EAAA,GAAA,CAAA;4CADtC,KAAK,EAAC,WAAW;4CAAC,IAAI,EAAC,QAAQ;4CAAE,KAAK,EAAE,IAAA,CAAA,KAAK,CAAC,QAAQ,IAAA;4CAC3D,OAAK,EAAE,IAAA,CAAA,YAAY;4CAAE,WAAW,EAAC,OAAO;;;;gCAI5C,GAAA,CAOO,MAAA,EAAA,GAAA,CAAA,EAPD,KAAK,EAAC,mBAAmB,EAAA,CAAA,EAAA;oCAC9B,GAAA,CAEO,MAAA,EAAA,GAAA,CAAA;wCAFD,KAAK,EAAC,uBAAuB;wCAAE,OAAK,EAAE,IAAA,CAAA,cAAc;;wCACzD,GAAA,CAAuC,MAAA,EAAA,GAAA,CAAA,EAAjC,KAAK,EAAC,iBAAiB,EAAA,CAAA,EAAC,IAAE,CAAA;;oCAEjC,GAAA,CAEO,MAAA,EAAA,GAAA,CAAA;wCAFD,KAAK,EAAC,wBAAwB;wCAAE,OAAK,EAAE,IAAA,CAAA,gBAAgB;;wCAC5D,GAAA,CAAuC,MAAA,EAAA,GAAA,CAAA,EAAjC,KAAK,EAAC,iBAAiB,EAAA,CAAA,EAAC,IAAE,CAAA", "file": "components/main-color-picker/main-color-picker.uvue", "sourcesContent": ["<template>\r\n\t<!-- 弹窗遮罩层 -->\r\n\t<view v-if=\"visible\" class=\"picker-overlay\" @click=\"onOverlayClick\">\r\n\t\t<view class=\"picker-modal\" @click.stop=\"\">\r\n\t\t\t<view class=\"color-picker-container\">\r\n\t\t\t\t<!-- 导航栏 -->\r\n\t\t\t\t<view class=\"navbar\">\r\n\t\t\t\t\t<text class=\"nav-btn cancel-btn\" @click=\"onCancel\">取消</text>\r\n\t\t\t\t\t<text class=\"nav-title\">颜色选择</text>\r\n\t\t\t\t\t<view class=\"confirm-btn-container\">\r\n\t\t\t\t\t\t<text class=\"nav-btn confirm-btn\" @click=\"onConfirm\">确定</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 颜色系列选择按钮 -->\r\n\t\t\t\t<view class=\"color-series-section\">\r\n\t\t\t\t\t<view class=\"color-series-buttons\">\r\n\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\tv-for=\"(series, index) in colorSeriesList\"\r\n\t\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\t\tclass=\"series-button\"\r\n\t\t\t\t\t\t\t:class=\"{\r\n\t\t\t\t\t\t\t\t'active': selectedSeriesIndex == index,\r\n\t\t\t\t\t\t\t\t'random-button': index == 0,\r\n\t\t\t\t\t\t\t\t'normal-button': index != 0\r\n\t\t\t\t\t\t\t}\"\r\n\t\t\t\t\t\t\t:style=\"{ backgroundColor: series.color }\"\r\n\t\t\t\t\t\t\t@click=\"onSeriesSelect(index)\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<text class=\"series-text\">{{ series.name }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 颜色方块列表 -->\r\n\t\t\t\t<view class=\"color-grid-section\">\r\n\t\t\t\t\t<view class=\"color-grid\">\r\n\t\t\t\t\t\t<view v-for=\"(color, index) in colorList\" :key=\"index\" class=\"color-item\"\r\n\t\t\t\t\t\t\t:class=\"{ 'selected': selectedColorIndex == index }\" :style=\"{ backgroundColor: color }\"\r\n\t\t\t\t\t\t\t@click=\"onColorSelect(index)\">\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 预览和透明度选择区域 -->\r\n\t\t\t\t<view class=\"preview-opacity-section\">\r\n\t\t\t\t\t<view class=\"preview-area\" @click=\"showRGBPicker\">\r\n\t\t\t\t\t\t<view class=\"preview-color\" :style=\"{ backgroundColor: finalColor }\"></view>\r\n\t\t\t\t\t\t<text class=\"rgba-text\">{{ finalColor }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"opacity-area\">\r\n\t\t\t\t\t\t<text class=\"opacity-label\">透明度</text>\r\n\t\t\t\t\t\t<view class=\"opacity-button\" @click=\"showOpacityPicker\">\r\n\t\t\t\t\t\t\t<text class=\"opacity-value\">{{ Math.round(opacity * 100) }}%</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- RGB设置弹窗 -->\r\n\t\t\t\t<view v-if=\"showRGBModal\" class=\"rgb-modal-overlay\" @click=\"closeRGBPicker\">\r\n\t\t\t\t\t<view class=\"rgb-modal\" @click=\"onRGBModalClick\">\r\n\t\t\t\t\t\t<view class=\"rgb-modal-header\">\r\n\t\t\t\t\t\t\t<text class=\"rgb-modal-title\">RGB颜色设置</text>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"rgb-preview-section\">\r\n\t\t\t\t\t\t\t<view class=\"rgb-preview-color\" :style=\"{ backgroundColor: tempRGBColor }\"></view>\r\n\t\t\t\t\t\t\t<text class=\"rgb-preview-text\">{{ tempRGBColor }}</text>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"rgb-controls\">\r\n\t\t\t\t\t\t\t<!-- R值控制 -->\r\n\t\t\t\t\t\t\t<view class=\"rgb-control-item\">\r\n\t\t\t\t\t\t\t\t<text class=\"rgb-label\">R</text>\r\n\t\t\t\t\t\t\t\t<slider class=\"rgb-slider\" :min=\"0\" :max=\"255\" :step=\"1\" :value=\"tempR\"\r\n\t\t\t\t\t\t\t\t\t@change=\"onTempRChange\" />\r\n\t\t\t\t\t\t\t\t<input class=\"rgb-input\" type=\"number\" :value=\"tempR.toString()\"\r\n\t\t\t\t\t\t\t\t\t@input=\"onTempRInput\" placeholder=\"0-255\" />\r\n\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t\t<!-- G值控制 -->\r\n\t\t\t\t\t\t\t<view class=\"rgb-control-item\">\r\n\t\t\t\t\t\t\t\t<text class=\"rgb-label\">G</text>\r\n\t\t\t\t\t\t\t\t<slider class=\"rgb-slider\" :min=\"0\" :max=\"255\" :step=\"1\" :value=\"tempG\"\r\n\t\t\t\t\t\t\t\t\t@change=\"onTempGChange\" />\r\n\t\t\t\t\t\t\t\t<input class=\"rgb-input\" type=\"number\" :value=\"tempG.toString()\"\r\n\t\t\t\t\t\t\t\t\t@input=\"onTempGInput\" placeholder=\"0-255\" />\r\n\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t\t<!-- B值控制 -->\r\n\t\t\t\t\t\t\t<view class=\"rgb-control-item\">\r\n\t\t\t\t\t\t\t\t<text class=\"rgb-label\">B</text>\r\n\t\t\t\t\t\t\t\t<slider class=\"rgb-slider\" :min=\"0\" :max=\"255\" :step=\"1\" :value=\"tempB\"\r\n\t\t\t\t\t\t\t\t\t@change=\"onTempBChange\" />\r\n\t\t\t\t\t\t\t\t<input class=\"rgb-input\" type=\"number\" :value=\"tempB.toString()\"\r\n\t\t\t\t\t\t\t\t\t@input=\"onTempBInput\" placeholder=\"0-255\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"rgb-modal-buttons\">\r\n\t\t\t\t\t\t\t<view class=\"rgb-button rgb-cancel\" @click=\"closeRGBPicker\">\r\n\t\t\t\t\t\t\t\t<text class=\"rgb-button-text\">取消</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"rgb-button rgb-confirm\" @click=\"confirmRGBPicker\">\r\n\t\t\t\t\t\t\t\t<text class=\"rgb-button-text\">确定</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t// 定义颜色类型\r\n\ttype ColorInfo = {\r\n\t\tr : number,\r\n\t\tg : number,\r\n\t\tb : number\r\n\t}\r\n\ttype RGBAValues = {\r\n\t  r: number,\r\n\t  g: number,\r\n\t  b: number,\r\n\t  a: number\r\n\t}\r\n\ttype RGBValues = {\r\n\t  r: number,\r\n\t  g: number,\r\n\t  b: number\r\n\t}\r\n\ttype ColorSeries = {\r\n\t  name: string,\r\n\t  color: string\r\n\t}\r\n\texport default {\r\n\t\tname: \"main-color-picker\",\r\n\t\temits: ['cancel', 'confirm'],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t// 控制弹窗显示\r\n\t\t\t\tvisible: false as boolean,\r\n\t\t\t\t// 当前选中的颜色系列索引\r\n\t\t\t\tselectedSeriesIndex: 0 as number,\r\n\t\t\t\t// 透明度，范围0-1\r\n\t\t\t\topacity: 1.0 as number,\r\n\t\t\t\t// 当前选中的颜色索引\r\n\t\t\t\tselectedColorIndex: 0 as number,\r\n\t\t\t\t// 基础颜色（可以根据需要修改）\r\n\t\t\t\tbaseColor: { r: 255.0, g: 0.0, b: 0.0 } as ColorInfo,\r\n\t\t\t\t// 随机色种子，用于重新生成随机色\r\n\t\t\t\trandomSeed: 0 as number,\r\n\t\t\t\t// RGB设置弹窗相关\r\n\t\t\t\tshowRGBModal: false as boolean,\r\n\t\t\t\ttempR: 255 as number,\r\n\t\t\t\ttempG: 0 as number,\r\n\t\t\t\ttempB: 0 as number,\r\n\t\t\t\t// 自定义颜色\r\n\t\t\t\tcustomColor: \"\" as string\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t// 颜色系列列表\r\n\t\t\tcolorSeriesList(): ColorSeries[] {\r\n\t\t\t\treturn [\r\n\t\t\t\t\t{ name: \"随机色\", color: \"#FF6B35\" },\r\n\t\t\t\t\t{ name: \"黑白灰\", color: \"#808080\" },\r\n\t\t\t\t\t{ name: \"红色\", color: \"#FF4444\" },\r\n\t\t\t\t\t{ name: \"橙色\", color: \"#FF8844\" },\r\n\t\t\t\t\t{ name: \"黄色\", color: \"#FFDD44\" },\r\n\t\t\t\t\t{ name: \"绿色\", color: \"#44FF44\" },\r\n\t\t\t\t\t{ name: \"青色\", color: \"#44FFFF\" },\r\n\t\t\t\t\t{ name: \"蓝色\", color: \"#4444FF\" },\r\n\t\t\t\t\t{ name: \"紫色\", color: \"#AA44FF\" },\r\n\t\t\t\t\t{ name: \"粉色\", color: \"#FF88CC\" },\r\n\t\t\t\t\t{ name: \"棕色\", color: \"#AA6644\" }\r\n\t\t\t\t]\r\n\t\t\t},\r\n\r\n\t\t\t// 根据选中的系列生成120个颜色（10行12列）\r\n\t\t\tcolorList() : string[] {\r\n\t\t\t\tconst colors : string[] = []\r\n\r\n\t\t\t\tfor (let i = 0; i < 120; i++) {\r\n\t\t\t\t\tconst row = Math.floor(i / 12) // 当前行（0-9）\r\n\t\t\t\t\tconst col = i % 12 // 当前列（0-11）\r\n\r\n\t\t\t\t\t// 计算位置因子\r\n\t\t\t\t\tconst rowFactor = row / 9.0 // 行因子 0-1\r\n\t\t\t\t\tconst colFactor = col / 11.0 // 列因子 0-1\r\n\r\n\t\t\t\t\t// 基于选中的系列索引确定颜色系列\r\n\t\t\t\t\tlet r: number, g: number, b: number\r\n\r\n\t\t\t\t\tif (this.selectedSeriesIndex == 0) {\r\n\t\t\t\t\t\t// 随机色系列 - 每个方块完全随机的RGB值\r\n\t\t\t\t\t\tconst seed1 = (row * 12 + col + this.randomSeed) * 0.1\r\n\t\t\t\t\t\tconst seed2 = (row * 12 + col + this.randomSeed + 100) * 0.13\r\n\t\t\t\t\t\tconst seed3 = (row * 12 + col + this.randomSeed + 200) * 0.17\r\n\t\t\t\t\t\tr = Math.round((Math.sin(seed1) * 0.5 + 0.5) * 255)\r\n\t\t\t\t\t\tg = Math.round((Math.sin(seed2) * 0.5 + 0.5) * 255)\r\n\t\t\t\t\t\tb = Math.round((Math.sin(seed3) * 0.5 + 0.5) * 255)\r\n\t\t\t\t\t} else if (this.selectedSeriesIndex == 1) {\r\n\t\t\t\t\t\t// 黑白灰系列 - 更细腻的灰度变化\r\n\t\t\t\t\t\tconst totalFactor = (row * 12 + col) / 119.0 // 0到1的完整渐变\r\n\t\t\t\t\t\tconst grayValue = Math.round(totalFactor * 255)\r\n\t\t\t\t\t\tr = grayValue\r\n\t\t\t\t\t\tg = grayValue\r\n\t\t\t\t\t\tb = grayValue\r\n\t\t\t\t\t} else if (this.selectedSeriesIndex == 2) {\r\n\t\t\t\t\t\t// 红色系列 - 更丰富的红色变化\r\n\t\t\t\t\t\tconst totalFactor = (row * 12 + col) / 119.0 // 0到1\r\n\t\t\t\t\t\tconst brightness = 0.2 + totalFactor * 0.8 // 0.2-1.0的亮度范围\r\n\t\t\t\t\t\tconst saturation = 0.3 + (1 - Math.abs(totalFactor - 0.5) * 2) * 0.7 // 中间饱和度高\r\n\t\t\t\t\t\tr = Math.round(brightness * 255)\r\n\t\t\t\t\t\tg = Math.round(brightness * (1 - saturation) * 255)\r\n\t\t\t\t\t\tb = Math.round(brightness * (1 - saturation) * 255)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 其他颜色系列 - 确保包含纯色且避免黑色\r\n\t\t\t\t\t\tconst totalFactor = (row * 12 + col) / 119.0 // 0到1\r\n\r\n\t\t\t\t\t\t// 根据系列索引确定基础色相\r\n\t\t\t\t\t\tlet baseHue: number\r\n\t\t\t\t\t\tif (this.selectedSeriesIndex == 3) baseHue = 30      // 橙色\r\n\t\t\t\t\t\telse if (this.selectedSeriesIndex == 4) baseHue = 60 // 黄色\r\n\t\t\t\t\t\telse if (this.selectedSeriesIndex == 5) baseHue = 120 // 绿色\r\n\t\t\t\t\t\telse if (this.selectedSeriesIndex == 6) baseHue = 180 // 青色\r\n\t\t\t\t\t\telse if (this.selectedSeriesIndex == 7) baseHue = 240 // 蓝色\r\n\t\t\t\t\t\telse if (this.selectedSeriesIndex == 8) baseHue = 300 // 紫色\r\n\t\t\t\t\t\telse if (this.selectedSeriesIndex == 9) baseHue = 330 // 粉色\r\n\t\t\t\t\t\telse baseHue = 25 // 棕色\r\n\r\n\t\t\t\t\t\t// 色相微调：在基础色相±10度范围内变化\r\n\t\t\t\t\t\tconst hue = baseHue + (colFactor - 0.5) * 20\r\n\r\n\t\t\t\t\t\t// 创造三种类型的颜色变化\r\n\t\t\t\t\t\tif (totalFactor < 0.4) {\r\n\t\t\t\t\t\t\t// 前40%：深色调 - 高饱和度，中低明度（避免太暗）\r\n\t\t\t\t\t\t\tconst localFactor = totalFactor / 0.4\r\n\t\t\t\t\t\t\tconst saturation = 0.8 + localFactor * 0.2 // 0.8-1.0\r\n\t\t\t\t\t\t\tconst value = 0.4 + localFactor * 0.3 // 0.4-0.7（避免太暗）\r\n\t\t\t\t\t\t\tconst rgb = this.hsvToRgb(hue, saturation, value)\r\n\t\t\t\t\t\t\tr = rgb.r\r\n\t\t\t\t\t\t\tg = rgb.g\r\n\t\t\t\t\t\t\tb = rgb.b\r\n\t\t\t\t\t\t} else if (totalFactor < 0.6) {\r\n\t\t\t\t\t\t\t// 中20%：纯色调 - 最高饱和度，最佳明度\r\n\t\t\t\t\t\t\tconst localFactor = (totalFactor - 0.4) / 0.2\r\n\t\t\t\t\t\t\tconst saturation = 1.0 // 最高饱和度\r\n\t\t\t\t\t\t\tconst value = 0.8 + localFactor * 0.2 // 0.8-1.0（确保亮度足够）\r\n\t\t\t\t\t\t\tconst rgb = this.hsvToRgb(hue, saturation, value)\r\n\t\t\t\t\t\t\tr = rgb.r\r\n\t\t\t\t\t\t\tg = rgb.g\r\n\t\t\t\t\t\t\tb = rgb.b\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t// 后40%：浅色调 - 降低饱和度，保持高明度\r\n\t\t\t\t\t\t\tconst localFactor = (totalFactor - 0.6) / 0.4\r\n\t\t\t\t\t\t\tconst saturation = 0.8 - localFactor * 0.6 // 0.8-0.2（逐渐降低饱和度）\r\n\t\t\t\t\t\t\tconst value = 0.9 + localFactor * 0.1 // 0.9-1.0（保持高明度）\r\n\t\t\t\t\t\t\tconst rgb = this.hsvToRgb(hue, saturation, value)\r\n\t\t\t\t\t\t\tr = rgb.r\r\n\t\t\t\t\t\t\tg = rgb.g\r\n\t\t\t\t\t\t\tb = rgb.b\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 确保RGB值在0-255范围内\r\n\t\t\t\t\tr = Math.max(0, Math.min(255, r))\r\n\t\t\t\t\tg = Math.max(0, Math.min(255, g))\r\n\t\t\t\t\tb = Math.max(0, Math.min(255, b))\r\n\r\n\t\t\t\t\tcolors.push(`rgb(${r}, ${g}, ${b})`)\r\n\t\t\t\t}\r\n\r\n\t\t\t\treturn colors\r\n\t\t\t},\r\n\r\n\r\n\r\n\t\t\t// 最终的RGBA颜色值\r\n\t\t\tfinalColor() : string {\r\n\t\t\t\t// 优先使用自定义颜色\r\n\t\t\t\tlet colorToUse = \"\"\r\n\t\t\t\tif (this.customColor != \"\") {\r\n\t\t\t\t\tcolorToUse = this.customColor\r\n\t\t\t\t} else if (this.colorList.length > this.selectedColorIndex) {\r\n\t\t\t\t\tcolorToUse = this.colorList[this.selectedColorIndex]\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (colorToUse != \"\") {\r\n\t\t\t\t\t// 提取RGB值并添加透明度\r\n\t\t\t\t\tconst rgbMatch = colorToUse.match(/rgb\\((\\d+),\\s*(\\d+),\\s*(\\d+)\\)/)\r\n\t\t\t\t\tif (rgbMatch != null) {\r\n\t\t\t\t\t\tconst r = parseInt(rgbMatch[1] as string)\r\n\t\t\t\t\t\tconst g = parseInt(rgbMatch[2] as string)\r\n\t\t\t\t\t\tconst b = parseInt(rgbMatch[3] as string)\r\n\t\t\t\t\t\treturn `rgba(${r}, ${g}, ${b}, ${this.opacity})`\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\treturn `rgba(255, 0, 0, ${this.opacity})`\r\n\t\t\t},\r\n\r\n\t\t\t// 临时RGB颜色预览\r\n\t\t\ttempRGBColor() : string {\r\n\t\t\t\treturn `rgb(${this.tempR}, ${this.tempG}, ${this.tempB})`\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 颜色系列选择事件\r\n\t\t\tonSeriesSelect(index: number) {\r\n\t\t\t\tthis.selectedSeriesIndex = index\r\n\t\t\t\tthis.selectedColorIndex = 0 // 重置选中的颜色\r\n\t\t\t\tthis.customColor = \"\" // 清除自定义颜色\r\n\r\n\t\t\t\t// 如果选择的是随机色系列，生成新的随机种子\r\n\t\t\t\tif (index == 0) { // 随机色现在是第1个按钮，索引为0\r\n\t\t\t\t\tthis.randomSeed = Math.floor(Math.random() * 1000)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 显示透明度选择器\r\n\t\t\tshowOpacityPicker() {\r\n\t\t\t\tconst opacityOptions = [\r\n\t\t\t\t\t'100%', '95%', '90%', '85%', '80%', '75%', '70%', '65%', '60%', '55%',\r\n\t\t\t\t\t'50%', '45%', '40%', '35%', '30%', '25%', '20%', '15%', '10%', '5%'\r\n\t\t\t\t]\r\n\r\n\t\t\t\tuni.showActionSheet({\r\n\t\t\t\t\titemList: opacityOptions,\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tconst selectedOpacity = (100 - res.tapIndex * 5) / 100\r\n\t\t\t\t\t\tthis.opacity = selectedOpacity\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\r\n\t\t\t// 颜色选择事件\r\n\t\t\tonColorSelect(index : number) {\r\n\t\t\t\tthis.selectedColorIndex = index\r\n\t\t\t\t// 清除自定义颜色，使用新选中的颜色\r\n\t\t\t\tthis.customColor = \"\"\r\n\t\t\t},\r\n\r\n\t\t\t// 显示RGB设置弹窗\r\n\t\t\tshowRGBPicker() {\r\n\t\t\t\t// 获取当前颜色的RGB值（优先使用自定义颜色）\r\n\t\t\t\tlet colorToUse = \"\"\r\n\t\t\t\tif (this.customColor != \"\") {\r\n\t\t\t\t\tcolorToUse = this.customColor\r\n\t\t\t\t} else if (this.colorList.length > this.selectedColorIndex) {\r\n\t\t\t\t\tcolorToUse = this.colorList[this.selectedColorIndex]\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (colorToUse != \"\") {\r\n\t\t\t\t\tconst rgbMatch = colorToUse.match(/rgb\\((\\d+),\\s*(\\d+),\\s*(\\d+)\\)/)\r\n\t\t\t\t\tif (rgbMatch != null) {\r\n\t\t\t\t\t\tthis.tempR = parseInt(rgbMatch[1] as string)\r\n\t\t\t\t\t\tthis.tempG = parseInt(rgbMatch[2] as string)\r\n\t\t\t\t\t\tthis.tempB = parseInt(rgbMatch[3] as string)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.tempR = 255\r\n\t\t\t\t\t\tthis.tempG = 0\r\n\t\t\t\t\t\tthis.tempB = 0\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.tempR = 255\r\n\t\t\t\t\tthis.tempG = 0\r\n\t\t\t\t\tthis.tempB = 0\r\n\t\t\t\t}\r\n\t\t\t\tthis.showRGBModal = true\r\n\t\t\t},\r\n\r\n\t\t\t// 关闭RGB设置弹窗\r\n\t\t\tcloseRGBPicker() {\r\n\t\t\t\tthis.showRGBModal = false\r\n\t\t\t},\r\n\r\n\t\t\t// RGB弹窗点击事件（阻止冒泡）\r\n\t\t\tonRGBModalClick() {\r\n\t\t\t\t// 空方法，用于阻止事件冒泡\r\n\t\t\t},\r\n\r\n\t\t\t// 确认RGB设置\r\n\t\t\tconfirmRGBPicker() {\r\n\t\t\t\t// 设置自定义颜色\r\n\t\t\t\tthis.customColor = `rgb(${this.tempR}, ${this.tempG}, ${this.tempB})`\r\n\t\t\t\tthis.showRGBModal = false\r\n\t\t\t},\r\n\r\n\t\t\t// R值滑块变化\r\n\t\t\tonTempRChange(event: UniSliderChangeEvent) {\r\n\t\t\t\tthis.tempR = event.detail.value as number\r\n\t\t\t},\r\n\r\n\t\t\t// G值滑块变化\r\n\t\t\tonTempGChange(event: UniSliderChangeEvent) {\r\n\t\t\t\tthis.tempG = event.detail.value as number\r\n\t\t\t},\r\n\r\n\t\t\t// B值滑块变化\r\n\t\t\tonTempBChange(event: UniSliderChangeEvent) {\r\n\t\t\t\tthis.tempB = event.detail.value as number\r\n\t\t\t},\r\n\r\n\t\t\t// R值输入框变化\r\n\t\t\tonTempRInput(event: UniInputEvent) {\r\n\t\t\t\tconst value = parseInt(event.detail.value)\r\n\t\t\t\tif (!isNaN(value)) {\r\n\t\t\t\t\tthis.tempR = Math.max(0, Math.min(255, value))\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// G值输入框变化\r\n\t\t\tonTempGInput(event: UniInputEvent) {\r\n\t\t\t\tconst value = parseInt(event.detail.value)\r\n\t\t\t\tif (!isNaN(value)) {\r\n\t\t\t\t\tthis.tempG = Math.max(0, Math.min(255, value))\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// B值输入框变化\r\n\t\t\tonTempBInput(event: UniInputEvent) {\r\n\t\t\t\tconst value = parseInt(event.detail.value)\r\n\t\t\t\tif (!isNaN(value)) {\r\n\t\t\t\t\tthis.tempB = Math.max(0, Math.min(255, value))\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 打开弹窗\r\n\t\t\topen() {\r\n\t\t\t\tthis.visible = true\r\n\t\t\t},\r\n\r\n\t\t\t// 关闭弹窗\r\n\t\t\tclose() {\r\n\t\t\t\tthis.visible = false\r\n\t\t\t},\r\n\r\n\t\t\t// 点击遮罩层关闭弹窗\r\n\t\t\tonOverlayClick() {\r\n\t\t\t\tthis.close()\r\n\t\t\t\tthis.$emit('cancel')\r\n\t\t\t},\r\n\r\n\t\t\t// 取消按钮点击事件\r\n\t\t\tonCancel() {\r\n\t\t\t\tthis.close()\r\n\t\t\t\tthis.$emit('cancel')\r\n\t\t\t},\r\n\r\n\t\t\t// 确定按钮点击事件\r\n\t\t\tonConfirm() {\r\n\t\t\t\tthis.close()\r\n\t\t\t\tconst rgbaValues = this.getRGBAValues()\r\n\t\t\t\tthis.$emit('confirm', {\r\n\t\t\t\t\tcolor: this.finalColor,\r\n\t\t\t\t\trgba: rgbaValues,\r\n\t\t\t\t\thex: this.rgbToHex(rgbaValues.r, rgbaValues.g, rgbaValues.b)\r\n\t\t\t\t})\r\n\t\t\t},\r\n \r\n\t\t\t// 获取RGBA数值\r\n\t\t\tgetRGBAValues() : RGBAValues {\r\n\t\t\t\tconst rgbaMatch = this.finalColor.match(/rgba\\((\\d+),\\s*(\\d+),\\s*(\\d+),\\s*([\\d.]+)\\)/)\r\n\t\t\t\tif (rgbaMatch != null) {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tr: parseInt(rgbaMatch[1] as string),\r\n\t\t\t\t\t\tg: parseInt(rgbaMatch[2] as string),\r\n\t\t\t\t\t\tb: parseInt(rgbaMatch[3] as string),\r\n\t\t\t\t\t\ta: parseFloat(rgbaMatch[4] as string)\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\treturn { r: 255, g: 0, b: 0, a: 1.0 }\r\n\t\t\t},\r\n\r\n\t\t\t// HSV转RGB\r\n\t\t\thsvToRgb(h: number, s: number, v: number): RGBValues {\r\n\t\t\t\tconst c: number = v * s\r\n\t\t\t\tconst x: number = c * (1.0 - Math.abs(((h / 60.0) % 2.0) - 1.0))\r\n\t\t\t\tconst m: number = v - c\r\n\r\n\t\t\t\tlet r: number = 0.0\r\n\t\t\t\tlet g: number = 0.0\r\n\t\t\t\tlet b: number = 0.0\r\n\r\n\t\t\t\tif (h >= 0 && h < 60) {\r\n\t\t\t\t\tr = c\r\n\t\t\t\t\tg = x\r\n\t\t\t\t\tb = 0.0\r\n\t\t\t\t} else if (h >= 60 && h < 120) {\r\n\t\t\t\t\tr = x\r\n\t\t\t\t\tg = c\r\n\t\t\t\t\tb = 0.0\r\n\t\t\t\t} else if (h >= 120 && h < 180) {\r\n\t\t\t\t\tr = 0.0\r\n\t\t\t\t\tg = c\r\n\t\t\t\t\tb = x\r\n\t\t\t\t} else if (h >= 180 && h < 240) {\r\n\t\t\t\t\tr = 0.0\r\n\t\t\t\t\tg = x\r\n\t\t\t\t\tb = c\r\n\t\t\t\t} else if (h >= 240 && h < 300) {\r\n\t\t\t\t\tr = x\r\n\t\t\t\t\tg = 0.0\r\n\t\t\t\t\tb = c\r\n\t\t\t\t} else if (h >= 300 && h < 360) {\r\n\t\t\t\t\tr = c\r\n\t\t\t\t\tg = 0.0\r\n\t\t\t\t\tb = x\r\n\t\t\t\t}\r\n\r\n\t\t\t\tconst result: RGBValues = {\r\n\t\t\t\t\tr: Math.round((r + m) * 255.0),\r\n\t\t\t\t\tg: Math.round((g + m) * 255.0),\r\n\t\t\t\t\tb: Math.round((b + m) * 255.0)\r\n\t\t\t\t}\r\n\t\t\t\treturn result\r\n\t\t\t},\r\n\r\n\t\t\t// RGB转十六进制\r\n\t\t\trgbToHex(r: number, g: number, b: number): string {\r\n\t\t\t\tconst toHex = (value: number): string => {\r\n\t\t\t\t\tconst hex = Math.round(Math.max(0, Math.min(255, value))).toString(16)\r\n\t\t\t\t\treturn hex.length == 1 ? '0' + hex : hex\r\n\t\t\t\t}\r\n\t\t\t\treturn '#' + toHex(r) + toHex(g) + toHex(b)\r\n\t\t\t},\r\n\r\n\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t/* 弹窗遮罩层 */\r\n\t.picker-overlay {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.5);\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: flex-end;\r\n\t\tz-index: 1000;\r\n\t}\r\n\r\n\t.picker-modal {\r\n\t\twidth: 100%;\r\n\t\tbackground-color: #ffffff;\r\n\t\tborder-top-left-radius: 20rpx;\r\n\t\tborder-top-right-radius: 20rpx;\r\n\t\toverflow: hidden;\r\n\t\tbox-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\r\n\t}\r\n\r\n\t.color-picker-container {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tbackground-color: #ffffff;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\r\n\t/* 导航栏样式 */\r\n\t.navbar {\r\n\t\theight: 44px;\r\n\t\tbackground-color: #f8f8f8;\r\n\t\tborder-bottom: 1px solid #e5e5e5;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tpadding: 0 10px;\r\n\t}\r\n\r\n\t.nav-btn {\r\n\t\tfont-size: 16px;\r\n\t\tcolor: #007aff;\r\n\t\tpadding: 8px 12px;\r\n\t}\r\n\r\n\t.cancel-btn {\r\n\t\tcolor: #999999;\r\n\t}\r\n\r\n\t.confirm-btn-container {\r\n\t\theight: 30px;\r\n\t\tbackground-color: #007aff;\r\n\t\tborder-radius: 8rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);\r\n\t}\r\n\r\n\t.confirm-btn {\r\n\t\tcolor: #ffffff;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.nav-title {\r\n\t\tfont-size: 17px;\r\n\t\tcolor: #333333;\r\n\t}\r\n\r\n\t/* 区域标题样式 */\r\n\t.section-title {\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #666666;\r\n\t\tmargin-bottom: 10px;\r\n\t}\r\n\r\n\t/* 颜色系列选择区域 */\r\n\t.color-series-section {\r\n\t\tpadding: 20rpx;\r\n\t\tborder-bottom: 1px solid #f0f0f0;\r\n\t}\r\n\r\n\t.color-series-buttons {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: space-between;\r\n\t\tmargin-top: 10rpx;\r\n\t\tpadding: 0 10rpx;\r\n\t}\r\n\r\n\t.series-button {\r\n\t\theight: 60rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t\tborder: 2px solid transparent;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tmargin-bottom: 10rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t/* 随机色按钮：两个普通按钮宽度 + 间距 */\r\n\t.random-button {\r\n\t\twidth: 220rpx;\r\n\t}\r\n\r\n\t/* 其他按钮正常宽度 */\r\n\t.normal-button {\r\n\t\twidth: 100rpx;\r\n\t}\r\n\r\n\t.series-button.active {\r\n\t\tborder-color: #007aff;\r\n\t\tbox-shadow: 0 0 0 1px #007aff;\r\n\t}\r\n\r\n\t.series-text {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #ffffff;\r\n\t\tfont-weight: bold;\r\n\t\ttext-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n\t}\r\n\r\n\t/* 颜色网格区域 */\r\n\t.color-grid-section {\r\n\t\tpadding: 20rpx;\r\n\t\tborder-bottom: 1px solid #f0f0f0;\r\n\t}\r\n\r\n\t.color-grid {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: space-around;\r\n\t\talign-items: flex-start;\r\n\t\tpadding: 15rpx;\r\n\t}\r\n\r\n\t.color-item {\r\n\t\twidth: 55rpx;\r\n\t\theight: 40rpx;\r\n\t\tborder-radius: 4px;\r\n\t\tborder: 2px solid transparent;\r\n\t\tmargin-bottom: 4px;\r\n\t\tflex-shrink: 0;\r\n\t\tflex-grow: 0;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.color-item.selected {\r\n\t\tborder-color: #007aff;\r\n\t\tbox-shadow: 0 0 0 1px #007aff;\r\n\t}\r\n\r\n\t/* 预览和透明度选择区域 */\r\n\t.preview-opacity-section {\r\n\t\tpadding: 15rpx 20rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tborder-bottom: 1px solid #f0f0f0;\r\n\t}\r\n\r\n\t.preview-area {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.preview-color {\r\n\t\twidth: 60rpx;\r\n\t\theight: 60rpx;\r\n\t\tborder-radius: 30rpx;\r\n\t\tborder: 1px solid #e5e5e5;\r\n\t\tmargin-right: 15rpx;\r\n\t\tbox-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n\t}\r\n\r\n\t.rgba-text {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #666666;\r\n\t\tfont-family: monospace;\r\n\t\tbackground-color: #f5f5f5;\r\n\t\tpadding: 8rpx 12rpx;\r\n\t\tborder-radius: 6rpx;\r\n\t}\r\n\r\n\t.opacity-area {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.opacity-label {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #666666;\r\n\t\tmargin-bottom: 8rpx;\r\n\t}\r\n\r\n\t.opacity-button {\r\n\t\tbackground-color: #007aff;\r\n\t\tpadding: 12rpx 20rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t\tborder: none;\r\n\t}\r\n\r\n\t.opacity-value {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #ffffff;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t/* RGB设置弹窗样式 */\r\n\t.rgb-modal-overlay {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.5);\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tz-index: 1000;\r\n\t}\r\n\r\n\t.rgb-modal {\r\n\t\tbackground-color: #ffffff;\r\n\t\tborder-radius: 12rpx;\r\n\t\twidth: 600rpx;\r\n\t\tmax-height: 800rpx;\r\n\t\tpadding: 30rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.rgb-modal-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.rgb-modal-title {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333333;\r\n\t}\r\n\r\n\t.rgb-preview-section {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tmargin-bottom: 25rpx;\r\n\t\tpadding: 15rpx;\r\n\t\tbackground-color: #f8f8f8;\r\n\t\tborder-radius: 8rpx;\r\n\t}\r\n\r\n\t.rgb-preview-color {\r\n\t\twidth: 60rpx;\r\n\t\theight: 60rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t\tborder: 1px solid #e5e5e5;\r\n\t\tmargin-right: 15rpx;\r\n\t}\r\n\r\n\t.rgb-preview-text {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #666666;\r\n\t\tfont-family: monospace;\r\n\t}\r\n\r\n\t.rgb-controls {\r\n\t\tmargin-bottom: 25rpx;\r\n\t}\r\n\r\n\t.rgb-control-item {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.rgb-label {\r\n\t\twidth: 40rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333333;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.rgb-slider {\r\n\t\tflex: 1;\r\n\t\tmargin: 0 15rpx;\r\n\t}\r\n\r\n\t.rgb-input {\r\n\t\twidth: 120rpx;\r\n\t\theight: 60rpx;\r\n\t\tborder: 1px solid #e5e5e5;\r\n\t\tborder-radius: 6rpx;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 24rpx;\r\n\t\tpadding: 0 10rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.rgb-modal-buttons {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\tjustify-content: space-between;\r\n\t}\r\n\r\n\t.rgb-button {\r\n\t\twidth: 45%;\r\n\t\theight: 70rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.rgb-cancel {\r\n\t\tbackground-color: #f5f5f5;\r\n\t\tborder: 1px solid #e5e5e5;\r\n\t}\r\n\r\n\t.rgb-confirm {\r\n\t\tbackground-color: #007aff;\r\n\t}\r\n\r\n\t.rgb-button-text {\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.rgb-cancel .rgb-button-text {\r\n\t\tcolor: #666666;\r\n\t}\r\n\r\n\t.rgb-confirm .rgb-button-text {\r\n\t\tcolor: #ffffff;\r\n\t}\r\n</style>"]}