<template>
	<view class="Mimage">
		<view class="Mimage-name" :style="{color:color}">
			{{data.name}}
		</view>
	
		<view class="Mimage-box qShadow1" :style="{backgroundColor:bgColor}" @click="selectImage">
			<view class="mb-defaultImage" v-if="mValue==''">
				<mb-icons type="Add-1" color="#666" size="60rpx"></mb-icons>
				
			</view>
			
			<view class="mb-showImage" v-else>
				<image :src="mValue" mode=""></image>
			</view>
		</view>
	
	</view>
	
	
	
</template>

<script>
	export default {
		name: "Mimage",
		props: {
			data: {
				type: Object,
				default: {}
			},
			keyName: {
				type: String,
				default: ""
			
			},
			index: {
				type: Number,
				default: 0
			},
			color: {
				type: String,
				default: '#000'
			},
			bgColor: {
				type: String,
				default: "#f1f4f9"
			
			}
		},
		created() {
			
			
			if(this.data.value!=''){
				this.mValue=this.data.value
			}
			
		
		},
		data() {
			return {
				mValue:""
			};
		},
		watch:{
			data: {
				
				handler(newValue, oldValue) {
					this.mValue= this.data.value
					
				},
				deep: true
			}
			
		},
		methods:{
			selectImage(){
				this.$mb.selectImage().then(res=>{
					this.mValue=res
					this.$emit("change",{index:this.index,value:res})
				})
			}
		}
	}
</script>

<style>
.Mimage {
		width: 100%;
		display: flex;
		flex-direction: column;
		padding: 0 20rpx;
		margin-bottom: 20rpx;
	
	}
	
	.Mimage-name {
		width: 100%;
		font-size: 32rpx;
		margin-bottom: 10rpx;
	}
	
	.Mimage-box {
		width: 150rpx;
		height: 150rpx;
		border-radius: 20rpx;
		overflow: hidden;
		background-color: #f2f4f8;
	}
	.mb-Imagebox{
		width: 150rpx;
		height: 150rpx;
		border-radius: 20rpx;
		overflow: hidden;
		background-color: #f2f4f8;
	}
	.mb-defaultImage{
		width: 100%;
		height: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
	}
	.mb-showImage{
		width: 100%;
		height: 100%;
	}
	.mb-showImage image{
		width: 100%;
		height: 100%;
	}
</style>