{"version": 3, "sources": ["components/main-form/components/form-time-picker.uvue", "components/main-form/components/form-input.uvue", "components/main-form/components/form-date-picker.uvue"], "sourcesContent": ["<template>\n\t<view class=\"form-time-picker-container\">\n\t\t<view class=\"form-time-picker-label\" :style=\"{ color: color }\">\n\t\t\t<text>{{ data.name }}</text>\n\t\t</view>\n\n\t\t<view class=\"form-time-picker-box\" :style=\"{ backgroundColor: bgColor }\" @click=\"showTimePicker\">\n\t\t\t<text class=\"form-time-picker-text\" :class=\"{ 'placeholder': selectedTime == '' }\">\n\t\t\t\t{{ selectedTime || data.tip || '请选择时间' }}\n\t\t\t</text>\n\t\t\t<text class=\"form-time-picker-icon\">🕐</text>\n\t\t</view>\n\t</view>\n</template>\n\n<script lang=\"uts\">\n\t// 表单项数据类型\n\ttype FormFieldData = {\n\t\tkey: string\n\t\tname: string\n\t\ttype: string\n\t\tvalue: any\n\t\ttip?: string\n\t\tisSave?: boolean\n\t}\n\n\texport default {\n\t\tname: \"FormTimePicker\",\n\t\tprops: {\n\t\t\tdata: {\n\t\t\t\ttype: Object as PropType<FormFieldData>,\n\t\t\t\tdefault: (): FormFieldData => ({\n\t\t\t\t\tkey: \"\",\n\t\t\t\t\tname: \"\",\n\t\t\t\t\ttype: \"time\",\n\t\t\t\t\tvalue: \"\"\n\t\t\t\t})\n\t\t\t},\n\t\t\tindex: {\n\t\t\t\ttype: Number,\n\t\t\t\tdefault: 0\n\t\t\t},\n\t\t\tcolor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#333333\"\n\t\t\t},\n\t\t\tbgColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#f8f9fa\"\n\t\t\t},\n\t\t\tkeyName: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"\"\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tselectedTime: \"\",\n\t\t\t\tsaveKey: \"\"\n\t\t\t}\n\t\t},\n\t\twatch: {\n\t\t\tdata: {\n\t\t\t\thandler(newValue: FormFieldData): void {\n\t\t\t\t\tthis.selectedTime = newValue.value || \"\"\n\t\t\t\t},\n\t\t\t\tdeep: true,\n\t\t\t\timmediate: true\n\t\t\t}\n\t\t},\n\t\tcreated(): void {\n\t\t\tthis.initializeValue()\n\t\t},\n\t\tmethods: {\n\t\t\t// 初始化值\n\t\t\tinitializeValue(): void {\n\t\t\t\tif (this.data.isSave == true && this.keyName != \"\") {\n\t\t\t\t\tthis.saveKey = this.keyName + \"_\" + this.data.key\n\t\t\t\t\t\n\t\t\t\t\t// 从存储中获取值\n\t\t\t\t\tuni.getStorage({\n\t\t\t\t\t\tkey: this.saveKey,\n\t\t\t\t\t\tsuccess: (res): void => {\n\t\t\t\t\t\t\tthis.selectedTime = res.data || \"\"\n\t\t\t\t\t\t\tthis.emitChange(res.data)\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: (): void => {\n\t\t\t\t\t\t\tthis.selectedTime = this.data.value || \"\"\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t} else {\n\t\t\t\t\tthis.selectedTime = this.data.value || \"\"\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 获取当前时间\n\t\t\tgetCurrentTime(): string {\n\t\t\t\tconst now = new Date()\n\t\t\t\tconst hours = String(now.getHours()).padStart(2, '0')\n\t\t\t\tconst minutes = String(now.getMinutes()).padStart(2, '0')\n\t\t\t\treturn `${hours}:${minutes}`\n\t\t\t},\n\n\t\t\t// 显示时间选择器\n\t\t\tshowTimePicker(): void {\n\t\t\t\t// 使用uni.showModal模拟时间选择器\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: this.data.name,\n\t\t\t\t\tcontent: \"请在系统时间选择器中选择时间\",\n\t\t\t\t\tshowCancel: true,\n\t\t\t\t\tsuccess: (res): void => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t// 这里应该调用系统时间选择器\n\t\t\t\t\t\t\t// 由于uni-app x的限制，这里使用简化实现\n\t\t\t\t\t\t\tconst currentTime = this.getCurrentTime()\n\t\t\t\t\t\t\tthis.handleTimeSelection(currentTime)\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\n\t\t\t// 处理时间选择结果\n\t\t\thandleTimeSelection(time: string): void {\n\t\t\t\tthis.selectedTime = time\n\t\t\t\tthis.emitChange(time)\n\t\t\t\tthis.saveValue(time)\n\t\t\t},\n\n\t\t\t// 发送变更事件\n\t\t\temitChange(value: string): void {\n\t\t\t\tthis.$emit(\"change\", {\n\t\t\t\t\tindex: this.index,\n\t\t\t\t\tvalue: value\n\t\t\t\t})\n\t\t\t},\n\n\t\t\t// 保存值到本地存储\n\t\t\tsaveValue(value: string): void {\n\t\t\t\tif (this.data.isSave == true && this.saveKey != \"\") {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tuni.setStorageSync(this.saveKey, value)\n\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\tconsole.error(\"保存数据失败:\", e)\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t.form-time-picker-container {\n\t\twidth: 100%;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tpadding: 0 20rpx;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.form-time-picker-label {\n\t\twidth: 100%;\n\t\tmargin-bottom: 10rpx;\n\t\tfont-size: 32rpx;\n\t}\n\n\t.form-time-picker-box {\n\t\twidth: 100%;\n\t\theight: 100rpx;\n\t\tborder: 1rpx solid #e0e0e0;\n\t\tbox-sizing: border-box;\n\t\tpadding: 0 20rpx;\n\t\tborder-radius: 20rpx;\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t}\n\n\t.form-time-picker-text {\n\t\tflex: 1;\n\t\tfont-size: 30rpx;\n\t\tcolor: #333333;\n\t}\n\n\t.form-time-picker-text.placeholder {\n\t\tcolor: #999999;\n\t}\n\n\t.form-time-picker-icon {\n\t\tfont-size: 32rpx;\n\t\tcolor: #666666;\n\t}\n</style>\n", null, null], "names": [], "mappings": ";;;;;;;;;;;;;+BA+FW;;+BAuCP;AA5GE;;kBA4CJ,OAAW,IAAG,CAAA;YACb,IAAI,CAAC,eAAe;QACrB;;;;;UATE,IAAQ,UAAU,cAAa,GAAG,IAAG,CAAA;YACpC,IAAI,CAAC,YAAW,GAAI,SAAS,KAAI,IAAK;QACvC;uBACA,OAAM,IAAI,EACV,YAAW,IAAG;;;;;;eAlEjB,IAWO,QAAA,IAXD,WAAM,+BAA4B;YACvC,IAEO,QAAA,IAFD,WAAM,0BAA0B,WAAK,IAAE,IAAA,WAAA,KAAA,KAAA;gBAC5C,IAA4B,QAAA,IAAA,EAAA,IAAnB,KAAA,IAAI,CAAC,IAAI,GAAA,CAAA;;YAGnB,IAKO,QAAA,IALD,WAAM,wBAAwB,WAAK,IAAE,IAAA,qBAAA,KAAA,OAAA,IAA+B,aAAO,KAAA,cAAc;gBAC9F,IAEO,QAAA,IAFD,WAAK,IAAA;oBAAC;oBAAgC,IAAA,kBAAA,KAAA,YAAA,IAAA;iBAAqC,QAC7E,KAAA,YAAY,IAAI,KAAA,IAAI,CAAC,GAAG,IAAA,UAAA,CAAA;gBAE5B,IAA6C,QAAA,IAAvC,WAAM,0BAAwB;;;;;;mBAoBV;;;;;aA2BzB;aACA;;;mBADA,kBAAc,IACd,aAAS;;aAiBV;aAAA,0BAAmB,IAAG,CAAA;QACrB,IAAI,IAAI,CAAC,IAAI,CAAC,MAAK,IAAK,IAAG,IAAK,IAAI,CAAC,OAAM,IAAK,IAAI;YACnD,IAAI,CAAC,OAAM,GAAI,IAAI,CAAC,OAAM,GAAI,MAAM,IAAI,CAAC,IAAI,CAAC,GAAE;YAGhD,iCACC,MAAK,IAAI,CAAC,OAAO,EACjB,UAAS,IAAC,MAAM,IAAG,CAAG;gBACrB,IAAI,CAAC,YAAW,GAAI,IAAI,IAAG,IAAK;gBAChC,IAAI,CAAC,UAAU,CAAC,IAAI,IAAI;YACzB,GACA,OAAM,QAAI,IAAG,CAAG;gBACf,IAAI,CAAC,YAAW,GAAI,IAAI,CAAC,IAAI,CAAC,KAAI,IAAK;YACxC;eAEK;YACN,IAAI,CAAC,YAAW,GAAI,IAAI,CAAC,IAAI,CAAC,KAAI,IAAK;;IAEzC;aAGA;aAAA,yBAAkB,MAAK,CAAA;QACtB,IAAM,MAAM,AAAI;QAChB,IAAM,QAAQ,OAAO,IAAI,QAAQ,IAAI,QAAQ,CAAC,CAAC,EAAE;QACjD,IAAM,UAAU,OAAO,IAAI,UAAU,IAAI,QAAQ,CAAC,CAAC,EAAE;QACrD,OAAO,KAAG,QAAK,MAAI;IACpB;aAGA;aAAA,yBAAkB,IAAG,CAAA;QAEpB,+BACC,QAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EACrB,UAAS,kBACT,aAAY,IAAI,EAChB,UAAS,IAAC,MAAM,IAAG,CAAG;YACrB,IAAI,IAAI,OAAO,EAAE;gBAGhB,IAAM,cAAc,IAAI,CAAC,cAAc;gBACvC,IAAI,CAAC,mBAAmB,CAAC;;QAE3B;;IAEF;aAGA;aAAA,2BAAoB,MAAM,MAAM,GAAG,IAAG,CAAA;QACrC,IAAI,CAAC,YAAW,GAAI;QACpB,IAAI,CAAC,UAAU,CAAC;QAChB,IAAI,CAAC,SAAS,CAAC;IAChB;aAGA;aAAA,kBAAW,OAAO,MAAM,GAAG,IAAG,CAAA;QAC7B,IAAI,CAAC,OAAK,CAAC,UAAU,IACpB,WAAO,IAAI,CAAC,KAAK,EACjB,WAAO;IAET;aAGA;aAAA,iBAAU,OAAO,MAAM,GAAG,IAAG,CAAA;QAC5B,IAAI,IAAI,CAAC,IAAI,CAAC,MAAK,IAAK,IAAG,IAAK,IAAI,CAAC,OAAM,IAAK,IAAI;YACnD,IAAI;gBACH,mBAAmB,IAAI,CAAC,OAAO,EAAE;;aAChC,OAAO,cAAG;gBACX,QAAQ,KAAK,CAAC,WAAW,GAAC;;;IAG7B;;mBAtHK;;;;;;;;;;;;;2EAIK,OAAI;mBAAiB,gBAC7B,MAAK,IACL,OAAM,IACN,OAAM,QACN,QAAO,GACP;;2DAIQ,CAAA,mDAIA,8DAIA,8DAIA;;;;;;;;;;AA+FZ"}