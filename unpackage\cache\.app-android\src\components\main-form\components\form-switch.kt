@file:Suppress("UNCHECKED_CAST", "USELESS_CAST", "INAPPLICABLE_JVM_NAME", "UNUSED_ANONYMOUS_PARAMETER", "NAME_SHADOWING", "UNNECESSARY_NOT_NULL_ASSERTION")
package uni.UNIC178CB1
import io.dcloud.uniapp.*
import io.dcloud.uniapp.extapi.*
import io.dcloud.uniapp.framework.*
import io.dcloud.uniapp.runtime.*
import io.dcloud.uniapp.vue.*
import io.dcloud.uniapp.vue.shared.*
import io.dcloud.unicloud.*
import io.dcloud.uts.*
import io.dcloud.uts.Map
import io.dcloud.uts.Set
import io.dcloud.uts.UTSAndroid
import io.dcloud.uniapp.extapi.getStorage as uni_getStorage
import io.dcloud.uniapp.extapi.setStorage as uni_setStorage
open class GenComponentsMainFormComponentsFormSwitch : VueComponent {
    constructor(__ins: ComponentInternalInstance) : super(__ins) {
        onCreated(fun(): Unit {
            val fieldObj = this.`$props`["data"] as FormFieldData
            this.initFieldData(fieldObj)
        }
        , __ins)
        this.`$watch`(fun(): Any? {
            return this.data
        }
        , fun(obj: FormFieldData) {
            val newValue = obj.value
            if (newValue !== this.fieldValue) {
                console.log("触发改变", " at components/main-form/components/form-switch.uvue:66")
                this.fieldValue = newValue
                this.updateSwitchValue()
            }
        }
        , WatchOptions(deep = true))
    }
    @Suppress("UNUSED_PARAMETER", "UNUSED_VARIABLE")
    override fun `$render`(): Any? {
        val _ctx = this
        val _cache = this.`$`.renderCache
        val _component_switch = resolveComponent("switch")
        val _component_form_container = resolveComponent("form-container")
        return _cV(_component_form_container, _uM("label" to _ctx.fieldName, "show-error" to _ctx.showError, "tip" to _ctx.tip, "error-message" to _ctx.errorMessage, "label-color" to _ctx.labelColor, "background-color" to _ctx.backgroundColor), _uM("input-content" to withSlotCtx(fun(): UTSArray<Any> {
            return _uA(
                _cE("view", _uM("class" to "switch-box"), _uA(
                    _cV(_component_switch, _uM("class" to "form-switch-element", "color" to _ctx.switchColor, "checked" to _ctx.switchValue, "onChange" to _ctx.handleChange), null, 8, _uA(
                        "color",
                        "checked",
                        "onChange"
                    ))
                ))
            )
        }
        ), "_" to 1), 8, _uA(
            "label",
            "show-error",
            "tip",
            "error-message",
            "label-color",
            "background-color"
        ))
    }
    open var data: FormFieldData? by `$props`
    open var index: Number by `$props`
    open var keyName: String by `$props`
    open var labelColor: String by `$props`
    open var backgroundColor: String by `$props`
    open var fieldName: String by `$data`
    open var fieldValue: Any? by `$data`
    open var isSave: Boolean by `$data`
    open var save_key: String by `$data`
    open var tip: String by `$data`
    open var varType: String by `$data`
    open var switchValue: Boolean by `$data`
    open var switchColor: String by `$data`
    open var showError: Boolean by `$data`
    open var errorMessage: String by `$data`
    @Suppress("USELESS_CAST")
    override fun data(): Map<String, Any?> {
        return _uM("fieldName" to "", "fieldValue" to null as Any?, "isSave" to false, "save_key" to "", "tip" to "", "varType" to "number", "switchValue" to false, "switchColor" to "#8A6DE9", "showError" to false, "errorMessage" to "")
    }
    open var initFieldData = ::gen_initFieldData_fn
    open fun gen_initFieldData_fn(fieldObj: FormFieldData): Unit {
        val fieldKey = fieldObj.key
        val fieldValue = fieldObj.value
        this.fieldName = fieldObj.name
        this.fieldValue = fieldValue
        this.isSave = fieldObj.isSave ?: false
        this.save_key = this.keyName + "_" + fieldKey
        val extalJson = fieldObj.extra as UTSJSONObject
        this.tip = extalJson.getString("tip") ?: ""
        this.varType = extalJson.getString("varType") ?: "number"
        this.updateSwitchValue()
        setTimeout(fun(){
            this.getCache()
        }
        , 500)
    }
    open var updateSwitchValue = ::gen_updateSwitchValue_fn
    open fun gen_updateSwitchValue_fn(): Unit {
        val valueType: String = UTSAndroid.`typeof`(this.fieldValue)
        if (valueType != this.varType) {
            console.log("类型不匹配", " at components/main-form/components/form-switch.uvue:110")
            this.switchValue = false
            if (this.varType == "number") {
                this.fieldValue = 0
            } else {
                this.fieldValue = false
            }
        }
        if (this.varType == "boolean") {
            this.switchValue = this.fieldValue as Boolean
        } else if (this.varType == "number") {
            this.switchValue = this.fieldValue == 1
        } else {
            this.switchValue = this.fieldValue == 1
        }
    }
    open var getCache = ::gen_getCache_fn
    open fun gen_getCache_fn(): Unit {
        if (this.isSave) {
            val that = this
            uni_getStorage(GetStorageOptions(key = this.save_key, success = fun(res: GetStorageSuccess){
                val save_value = res.data
                if (UTSAndroid.`typeof`(save_value) === "boolean") {
                    that.convertAndSetValue(save_value as Boolean)
                }
            }
            ))
        }
    }
    open var setCache = ::gen_setCache_fn
    open fun gen_setCache_fn(cacheValue: Boolean): Unit {
        if (this.isSave) {
            uni_setStorage(SetStorageOptions(key = this.save_key, data = cacheValue))
        }
    }
    open var convertAndSetValue = ::gen_convertAndSetValue_fn
    open fun gen_convertAndSetValue_fn(isChecked: Boolean): Unit {
        var cacheValue: Any
        if (this.varType == "boolean") {
            cacheValue = isChecked
        } else if (this.varType === "number") {
            cacheValue = if (isChecked) {
                1
            } else {
                0
            }
        } else {
            cacheValue = isChecked
        }
        this.fieldValue = cacheValue
        this.switchValue = isChecked as Boolean
        val result = FormChangeEvent(index = this.index, value = cacheValue)
        this.`$emit`("change", result)
        this.setCache(isChecked)
    }
    open var handleChange = ::gen_handleChange_fn
    open fun gen_handleChange_fn(event: UniSwitchChangeEvent): Unit {
        console.log("触发改变", " at components/main-form/components/form-switch.uvue:189")
        val isChecked = event.detail.value as Boolean
        this.convertAndSetValue(isChecked)
    }
    companion object {
        var name = "FormSwitch"
        val styles: Map<String, Map<String, Map<String, Any>>> by lazy {
            _nCS(_uA(
                styles0
            ))
        }
        val styles0: Map<String, Map<String, Map<String, Any>>>
            get() {
                return _uM("switch-box" to _pS(_uM("width" to "100%", "display" to "flex", "flexDirection" to "row", "alignItems" to "center", "justifyContent" to "flex-end")), "form-switch-element" to _pS(_uM("marginLeft" to "auto")))
            }
        var inheritAttrs = true
        var inject: Map<String, Map<String, Any?>> = _uM()
        var emits: Map<String, Any?> = _uM()
        var props = _nP(_uM("data" to _uM("type" to "Object"), "index" to _uM("type" to "Number", "default" to 0), "keyName" to _uM("type" to "String", "default" to ""), "labelColor" to _uM("type" to "String", "default" to "#000"), "backgroundColor" to _uM("type" to "String", "default" to "#f1f4f9")))
        var propsNeedCastKeys = _uA(
            "index",
            "keyName",
            "labelColor",
            "backgroundColor"
        )
        var components: Map<String, CreateVueComponent> = _uM("FormContainer" to GenComponentsMainFormComponentsFormContainerClass)
    }
}
