{"version": 3, "sources": ["components/main-form/components/form-yearmonth.uvue", "components/main-form/components/form-input.uvue"], "sourcesContent": ["<template>\n\t<form-container :label=\"fieldName\" :show-error=\"showError\" :tip=\"tip\" :error-message=\"errorMessage\" :label-color=\"labelColor\"\n\t\t:background-color=\"backgroundColor\">\n\t\t<template #input-content>\n\t\t\t<view class=\"yearmonth-display-container\" @click=\"openYearMonthPicker\">\n\t\t\t\t<view class=\"yearmonth-icon\">\n\t\t\t\t\t<text class=\"yearmonth-icon-text\">📅</text>\n\t\t\t\t</view>\n\t\t\t\t<text class=\"yearmonth-text\">{{ displayText }}</text>\n\t\t\t</view>\n\t\t</template>\n\t</form-container>\n\n\t<!-- 年月选择器 -->\n\t<main-yearmonth-picker ref=\"yearmonthPicker\" @confirm=\"onYearMonthConfirm\" @cancel=\"onYearMonthCancel\"></main-yearmonth-picker>\n</template>\n\n<script lang=\"uts\">\n\timport { FormFieldData, FormChangeEvent } from '@/components/main-form/form_type.uts'\n\timport FormContainer from './form-container.uvue'\n\timport MainYearmonthPicker from './../tools/main-yearmonth-picker.uvue'\n\n\t// 年月数据类型\n\ttype YearMonthData = {\n\t\tyear: number,\n\t\tmonth: number\n\t}\n\n\texport default {\n\t\tname: \"FormYearmonth\",\n\t\temits: ['change'],\n\t\tcomponents: {\n\t\t\tFormContainer,\n\t\t\tMainYearmonthPicker\n\t\t},\n\t\tprops: {\n\t\t\tdata: {\n\t\t\t\ttype: null as any as PropType<FormFieldData>\n\t\t\t},\n\t\t\tindex: {\n\t\t\t\ttype: Number,\n\t\t\t\tdefault: 0\n\t\t\t},\n\t\t\tkeyName: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"\"\n\t\t\t},\n\t\t\tlabelColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#000\"\n\t\t\t},\n\t\t\tbackgroundColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#f1f4f9\"\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tfieldName: \"\",\n\t\t\t\tfieldValue: \"\" as string,\n\t\t\t\tisSave: false,\n\t\t\t\tsave_key: \"\",\n\t\t\t\ttip: \"\",\n\t\t\t\tdisplayText: \"请选择年月\",\n\t\t\t\tshowError: false,\n\t\t\t\terrorMessage: \"\"\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\n\t\t},\n\t\twatch: {\n\t\t\tdata: {\n\t\t\t\thandler(obj: FormFieldData) {\n\t\t\t\t\t// 只处理value的变化，当外部传入的value与当前fieldValue不同时，才更新fieldValue\n\t\t\t\t\t// 这避免了用户输入时的循环更新问题\n\t\t\t\t\tconst newValue = obj.value as string\n\t\t\t\t\tif (newValue !== this.fieldValue) {\n\t\t\t\t\t\tthis.fieldValue = newValue\n\t\t\t\t\t\tthis.updateDisplayText()\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tdeep: true\n\t\t\t}\n\t\t},\n\t\tcreated(): void {\n\t\t\t// 初始化时调用一次即可\n\t\t\tconst fieldObj = this.$props[\"data\"] as FormFieldData\n\t\t\tthis.initFieldData(fieldObj)\n\t\t},\n\t\tmethods: {\n\t\t\t// 初始化字段数据（仅在首次加载时调用）\n\t\t\tinitFieldData(fieldObj: FormFieldData): void {\n\t\t\t\tconst fieldKey = fieldObj.key\n\t\t\t\tconst fieldValue = fieldObj.value as string\n\n\t\t\t\t// 设置基本信息\n\t\t\t\tthis.fieldName = fieldObj.name\n\t\t\t\tthis.fieldValue = fieldValue\n\t\t\t\tthis.isSave = fieldObj.isSave ?? false\n\t\t\t\tthis.save_key = this.keyName + \"_\" + fieldKey\n\n\t\t\t\t// 解析配置信息\n\t\t\t\tconst extalJson = fieldObj.extra as UTSJSONObject\n\t\t\t\tthis.tip = extalJson.getString(\"tip\") ?? \"\"\n\n\t\t\t\t// 更新显示文本\n\t\t\t\tthis.updateDisplayText()\n\n\t\t\t\t// 获取缓存\n\t\t\t\tthis.getCache()\n\t\t\t},\n\n\t\t\t// 更新显示文本\n\t\t\tupdateDisplayText(): void {\n\t\t\t\tif (this.fieldValue != \"\" ) {\n\t\t\t\t\t// 验证格式是否为 YYYY-MM\n\t\t\t\t\tconst yearMonthPattern = /^\\d{4}-\\d{2}$/\n\t\t\t\t\tif (yearMonthPattern.test(this.fieldValue)) {\n\t\t\t\t\t\tconst parts = this.fieldValue.split(\"-\")\n\t\t\t\t\t\tconst year = parts[0]\n\t\t\t\t\t\tconst month = parts[1]\n\t\t\t\t\t\tthis.displayText = `${year}年${month}月`\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.displayText = this.fieldValue\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tthis.displayText = \"请选择年月\"\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tgetCache(): void {\n\t\t\t\tif (this.isSave) {\n\t\t\t\t\tconst that = this\n\t\t\t\t\tuni.getStorage({\n\t\t\t\t\t\tkey: this.save_key,\n\t\t\t\t\t\tsuccess: (res: GetStorageSuccess) => {\n\t\t\t\t\t\t\tconst save_value = res.data\n\t\t\t\t\t\t\tif(typeof save_value === 'string'){\n\t\t\t\t\t\t\t\tthat.fieldValue = save_value as string\n\t\t\t\t\t\t\t\tthat.updateDisplayText()\n\t\t\t\t\t\t\t\tconst result: FormChangeEvent = {\n\t\t\t\t\t\t\t\t\tindex: this.index,\n\t\t\t\t\t\t\t\t\tvalue: save_value\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tthis.change(result)\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tsetCache(): void {\n\t\t\t\tif (this.isSave && typeof this.fieldValue ===\"string\") {\n\t\t\t\t\tuni.setStorage({\n\t\t\t\t\t\tkey: this.save_key,\n\t\t\t\t\t\tdata: this.fieldValue\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tvalidate(): boolean {\n\t\t\t\t// 年月值验证\n\t\t\t\tif (this.fieldValue == \"\") {\n\t\t\t\t\tthis.showError = true\n\t\t\t\t\tthis.errorMessage = \"请选择年月\"\n\t\t\t\t\treturn false\n\t\t\t\t}\n\n\t\t\t\t// 验证年月格式 YYYY-MM\n\t\t\t\tconst yearMonthPattern = /^\\d{4}-\\d{2}$/\n\t\t\t\tif (!yearMonthPattern.test(this.fieldValue)) {\n\t\t\t\t\tthis.showError = true\n\t\t\t\t\tthis.errorMessage = \"年月格式不正确\"\n\t\t\t\t\treturn false\n\t\t\t\t}\n\n\t\t\t\t// 验证月份范围\n\t\t\t\tconst parts = this.fieldValue.split(\"-\")\n\t\t\t\tconst month = parseInt(parts[1])\n\t\t\t\tif (month < 1 || month > 12) {\n\t\t\t\t\tthis.showError = true\n\t\t\t\t\tthis.errorMessage = \"月份必须在1-12之间\"\n\t\t\t\t\treturn false\n\t\t\t\t}\n\n\t\t\t\tthis.showError = false\n\t\t\t\tthis.errorMessage = \"\"\n\t\t\t\treturn true\n\t\t\t},\n\n\t\t\tchange(event: FormChangeEvent): void {\n\t\t\t\t// 更新字段值\n\t\t\t\tthis.fieldValue = event.value as string\n\t\t\t\t// 更新显示文本\n\t\t\t\tthis.updateDisplayText()\n\t\t\t\t// 保存缓存\n\t\t\t\tthis.setCache()\n\t\t\t\t// 触发父组件事件\n\t\t\t\tthis.$emit('change', event)\n\t\t\t},\n\n\t\t\t// 打开年月选择器\n\t\t\topenYearMonthPicker(): void {\n\t\t\t\tconst yearmonthPicker = this.$refs[\"yearmonthPicker\"] as ComponentPublicInstance\n\t\t\t\tyearmonthPicker.$callMethod(\"open\")\n\t\t\t},\n\n\t\t\t// 年月选择确认\n\t\t\tonYearMonthConfirm(yearMonthData: UTSJSONObject): void {\r\n\t\t\t\tconsole.log(yearMonthData)\n\t\t\t\t// 格式化为 YYYY-MM 格式\n\t\t\t\tconst year = yearMonthData.getNumber(\"year\")\n\t\t\t\tconst month = yearMonthData.getNumber(\"month\")\r\n\t\t\t\tlet yearValue :string=new Date().getFullYear().toString()\r\n\t\t\t\tlet monthValue :string=new Date().getMonth().toString()\r\n\r\n\t\t\t\tif(year!=null){\r\n\t\t\t\t\tyearValue=year.toString()\r\n\t\t\t\t}\r\n\t\t\t\tif(month!=null){\r\n\t\t\t\t\tmonthValue=month.toString().padStart(2, '0')\r\n\t\t\t\t}\r\n\t\t\t\t\n\t\t\t\tconst selectedValue = `${yearValue}-${monthValue}`\n\n\t\t\t\tconst result: FormChangeEvent = {\n\t\t\t\t\tindex: this.index,\n\t\t\t\t\tvalue: selectedValue\n\t\t\t\t}\n\t\t\t\tthis.change(result)\n\t\t\t},\n\n\t\t\t// 年月选择取消\n\t\t\tonYearMonthCancel(): void {\n\t\t\t\t// 取消选择，不做任何操作\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t.yearmonth-display-container {\n\t\tflex: 1;\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t\tmin-height: 60rpx;\n\t\tpadding: 10rpx;\n\t\tborder-radius: 10rpx;\n\t\tbackground-color: rgba(255, 255, 255, 0.8);\n\t}\n\n\t.yearmonth-icon {\n\t\twidth: 60rpx;\n\t\theight: 40rpx;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tmargin-right: 20rpx;\n\t}\n\n\t.yearmonth-icon-text {\n\t\tfont-size: 32rpx;\n\t}\n\n\t.yearmonth-text {\n\t\tflex: 1;\n\t\tfont-size: 28rpx;\n\t\tcolor: #333333;\n\t}\n</style>", null], "names": [], "mappings": ";;;;;;;;;;;;;+BA8GS;+BAgBL;AAlGE;;kBAyDJ,OAAW,IAAG,CAAA;YAEb,IAAM,WAAW,IAAI,CAAC,QAAM,CAAC,OAAM,CAAA,EAAA;YACnC,IAAI,CAAC,aAAa,CAAC;QACpB;;;;;UAhBE,IAAQ,kBAAkB,EAAA;YAGzB,IAAM,WAAW,IAAI,KAAI,CAAA,EAAA,CAAK,MAAK;YACnC,IAAI,aAAa,IAAI,CAAC,UAAU,EAAE;gBACjC,IAAI,CAAC,UAAS,GAAI;gBAClB,IAAI,CAAC,iBAAiB;;QAExB;uBACA,OAAM,IAAG;;;;;;;;;YAjFZ,IAUiB,2BAAA,IAVA,WAAO,KAAA,SAAS,EAAG,gBAAY,KAAA,SAAS,EAAG,SAAK,KAAA,GAAG,EAAG,mBAAe,KAAA,YAAY,EAAG,iBAAa,KAAA,UAAU,EAC1H,sBAAkB,KAAA,eAAe,OACvB,mBAAa,YACvB,gBAKO,GAAA;uBAAA;oBALP,IAKO,QAAA,IALD,WAAM,+BAA+B,aAAO,KAAA,mBAAmB;wBACpE,IAEO,QAAA,IAFD,WAAM,mBAAgB;4BAC3B,IAA2C,QAAA,IAArC,WAAM,wBAAsB;;wBAEnC,IAAqD,QAAA,IAA/C,WAAM,mBAAgB,IAAI,KAAA,WAAW,GAAA,CAAA;;;;;;;;;;;;;;YAM9C,IAA+H,kCAAA,IAAxG,SAAI,mBAAmB,eAAS,KAAA,kBAAkB,EAAG,cAAQ,KAAA,iBAAiB;;;;;;;;;;;aA4ClG;aACA,YAAkB,MAAM;aACxB;aACA;aACA;aACA;aACA;aACA;;;mBAPA,eAAW,IACX,gBAAY,GAAC,EAAA,CAAK,MAAM,EACxB,YAAQ,KAAK,EACb,cAAU,IACV,SAAK,IACL,iBAAa,SACb,eAAW,KAAK,EAChB,kBAAc;;aA2Bf;aAAA,qBAAc,uBAAuB,GAAG,IAAG,CAAA;QAC1C,IAAM,WAAW,SAAS,GAAE;QAC5B,IAAM,aAAa,SAAS,KAAI,CAAA,EAAA,CAAK,MAAK;QAG1C,IAAI,CAAC,SAAQ,GAAI,SAAS,IAAG;QAC7B,IAAI,CAAC,UAAS,GAAI;QAClB,IAAI,CAAC,MAAK,GAAI,SAAS,MAAK,IAAK,KAAI;QACrC,IAAI,CAAC,QAAO,GAAI,IAAI,CAAC,OAAM,GAAI,MAAM;QAGrC,IAAM,YAAY,SAAS,KAAI,CAAA,EAAA,CAAK;QACpC,IAAI,CAAC,GAAE,GAAI,UAAU,SAAS,CAAC,UAAU;QAGzC,IAAI,CAAC,iBAAiB;QAGtB,IAAI,CAAC,QAAQ;IACd;aAGA;aAAA,4BAAqB,IAAG,CAAA;QACvB,IAAI,IAAI,CAAC,UAAS,IAAK,IAAK;YAE3B,IAAM,mBAAmB;YACzB,IAAI,iBAAiB,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG;gBAC3C,IAAM,QAAQ,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;gBACpC,IAAM,OAAO,KAAK,CAAC,CAAC,CAAA;gBACpB,IAAM,QAAQ,KAAK,CAAC,CAAC,CAAA;gBACrB,IAAI,CAAC,WAAU,GAAI,KAAG,OAAI,WAAI,QAAK;mBAC7B;gBACN,IAAI,CAAC,WAAU,GAAI,IAAI,CAAC,UAAS;aAClC;eACM;YACN,IAAI,CAAC,WAAU,GAAI;;IAErB;aAEA;aAAA,mBAAY,IAAG,CAAA;QACd,IAAI,IAAI,CAAC,MAAM,EAAE;YAChB,IAAM,OAAO,IAAG;YAChB,iCACC,MAAK,IAAI,CAAC,QAAQ,EAClB,UAAS,IAAC,KAAK,kBAAoB;gBAClC,IAAM,aAAa,IAAI,IAAG;gBAC1B,IAAG,oBAAO,gBAAe,UAAS;oBACjC,KAAK,UAAS,GAAI,WAAS,EAAA,CAAK,MAAK;oBACrC,KAAK,iBAAiB;oBACtB,IAAM,yBACL,QAAO,IAAI,CAAC,KAAK,EACjB,QAAO;oBAER,IAAI,CAAC,MAAM,CAAC;;YAGd;;;IAGH;aAEA;aAAA,mBAAY,IAAG,CAAA;QACd,IAAI,IAAI,CAAC,MAAK,IAAK,oBAAO,IAAI,CAAC,UAAS,MAAK,UAAU;YACtD,iCACC,MAAK,IAAI,CAAC,QAAQ,EAClB,OAAM,IAAI,CAAC,UAAS;;IAGvB;aAEA;aAAA,mBAAY,OAAM,CAAA;QAEjB,IAAI,IAAI,CAAC,UAAS,IAAK,IAAI;YAC1B,IAAI,CAAC,SAAQ,GAAI,IAAG;YACpB,IAAI,CAAC,YAAW,GAAI;YACpB,OAAO,KAAI;;QAIZ,IAAM,mBAAmB;QACzB,IAAI,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG;YAC5C,IAAI,CAAC,SAAQ,GAAI,IAAG;YACpB,IAAI,CAAC,YAAW,GAAI;YACpB,OAAO,KAAI;;QAIZ,IAAM,QAAQ,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;QACpC,IAAM,QAAQ,SAAS,KAAK,CAAC,CAAC,CAAC;QAC/B,IAAI,QAAQ,CAAA,IAAK,QAAQ,EAAE,EAAE;YAC5B,IAAI,CAAC,SAAQ,GAAI,IAAG;YACpB,IAAI,CAAC,YAAW,GAAI;YACpB,OAAO,KAAI;;QAGZ,IAAI,CAAC,SAAQ,GAAI,KAAI;QACrB,IAAI,CAAC,YAAW,GAAI;QACpB,OAAO,IAAG;IACX;aAEA;aAAA,cAAO,sBAAsB,GAAG,IAAG,CAAA;QAElC,IAAI,CAAC,UAAS,GAAI,MAAM,KAAI,CAAA,EAAA,CAAK,MAAK;QAEtC,IAAI,CAAC,iBAAiB;QAEtB,IAAI,CAAC,QAAQ;QAEb,IAAI,CAAC,OAAK,CAAC,UAAU;IACtB;aAGA;aAAA,8BAAuB,IAAG,CAAA;QACzB,IAAM,kBAAkB,IAAI,CAAC,OAAK,CAAC,kBAAiB,CAAA,EAAA,CAAK;QACzD,gBAAgB,aAAW,CAAC;IAC7B;aAGA;aAAA,0BAAmB,eAAe,aAAa,GAAG,IAAG,CAAA;QACpD,QAAQ,GAAG,CAAC,eAAa;QAEzB,IAAM,OAAO,cAAc,SAAS,CAAC;QACrC,IAAM,QAAQ,cAAc,SAAS,CAAC;QACtC,IAAI,WAAW,MAAM,GAAC,AAAI,OAAO,WAAW,GAAG,QAAQ,CAAA,EAAA;QACvD,IAAI,YAAY,MAAM,GAAC,AAAI,OAAO,QAAQ,GAAG,QAAQ,CAAA,EAAA;QAErD,IAAG,QAAM,IAAI,EAAC;YACb,YAAU,KAAK,QAAQ,CAAA,EAAA;;QAExB,IAAG,SAAO,IAAI,EAAC;YACd,aAAW,MAAM,QAAQ,CAAA,EAAA,EAAG,QAAQ,CAAC,CAAC,EAAE;;QAGzC,IAAM,gBAAgB,KAAG,YAAS,MAAI;QAEtC,IAAM,yBACL,QAAO,IAAI,CAAC,KAAK,EACjB,QAAO;QAER,IAAI,CAAC,MAAM,CAAC;IACb;aAGA;aAAA,4BAAqB,IAAG,CAAA,CAExB;;mBAhNK;;;;;;;;;;;;;6FAYK,CAAA,qDAIA,0DAIA,mEAIA;;;;;;;;;AA0LZ"}