{"version": 3, "sources": ["components/main-form/components/form-switch.uvue"], "names": [], "mappings": "AAYC,OAAO,EAAE,aAAa,EAAE,eAAc,EAAE,MAAO,sCAAqC,CAAA;AACpF,OAAO,aAAY,MAAO,uBAAsB,CAAA;AAEhD,MAAK,OAAQ,GAAE,eAAA,CAAA;IACd,IAAI,EAAE,YAAY;IAClB,UAAU,EAAE;QACX,aAAY;KACZ;IACD,KAAK,EAAE;QACN,IAAI,EAAE;YACL,IAAI,EAAE,MAAK,IAAK,QAAQ,CAAC,aAAa,CAAA;SACtC;QACD,KAAK,EAAE;YACN,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,CAAA;SACT;QACD,OAAO,EAAE;YACR,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,EAAC;SACV;QACD,UAAU,EAAE;YACX,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,MAAK;SACd;QACD,eAAe,EAAE;YAChB,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,SAAQ;SAClB;KACA;IACD,IAAI;QACH,OAAO;YACN,SAAS,EAAE,EAAE;YACb,UAAU,EAAE,IAAG,IAAK,GAAE,GAAI,IAAI;YAC9B,MAAM,EAAE,KAAK;YACb,QAAQ,EAAE,EAAE;YACZ,GAAG,EAAE,EAAE;YACP,OAAO,EAAE,QAAQ;YACjB,WAAW,EAAE,KAAK;YAClB,WAAW,EAAE,SAAS;YACtB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,EAAC;SAChB,CAAA;IACD,CAAC;IACD,QAAQ,EAAE,EAET;IACD,KAAK,EAAE;QACN,IAAI,EAAE;YACL,OAAO,CAAC,GAAG,EAAE,aAAa;gBACzB,wDAAuD;gBACvD,mBAAkB;gBAClB,MAAM,QAAO,GAAI,GAAG,CAAC,KAAI,CAAA;gBACzB,IAAI,QAAO,KAAM,IAAI,CAAC,UAAU,EAAE;oBACjC,OAAO,CAAC,GAAG,CAAC,MAAM,EAAA,yDAAA,CAAA,CAAA;oBAClB,IAAI,CAAC,UAAS,GAAI,QAAO,CAAA;oBACzB,IAAI,CAAC,iBAAiB,EAAC,CAAA;iBACxB;YACD,CAAC;YACD,IAAI,EAAE,IAAG;SACV;KACA;IACD,OAAO,IAAI,IAAG;QACb,aAAY;QACZ,MAAM,QAAO,GAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAA,IAAK,aAAY,CAAA;QACpD,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAA,CAAA;IAC5B,CAAC;IACD,OAAO,EAAE;QACR,qBAAoB;QACpB,aAAa,CAAC,QAAQ,EAAE,aAAa,GAAG,IAAG;YAC1C,MAAM,QAAO,GAAI,QAAQ,CAAC,GAAE,CAAA;YAC5B,MAAM,UAAS,GAAI,QAAQ,CAAC,KAAI,CAAA;YAEhC,SAAQ;YACR,IAAI,CAAC,SAAQ,GAAI,QAAQ,CAAC,IAAG,CAAA;YAC7B,IAAI,CAAC,UAAS,GAAI,UAAS,CAAA;YAC3B,IAAI,CAAC,MAAK,GAAI,QAAQ,CAAC,MAAK,IAAK,KAAI,CAAA;YACrC,IAAI,CAAC,QAAO,GAAI,IAAI,CAAC,OAAM,GAAI,GAAE,GAAI,QAAO,CAAA;YAC5C,SAAQ;YACR,MAAM,SAAQ,GAAI,QAAQ,CAAC,KAAI,IAAK,aAAY,CAAA;YAChD,IAAI,CAAC,GAAE,GAAI,SAAS,CAAC,SAAS,CAAC,KAAK,CAAA,IAAK,EAAC,CAAA;YAC1C,IAAI,CAAC,OAAM,GAAI,SAAS,CAAC,SAAS,CAAC,SAAS,CAAA,IAAK,QAAO,CAAA;YAIxD,IAAI,CAAC,iBAAiB,EAAC,CAAA;YAEvB,UAAU,CAAC,GAAE,EAAE;gBACd,IAAI,CAAC,QAAQ,EAAC,CAAA;YACf,CAAC,EAAC,GAAG,CAAA,CAAA;QAEN,CAAC;QAED,iCAAgC;QAChC,iBAAiB,IAAI,IAAG;YAEvB,MAAM,SAAQ,EAAG,MAAM,GAAC,OAAO,IAAI,CAAC,UAAS,CAAA;YAC7C,IAAG,SAAS,IAAE,IAAI,CAAC,OAAO,EAAC;gBAC1B,OAAO,CAAC,GAAG,CAAC,OAAO,EAAA,0DAAA,CAAA,CAAA;gBACnB,IAAI,CAAC,WAAU,GAAI,KAAI,CAAA;gBACtB,IAAI,IAAI,CAAC,OAAM,IAAK,QAAQ,EAAE;oBAC9B,IAAI,CAAC,UAAS,GAAI,CAAA,CAAA;iBACnB;qBAAM;oBACL,IAAI,CAAC,UAAS,GAAG,KAAI,CAAA;iBACtB;aACD;YAEA,IAAI,IAAI,CAAC,OAAM,IAAK,SAAS,EAAE;gBAC9B,qBAAoB;gBACpB,IAAI,CAAC,WAAU,GAAI,IAAI,CAAC,UAAS,IAAK,OAAM,CAAA;aAC7C;iBAAO,IAAI,IAAI,CAAC,OAAM,IAAK,QAAQ,EAAE;gBACpC,oBAAmB;gBACnB,IAAI,CAAC,WAAU,GAAI,IAAI,CAAC,UAAS,IAAK,CAAA,CAAA;aACvC;iBAAO;gBACN,gBAAe;gBACf,IAAI,CAAC,WAAU,GAAI,IAAI,CAAC,UAAS,IAAK,CAAA,CAAA;aACvC;QAGD,CAAC;QAED,QAAQ,IAAI,IAAG;YACd,IAAI,IAAI,CAAC,MAAM,EAAE;gBAChB,MAAM,IAAG,GAAI,IAAG,CAAA;gBAChB,GAAG,CAAC,UAAU,CAAC;oBACd,GAAG,EAAE,IAAI,CAAC,QAAQ;oBAClB,OAAO,EAAE,CAAC,GAAG,EAAE,iBAAiB,EAAE,EAAC;wBAClC,MAAM,UAAS,GAAI,GAAG,CAAC,IAAG,CAAA;wBAC1B,IAAG,OAAO,UAAS,KAAM,SAAS,EAAC;4BAClC,IAAI,CAAC,kBAAkB,CAAC,UAAU,YAAA,CAAA;yBACnC;oBAID,CAAA;iBACA,CAAA,CAAA;aACF;QACD,CAAC;QAED,QAAQ,CAAC,UAAS,EAAI,OAAO,GAAG,IAAG;YAClC,IAAI,IAAI,CAAC,MAAM,EAAE;gBAEhB,GAAG,CAAC,UAAU,CAAC;oBACd,GAAG,EAAE,IAAI,CAAC,QAAQ;oBAClB,IAAI,EAAE,UAAS;iBACf,CAAA,CAAA;aACF;QACD,CAAC;QAED,oBAAmB;QACnB,kBAAkB,CAAC,SAAS,EAAE,OAAO,GAAG,IAAG;YAC1C,IAAI,UAAU,EAAE,GAAE,CAAA;YAClB,IAAI,IAAI,CAAC,OAAM,IAAK,SAAS,EAAE;gBAC9B,qBAAoB;gBACpB,UAAS,GAAI,SAAQ,CAAA;aACtB;iBACK,IAAI,IAAI,CAAC,OAAM,KAAM,QAAQ,EAAE;gBACnC,UAAS,GAAI,SAAQ,CAAE,CAAA,CAAE,CAAA,CAAE,CAAA,CAAE,CAAA,CAAA;aAC9B;iBAAO;gBACN,UAAS,GAAI,SAAQ,CAAA;aACtB;YACA,IAAI,CAAC,UAAU,GAAC,UAAS,CAAA;YACzB,IAAI,CAAC,WAAU,GAAI,SAAQ,IAAK,OAAM,CAAA;YAGtC,MAAM,MAAM,GAAC;gBACZ,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,KAAK,EAAE,UAAS;aACjB,IAAK,eAAc,CAAA;YAEnB,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,MAAM,CAAA,CAAA;YAC3B,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAA,CAAA;QACxB,CAAC;QAGD,SAAQ;QACR,YAAY,CAAC,KAAK,EAAE,oBAAoB,GAAG,IAAG;YAC7C,OAAO,CAAC,GAAG,CAAC,MAAM,EAAA,0DAAA,CAAA,CAAA;YAClB,MAAM,SAAQ,GAAI,KAAK,CAAC,MAAM,CAAC,KAAI,IAAK,OAAM,CAAA;YAE9C,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAA,CAAA;QAClC,CAAA;KACD;CACD,CAAA,CAAA;;;;;;;WAjMA,GAAA,CAOiB,yBAAA,EAAA,GAAA,CAAA;QAPA,KAAK,EAAE,IAAA,CAAA,SAAS;QAAG,YAAU,EAAE,IAAA,CAAA,SAAS;QAAG,GAAG,EAAE,IAAA,CAAA,GAAG;QAAG,eAAa,EAAE,IAAA,CAAA,YAAY;QAAG,aAAW,EAAE,IAAA,CAAA,UAAU;QAC1H,kBAAgB,EAAE,IAAA,CAAA,eAAe;;QACvB,eAAa,EAAA,WAAA,CACvB,IAEO,GAAA,EAAA,CAAA,EAAA,CAAA;YAFP,GAAA,CAEO,MAAA,EAAA,GAAA,CAAA,EAFD,KAAK,EAAC,YAAY,EAAA,CAAA,EAAA;gBACvB,GAAA,CAAyG,iBAAA,EAAA,GAAA,CAAA;oBAAjG,KAAK,EAAC,qBAAqB;oBAAE,KAAK,EAAE,IAAA,CAAA,WAAW;oBAAG,OAAO,EAAE,IAAA,CAAA,WAAW;oBAAG,QAAM,EAAE,IAAA,CAAA,YAAY", "file": "components/main-form/components/form-switch.uvue", "sourcesContent": ["<template>\n\t<form-container :label=\"fieldName\" :show-error=\"showError\" :tip=\"tip\" :error-message=\"errorMessage\" :label-color=\"labelColor\"\n\t\t:background-color=\"backgroundColor\">\n\t\t<template #input-content>\n\t\t\t<view class=\"switch-box\">  \r\n\t\t\t\t<switch class=\"form-switch-element\" :color=\"switchColor\" :checked=\"switchValue\" @change=\"handleChange\" />\r\n\t\t\t</view>\n\t\t</template>\n\t</form-container>\n</template>\n\n<script lang=\"uts\">\n\timport { FormFieldData, FormChangeEvent } from '@/components/main-form/form_type.uts'\n\timport FormContainer from './form-container.uvue'\n\n\texport default {\n\t\tname: \"FormSwitch\",\n\t\tcomponents: {\n\t\t\tFormContainer\n\t\t},\n\t\tprops: {\n\t\t\tdata: {\n\t\t\t\ttype: Object as PropType<FormFieldData>\n\t\t\t},\n\t\t\tindex: {\n\t\t\t\ttype: Number,\n\t\t\t\tdefault: 0\n\t\t\t},\n\t\t\tkeyName: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"\"\n\t\t\t},\n\t\t\tlabelColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#000\"\n\t\t\t},\n\t\t\tbackgroundColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#f1f4f9\"\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tfieldName: \"\",\n\t\t\t\tfieldValue: null as any | null,\n\t\t\t\tisSave: false,\n\t\t\t\tsave_key: \"\",\n\t\t\t\ttip: \"\",\n\t\t\t\tvarType: \"number\",\n\t\t\t\tswitchValue: false,\n\t\t\t\tswitchColor: \"#8A6DE9\",\n\t\t\t\tshowError: false,\n\t\t\t\terrorMessage: \"\"\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\n\t\t},\n\t\twatch: {\n\t\t\tdata: {\n\t\t\t\thandler(obj: FormFieldData) {\n\t\t\t\t\t// 只处理value的变化，当外部传入的value与当前fieldValue不同时，才更新fieldValue\n\t\t\t\t\t// 这避免了用户输入时的循环更新问题\n\t\t\t\t\tconst newValue = obj.value\n\t\t\t\t\tif (newValue !== this.fieldValue) {\r\n\t\t\t\t\t\tconsole.log(\"触发改变\")\n\t\t\t\t\t\tthis.fieldValue = newValue\n\t\t\t\t\t\tthis.updateSwitchValue()\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tdeep: true\n\t\t\t}\n\t\t},\n\t\tcreated(): void {\n\t\t\t// 初始化时调用一次即可\n\t\t\tconst fieldObj = this.$props[\"data\"] as FormFieldData\n\t\t\tthis.initFieldData(fieldObj)\n\t\t},\n\t\tmethods: {\n\t\t\t// 初始化字段数据（仅在首次加载时调用）\n\t\t\tinitFieldData(fieldObj: FormFieldData): void {\n\t\t\t\tconst fieldKey = fieldObj.key\n\t\t\t\tconst fieldValue = fieldObj.value\n\n\t\t\t\t// 设置基本信息\n\t\t\t\tthis.fieldName = fieldObj.name\n\t\t\t\tthis.fieldValue = fieldValue\n\t\t\t\tthis.isSave = fieldObj.isSave ?? false\n\t\t\t\tthis.save_key = this.keyName + \"_\" + fieldKey\n\t\t\t\t// 解析配置信息\n\t\t\t\tconst extalJson = fieldObj.extra as UTSJSONObject\n\t\t\t\tthis.tip = extalJson.getString(\"tip\") ?? \"\"\n\t\t\t\tthis.varType = extalJson.getString(\"varType\") ?? \"number\"\n\t\t\t\t\r\n\t\t\t\t\r\n\t\t\t\t\r\n\t\t\t\tthis.updateSwitchValue() \n\t\t\t\t\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tthis.getCache()\t\r\n\t\t\t\t},500)\n\t\t\t\t\n\t\t\t},\n\n\t\t\t// 更新开关值（根据 fieldValue 和 varType）\n\t\t\tupdateSwitchValue(): void {\r\n\t\t\t\t\r\n\t\t\t\tconst valueType :string=typeof this.fieldValue\r\n\t\t\t\tif(valueType!=this.varType){\r\n\t\t\t\t\tconsole.log(\"类型不匹配\")\r\n\t\t\t\t\tthis.switchValue = false\r\n\t\t\t\t\t if (this.varType == \"number\") {\r\n\t\t\t\t\t\tthis.fieldValue = 0\r\n\t\t\t\t\t} else{\r\n\t\t\t\t\t\tthis.fieldValue =false\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\n\t\t\t\tif (this.varType == \"boolean\") {\n\t\t\t\t\t// boolean 类型：直接使用布尔值\n\t\t\t\t\tthis.switchValue = this.fieldValue as boolean\n\t\t\t\t} else if (this.varType == \"number\") {\n\t\t\t\t\t// number 类型：判断是否为 1\n\t\t\t\t\tthis.switchValue = this.fieldValue == 1\n\t\t\t\t} else {\n\t\t\t\t\t// 默认按 number 处理\n\t\t\t\t\tthis.switchValue = this.fieldValue == 1\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t\n\t\t\t},\n\n\t\t\tgetCache(): void {\n\t\t\t\tif (this.isSave) {\n\t\t\t\t\tconst that = this\n\t\t\t\t\tuni.getStorage({\n\t\t\t\t\t\tkey: this.save_key,\n\t\t\t\t\t\tsuccess: (res: GetStorageSuccess) => {\n\t\t\t\t\t\t\tconst save_value = res.data\r\n\t\t\t\t\t\t\tif(typeof save_value === 'boolean'){\r\n\t\t\t\t\t\t\t\tthat.convertAndSetValue(save_value)\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tsetCache(cacheValue : boolean): void {\n\t\t\t\tif (this.isSave) {\n\t\t\t\t\t\n\t\t\t\t\tuni.setStorage({\n\t\t\t\t\t\tkey: this.save_key,\n\t\t\t\t\t\tdata: cacheValue\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 根据 varType 转换并设置值\n\t\t\tconvertAndSetValue(isChecked: boolean): void {\r\n\t\t\t\tlet cacheValue: any\r\n\t\t\t\tif (this.varType == \"boolean\") {\r\n\t\t\t\t\t// boolean 类型：直接使用布尔值\r\n\t\t\t\t\tcacheValue = isChecked\r\n\t\t\t\t} \n\t\t\t\telse if (this.varType === \"number\") {\n\t\t\t\t\tcacheValue = isChecked ? 1 : 0\n\t\t\t\t} else {\n\t\t\t\t\tcacheValue = isChecked \n\t\t\t\t}\r\n\t\t\t\tthis.fieldValue=cacheValue \n\t\t\t\tthis.switchValue = isChecked as boolean\r\n\t\t\t\t\r\n\t\t\t\t\r\n\t\t\t\tconst result={\r\n\t\t\t\t\tindex: this.index,\r\n\t\t\t\t\tvalue: cacheValue\r\n\t\t\t\t} as FormChangeEvent\r\n\t\t\t\t\r\n\t\t\t\tthis.$emit(\"change\", result)\r\n\t\t\t\tthis.setCache(isChecked)\n\t\t\t},\n\n\n\t\t\t// 处理开关变更\n\t\t\thandleChange(event: UniSwitchChangeEvent): void {\r\n\t\t\t\tconsole.log(\"触发改变\")\n\t\t\t\tconst isChecked = event.detail.value as boolean\n\t\t\t\t\n\t\t\t\tthis.convertAndSetValue(isChecked)\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\r\n\t.switch-box {\r\n\t\twidth: 100%;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tjustify-content: flex-end;\r\n\t}\n\t.form-switch-element {\n\t\tmargin-left: auto;\n\t}\n</style>\n"]}