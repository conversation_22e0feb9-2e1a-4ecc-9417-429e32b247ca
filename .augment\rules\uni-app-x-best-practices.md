---
description: Best practices for uni-app-x
globs: 
alwaysApply: true
---

# Memory Bank

你熟悉 uni-app x框架,擅长编写跨平台且高性能的代码.
uni-app x项目使用UTS语言编写script. UTS是一种跨平台的强类型语言, 类似TS语言但类型要求更加严格.

## Code Style and Structure
    - 简洁易懂,复杂的代码配上中文注释.
    - 严格类型匹配,不使用隐式转换.
    - 不使用变量和函数的声明提升, 严格的在清晰的范围内使用变量和函数.
    - 当生成某个平台专用代码时, 应使用条件编译进行平台约束,避免干扰其他平台.

## UTS语言规范
    - 变量声明必须明确类型: `let baseHue: number` 而不是 `let baseHue = 0`
    - 避免冗余初始化: 如果变量会被完全覆盖，不要给初始值
    - 类型定义使用type关键字: `type ColorInfo = { r: number, g: number, b: number }`
    - 计算属性返回类型要明确: `colorList(): string[]` 而不是复杂的泛型
    - 方法参数类型必须明确: `onColorSelect(index: number)`

## project
    - 遵循uni-app x的项目结构, 在正确的目录中放置生成的文件.
    - 不要创建测试文件或示例文件，专注于核心功能实现.

## 包管理规范
    - **使用包管理器**: 总是使用适当的包管理器(npm、yarn、pnpm)进行依赖管理，而不是手动编辑package.json
    - **避免手动编辑**: 不要直接编辑package.json、requirements.txt等配置文件
    - **版本管理**: 让包管理器自动处理版本解析和依赖冲突
    
## CSS样式规范
    - **文本相关属性限制**: `color`、`text-align`、`font-weight`等只能用于文本元素(`<text>`、`<button>`、`<input>`、`<textarea>`)
    - **不支持伪类选择器**: 不能使用`:nth-child()`、`:not()`、`:hover`等，只支持基本类名选择器
    - **不支持calc()函数**: 不能使用`width: calc(100% - 20px)`，必须使用固定值或rpx单位
    - **font-weight限制**: 只支持`normal`、`bold`、`400`、`700`，不支持`500`、`600`等
    - **尺寸单位限制**: `max-height`、`max-width`不支持百分比，只支持`number`、`pixel`、`rpx`
    - **布局推荐**: 优先使用Flexbox布局而不是传统CSS属性，如用`justify-content: center`代替`text-align: center`
    - **响应式单位**: 优先使用rpx而不是px或百分比，确保跨设备一致性

## 布局最佳实践
    - **居中对齐**: 容器使用`display: flex; justify-content: center; align-items: center`
    - **文本居中**: 在`<text>`元素上使用`text-align: center`
    - **宽度控制**: 使用固定rpx值而不是百分比，如`width: 100rpx`而不是`width: 10%`
    - **间距控制**: 使用`margin`、`padding`的rpx值，避免使用`gap`属性
    - **动态类名**: 使用`:class="{ 'active': condition }"`而不是伪类选择器

## page
    - 使用uvue作为页面后缀名, uvue与vue基本类似, 但有少量细节差异.
    - 生成的uvue页面放置在项目的pages目录下, 生成的页面需要在pages.json中注册.
    - 可滚动内容必须在scroll-view、list-view、waterflow等滚动容器中. 如果页面需要滚动, 则在页面template的一级子节点放置滚动容器, 例如` <scroll-view style="flex:1">`. 此时应在 App 上使用条件编译, 例如: `<!-- #ifdef APP --><scroll-view class="container"><!-- #endif -->`
    - 生成uvue页面时, 页面内容需符合uts.mdc、uvue.mdc、ucss.mdc、api.mdc约定的规范.

## 常见错误和解决方案
    - **CSS属性限制错误**:
        - 错误: `style property 'color' is only supported on <text>|<button>|<input>|<textarea>`
        - 解决: 将color属性移到text元素上，容器使用background-color
    - **CSS选择器错误**:
        - 错误: `Selector '.button:nth-child(1)' is not supported`
        - 解决: 使用动态类名`:class="{ 'first-button': index === 0 }"`
        - 错误: `Selector '.form-input-label text' is not supported. uvue only support classname selector`
        - 解决: 不使用元素选择器，将样式直接写在父容器上
    - **CSS函数错误**:
        - 错误: `property value 'calc(100% - 20px)' is not supported`
        - 解决: 使用固定rpx值或Flexbox布局
    - **CSS属性值错误**:
        - 错误: `property value '80%' is not supported for 'max-height'`
        - 解决: 使用rpx单位: `max-height: 800rpx`
        - 错误: `property value '600' is not supported for 'font-weight'`
        - 解决: 只使用支持的值: `font-weight: bold` 或 `font-weight: 700`
        - 错误: `overflow-y` is not a standard property name
        - 解决: 避免使用非标准CSS属性
        - 错误: `gap` is not a standard property name
        - 解决: 使用margin代替gap属性
    - **UTS类型错误**:
        - 错误: `Variable 'baseHue' initializer is redundant`
        - 解决: 声明类型但不初始化: `let baseHue: number`
        - 错误: `None of the following functions can be called with the arguments supplied: String(...)`
        - 解决: 避免使用 `String(value || "")` 的写法，使用类型检查: `value != null ? value as string : ""`
        - 错误: `类型不匹配: 推断类型是Any，但预期的是Boolean`
        - 解决: 在模板中使用计算属性或方法，避免复杂的逻辑表达式
        - 错误: `The integer literal does not conform to the expected type Boolean`
        - 解决: 确保属性值类型匹配，如 `:maxlength="100"` 改为使用计算属性
        - 错误: `Cannot find name 'Object'`
        - 解决: 在UTS中不能直接使用Object类型，需要定义具体的类型或使用UTSJSONObject
        - 错误: `Expression 'getMaxLength' of type 'Number' cannot be invoked as a function`
        - 解决: 计算属性在模板中不能当作函数调用，使用 `getMaxLength` 而不是 `getMaxLength()`
        - 错误: `Cannot create an instance of an abstract class`
        - 解决: 不能使用 `Number(value)` 构造函数，使用 `parseFloat(value)` 或 `parseInt(value)`
        - 错误: `类型不匹配: 推断类型是Any?（可为空的Any），但预期的是Any`
        - 解决: 确保传递给函数的参数类型正确，避免传递可能为null的值
    - **事件处理错误**:
        - 错误: `组件view不支持事件: 'click.stop'`
        - 解决: 使用方法处理事件冒泡，不能使用事件修饰符

## 调试技巧
    - **编译错误**: 优先检查CSS属性是否用在正确的元素上
    - **类型错误**: 确保所有变量都有明确的类型声明
    - **布局问题**: 使用Flexbox而不是传统CSS布局方式
    - **兼容性问题**: 参考uni-app x官方文档确认支持的CSS属性