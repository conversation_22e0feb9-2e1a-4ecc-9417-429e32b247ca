{"version": 3, "sources": ["components/main-icons/main-icons.uvue"], "sourcesContent": ["<template>\r\n\t<text class=\"uni-icons\" :style=\"styleObj\">\r\n\t\t<slot>{{unicode}}</slot>\r\n\t</text>\r\n</template>\r\n\r\n<script>\r\n\timport { fontData, IconsDataItem } from './iconfont_file'\r\n\r\n\t/**\r\n\t * Icons 图标\r\n\t * @description 用于展示 icon 图标\r\n\t * @tutorial https://ext.dcloud.net.cn/plugin?id=28\r\n\t * @property {Number} size 图标大小\r\n\t * @property {String} type 图标图案，参考示例\r\n\t * @property {String} color 图标颜色\r\n\t * @property {String} customPrefix 自定义图标\r\n\t * @event {Function} click 点击 Icon 触发事件\r\n\t */\r\n\texport default {\r\n\t\tname: \"main-icons\",\r\n\t\tprops: {\r\n\t\t\ttype: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tcolor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#333333'\r\n\t\t\t},\r\n\t\t\tsize: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault: 16\r\n\t\t\t},\r\n\t\t\tfontFamily: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {};\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tunicode() : string {\r\n\t\t\t\tlet codes = fontData.find((item : IconsDataItem) : boolean => { return item.font_class == this.type })\r\n\t\t\t\tif (codes !== null) {\r\n\t\t\t\t\treturn codes.unicode\r\n\t\t\t\t}\r\n\t\t\t\treturn ''\r\n\t\t\t},\r\n\t\t\ticonSize() : string {\r\n\t\t\t\tconst size = this.size\r\n\t\t\t\tif (typeof size == 'string') {\r\n\t\t\t\t\tconst reg = /^[0-9]*$/g\r\n\t\t\t\t\treturn reg.test(size as string) ? '' + size + 'px' : '' + size;\r\n\t\t\t\t\t// return '' + this.size\r\n\t\t\t\t}\r\n\t\t\t\treturn this.getFontSize(size as number)\r\n\t\t\t},\r\n\t\t\tstyleObj() : UTSJSONObject {\r\n\t\t\t\tif (this.fontFamily !== '') {\r\n\t\t\t\t\treturn { color: this.color, fontSize: this.iconSize, fontFamily: this.fontFamily }\r\n\t\t\t\t}\r\n\t\t\t\treturn { color: this.color, fontSize: this.iconSize }\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() { },\r\n\t\tmethods: {\r\n\t\t\t/**\r\n\t\t\t * 字体大小\r\n\t\t\t */\r\n\t\t\tgetFontSize(size : number) : string {\r\n\t\t\t\treturn size + 'px';\r\n\t\t\t},\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style scoped>\r\n\t@font-face {\r\n\t\tfont-family: UniIconsFontFamily;\r\n\t\tsrc: url('./iconfont.ttf');\r\n\t}\r\n\r\n\t.uni-icons {\r\n\t\tfont-family: UniIconsFontFamily;\r\n\t\tfont-size: 18px;\r\n\t\tfont-style: normal;\r\n\t\tcolor: #333;\r\n\t}\r\n</style>\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAmBM;;kBA+CJ,MAAO,CAAK;;;;;;eAjEb,IAEO,QAAA,IAFD,WAAM,aAAa,WAAK,IAAE,KAAA,QAAQ;YACvC,WAAwB,KAAA,QAAA,EAAA,WAAA,eAAA,EAAxB,gBAAwB,GAAA;uBAAA;oBAAA,IAAhB,KAAA,OAAO;iBAAA;;;;;;;;;sBAyCF,MAAK;uBAOJ,MAAK;uBASL;;;yCAhBD,MAAK,EAAjB,OAAY,MAAK,CAAA;YAChB,IAAI,QAAQ,SAAS,IAAI,CAAC,IAAC,sBAAwB,OAAM,CAAG;gBAAI,OAAO,KAAK,UAAS,IAAK,IAAI,CAAC,IAAG;YAAE;;YACpG,IAAI,SAAU,IAAI,EAAE;gBACnB,OAAO,MAAM,OAAM;;YAEpB,OAAO;QACR;kCACa,MAAK,EAAlB,OAAa,MAAK,CAAA;YACjB,IAAM,OAAO,IAAI,CAAC,IAAG;YACrB,IAAI,oBAAO,SAAQ,UAAU;gBAC5B,IAAM,MAAM;gBACZ,OAAO,IAAA,IAAI,IAAI,CAAC,KAAG,EAAA,CAAK,MAAM;oBAAI,KAAK,OAAO;;oBAAO,KAAK;;;YAG3D,OAAO,IAAI,CAAC,WAAW,CAAC,KAAG,EAAA,CAAK,MAAM;QACvC;kCACa,eAAb,OAAa,cAAY;YACxB,IAAI,IAAI,CAAC,UAAS,KAAM,IAAI;gBAC3B,OAAO,IAAE,WAAO,IAAI,CAAC,KAAK,EAAE,cAAU,IAAI,CAAC,QAAQ,EAAE,gBAAY,IAAI,CAAC,UAAS;;YAEhF,OAAO,IAAE,WAAO,IAAI,CAAC,KAAK,EAAE,cAAU,IAAI,CAAC,QAAO;QACnD;;;aAOA;aAAA,mBAAY,MAAO,MAAM,GAAI,MAAK,CAAA;QACjC,OAAO,OAAO;IACf;;mBArDK;;;;;;;;;;;;;2EAIK,qDAIA,uCAIA,EAAC,wDAID;;;;;;;;;AAuCZ"}