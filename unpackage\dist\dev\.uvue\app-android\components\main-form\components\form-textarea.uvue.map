{"version": 3, "sources": ["components/main-form/components/form-textarea.uvue"], "names": [], "mappings": "AAWC,OAAO,EAAE,aAAa,EAAE,eAAc,EAAE,MAAO,sCAAqC,CAAA;AACpF,OAAO,aAAY,MAAO,uBAAsB,CAAA;AAEhD,MAAK,OAAQ,GAAE,eAAA,CAAA;IACd,IAAI,EAAE,cAAc;IACpB,UAAU,EAAE;QACX,aAAY;KACZ;IACD,KAAK,EAAE;QACN,IAAI,EAAE;YACL,IAAI,EAAE,MAAK,IAAK,QAAQ,CAAC,aAAa,CAAA;SACtC;QACD,KAAK,EAAE;YACN,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,CAAA;SACT;QACD,OAAO,EAAE;YACR,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,EAAC;SACV;QACD,UAAU,EAAE;YACX,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,MAAK;SACd;QACD,eAAe,EAAE;YAChB,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,SAAQ;SAClB;KACA;IACD,IAAI;QACH,OAAO;YACN,SAAS,EAAE,EAAE;YACb,UAAU,EAAE,EAAE;YACd,MAAM,EAAE,KAAK;YACb,QAAQ,EAAE,EAAE;YACZ,GAAG,EAAE,EAAE;YACP,WAAW,EAAE,EAAE;YACf,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE,CAAC,CAAC;YACb,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,EAAC;SAChB,CAAA;IACD,CAAC;IACD,QAAQ,EAAE,EAET;IACD,KAAK,EAAE;QACN,IAAI,EAAE;YACL,OAAO,CAAC,GAAG,EAAE,aAAa;gBACzB,wDAAuD;gBACvD,mBAAkB;gBAClB,MAAM,QAAO,GAAI,GAAG,CAAC,KAAI,IAAK,MAAK,CAAA;gBACnC,IAAI,QAAO,KAAM,IAAI,CAAC,UAAU,EAAE;oBACjC,IAAI,CAAC,UAAS,GAAI,QAAO,CAAA;iBAC1B;YACD,CAAC;YACD,IAAI,EAAE,IAAG;SACV;KACA;IACD,OAAO,IAAI,IAAG;QACb,aAAY;QACZ,MAAM,QAAO,GAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAA,IAAK,aAAY,CAAA;QACpD,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAA,CAAA;IAC5B,CAAC;IACD,OAAO,EAAE;QACR,qBAAoB;QACpB,aAAa,CAAC,QAAQ,EAAE,aAAa,GAAG,IAAG;YAC1C,MAAM,QAAO,GAAI,QAAQ,CAAC,GAAE,CAAA;YAC5B,MAAM,UAAS,GAAI,QAAQ,CAAC,KAAI,IAAK,MAAK,CAAA;YAE1C,SAAQ;YACR,IAAI,CAAC,SAAQ,GAAI,QAAQ,CAAC,IAAG,CAAA;YAC7B,IAAI,CAAC,UAAS,GAAI,UAAS,CAAA;YAC3B,IAAI,CAAC,MAAK,GAAI,QAAQ,CAAC,MAAK,IAAK,KAAI,CAAA;YACrC,IAAI,CAAC,QAAO,GAAI,IAAI,CAAC,OAAM,GAAI,GAAE,GAAI,QAAO,CAAA;YAE5C,mCAAkC;YAClC,MAAM,SAAQ,GAAI,QAAQ,CAAC,KAAI,IAAK,aAAY,CAAA;YAChD,IAAI,CAAC,SAAQ,GAAI,SAAS,CAAC,SAAS,CAAC,WAAW,CAAA,IAAK,CAAA,CAAA;YACrD,IAAI,CAAC,SAAQ,GAAI,SAAS,CAAC,SAAS,CAAC,WAAW,CAAA,IAAK,CAAC,CAAA,CAAA;YACtD,IAAI,CAAC,WAAU,GAAI,SAAS,CAAC,SAAS,CAAC,aAAa,CAAA,IAAK,EAAC,CAAA;YAC1D,IAAI,CAAC,GAAE,GAAI,SAAS,CAAC,SAAS,CAAC,KAAK,CAAA,IAAK,EAAC,CAAA;YAE1C,OAAM;YACN,IAAI,CAAC,QAAQ,EAAC,CAAA;QACf,CAAC;QAED,QAAQ,IAAI,IAAG;YACd,IAAI,IAAI,CAAC,MAAM,EAAE;gBAChB,MAAM,IAAG,GAAI,IAAG,CAAA;gBAChB,GAAG,CAAC,UAAU,CAAC;oBACd,GAAG,EAAE,IAAI,CAAC,QAAQ;oBAClB,OAAO,EAAE,CAAC,GAAG,EAAE,iBAAiB,EAAE,EAAC;wBAClC,MAAM,UAAS,GAAI,GAAG,CAAC,IAAG,IAAK,MAAK,CAAA;wBACpC,IAAI,CAAC,UAAS,GAAI,UAAS,CAAA;wBAC3B,IAAI,CAAC,YAAY,EAAC,CAAA;oBACnB,CAAA;iBACA,CAAA,CAAA;aACF;QACD,CAAC;QAED,QAAQ,IAAI,IAAG;YACd,IAAI,IAAI,CAAC,MAAM,EAAE;gBAChB,GAAG,CAAC,UAAU,CAAC;oBACd,GAAG,EAAE,IAAI,CAAC,QAAQ;oBAClB,IAAI,EAAE,IAAI,CAAC,UAAS;iBACpB,CAAA,CAAA;aACF;QACD,CAAC;QAED,QAAQ,IAAI,OAAM;YACjB,uBAAsB;YACtB,IAAI,IAAI,CAAC,SAAQ,IAAK,CAAC,CAAC,EAAE;gBACzB,IAAI,IAAI,CAAC,SAAQ,GAAI,CAAA,IAAK,IAAI,CAAC,UAAU,CAAC,MAAK,GAAI,IAAI,CAAC,SAAS,EAAE;oBAClE,IAAI,CAAC,SAAQ,GAAI,IAAG,CAAA;oBACpB,IAAI,CAAC,YAAW,GAAI,WAAW,IAAI,CAAC,SAAS,KAAI,CAAA;oBACjD,OAAO,KAAI,CAAA;iBACZ;gBACA,IAAI,CAAC,SAAQ,GAAI,KAAI,CAAA;gBACrB,IAAI,CAAC,YAAW,GAAI,EAAC,CAAA;gBACrB,OAAO,IAAG,CAAA;aACX;YAEA,SAAQ;YACR,IAAI,IAAI,CAAC,UAAU,CAAC,MAAK,GAAI,IAAI,CAAC,SAAS,EAAE;gBAC5C,IAAI,CAAC,SAAQ,GAAI,IAAG,CAAA;gBACpB,IAAI,CAAC,YAAW,GAAI,WAAW,IAAI,CAAC,SAAS,KAAI,CAAA;gBACjD,OAAO,KAAI,CAAA;aACZ;YAEA,SAAQ;YACR,IAAI,IAAI,CAAC,SAAQ,GAAI,CAAA,IAAK,IAAI,CAAC,UAAU,CAAC,MAAK,GAAI,IAAI,CAAC,SAAS,EAAE;gBAClE,IAAI,CAAC,SAAQ,GAAI,IAAG,CAAA;gBACpB,IAAI,CAAC,YAAW,GAAI,WAAW,IAAI,CAAC,SAAS,KAAI,CAAA;gBACjD,OAAO,KAAI,CAAA;aACZ;YAEA,IAAI,CAAC,SAAQ,GAAI,KAAI,CAAA;YACrB,IAAI,CAAC,YAAW,GAAI,EAAC,CAAA;YACrB,OAAO,IAAG,CAAA;QACX,CAAC;QAED,MAAM,CAAC,KAAK,EAAE,eAAe,GAAG,IAAG;YAClC,QAAO;YACP,IAAI,CAAC,UAAS,GAAI,KAAK,CAAC,KAAI,IAAK,MAAK,CAAA;YACtC,OAAM;YACN,IAAI,CAAC,QAAQ,EAAC,CAAA;YACd,UAAS;YACT,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAA,CAAA;QAC3B,CAAC;QACD,YAAY,IAAI,IAAG;YAClB,MAAM,MAAM,EAAE,eAAc,GAAI;gBAC/B,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,KAAK,EAAE,IAAI,CAAC,UAAS;aACtB,CAAA;YACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAA,CAAA;QACnB,CAAC;QACD,OAAO,CAAC,KAAK,EAAE,aAAa,GAAG,IAAG;YACjC,MAAM,MAAM,EAAE,eAAc,GAAI;gBAC/B,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,KAAI;aACzB,CAAA;YACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAA,CAAA;QACnB,CAAC;QAED,MAAM,IAAI,IAAG;YACZ,aAAY;YACZ,IAAI,CAAC,QAAQ,EAAC,CAAA;QACf,CAAA;KACD;CACD,CAAA,CAAA;;;;;;WApLA,GAAA,CAMiB,yBAAA,EAAA,GAAA,CAAA;QANA,KAAK,EAAE,IAAA,CAAA,SAAS;QAAG,YAAU,EAAE,IAAA,CAAA,SAAS;QAAG,GAAG,EAAE,IAAA,CAAA,GAAG;QAAG,eAAa,EAAE,IAAA,CAAA,YAAY;QAAG,aAAW,EAAE,IAAA,CAAA,UAAU;QAC1H,kBAAgB,EAAE,IAAA,CAAA,eAAe;;QACvB,eAAa,EAAA,WAAA,CACvB,IACkB,GAAA,EAAA,CAAA,EAAA,CAAA;YADlB,GAAA,CACkB,UAAA,EAAA,GAAA,CAAA;gBADR,KAAK,EAAC,uBAAuB;4BAAU,IAAA,CAAA,UAAU;wDAAV,IAAA,CAAA,UAAU,CAAA,GAAA,MAAA,CAAA,MAAA,CAAA,KAAA,CAAA,CAAA,CAAA,EAA4D,IAAA,CAAA,OAAO,CAAA;gBAAhE,SAAS,EAAE,IAAA,CAAA,SAAS;gBAAG,WAAW,EAAE,IAAA,CAAA,WAAW;gBAC3G,MAAI,EAAE,IAAA,CAAA,MAAM", "file": "components/main-form/components/form-textarea.uvue", "sourcesContent": ["<template>\n\t<form-container :label=\"fieldName\" :show-error=\"showError\" :tip=\"tip\" :error-message=\"errorMessage\" :label-color=\"labelColor\"\n\t\t:background-color=\"backgroundColor\">\n\t\t<template #input-content>\n\t\t\t<textarea class=\"form-textarea-element\" v-model=\"fieldValue\" :maxlength=\"maxLength\" :placeholder=\"placeholder\" @input=\"onInput\"\n\t\t\t\t@blur=\"onBlur\" />\n\t\t</template>\n\t</form-container>\n</template>\n\n<script lang=\"uts\">\n\timport { FormFieldData, FormChangeEvent } from '@/components/main-form/form_type.uts'\n\timport FormContainer from './form-container.uvue'\n\n\texport default {\n\t\tname: \"FormTextarea\",\n\t\tcomponents: {\n\t\t\tFormContainer\n\t\t},\n\t\tprops: {\n\t\t\tdata: {\n\t\t\t\ttype: Object as PropType<FormFieldData>\n\t\t\t},\n\t\t\tindex: {\n\t\t\t\ttype: Number,\n\t\t\t\tdefault: 0\n\t\t\t},\n\t\t\tkeyName: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"\"\n\t\t\t},\n\t\t\tlabelColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#000\"\n\t\t\t},\n\t\t\tbackgroundColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#f1f4f9\"\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tfieldName: \"\",\n\t\t\t\tfieldValue: \"\",\n\t\t\t\tisSave: false,\n\t\t\t\tsave_key: \"\",\n\t\t\t\ttip: \"\",\n\t\t\t\tplaceholder: \"\",\n\t\t\t\tminLength: 0,\n\t\t\t\tmaxLength: -1,\n\t\t\t\tshowError: false,\n\t\t\t\terrorMessage: \"\"\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\n\t\t},\n\t\twatch: {\n\t\t\tdata: {\n\t\t\t\thandler(obj: FormFieldData) {\n\t\t\t\t\t// 只处理value的变化，当外部传入的value与当前fieldValue不同时，才更新fieldValue\n\t\t\t\t\t// 这避免了用户输入时的循环更新问题\n\t\t\t\t\tconst newValue = obj.value as string\n\t\t\t\t\tif (newValue !== this.fieldValue) {\n\t\t\t\t\t\tthis.fieldValue = newValue\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tdeep: true\n\t\t\t}\n\t\t},\n\t\tcreated(): void {\n\t\t\t// 初始化时调用一次即可\n\t\t\tconst fieldObj = this.$props[\"data\"] as FormFieldData\n\t\t\tthis.initFieldData(fieldObj)\n\t\t},\n\t\tmethods: {\n\t\t\t// 初始化字段数据（仅在首次加载时调用）\n\t\t\tinitFieldData(fieldObj: FormFieldData): void {\n\t\t\t\tconst fieldKey = fieldObj.key\n\t\t\t\tconst fieldValue = fieldObj.value as string\n\n\t\t\t\t// 设置基本信息\n\t\t\t\tthis.fieldName = fieldObj.name\n\t\t\t\tthis.fieldValue = fieldValue\n\t\t\t\tthis.isSave = fieldObj.isSave ?? false\n\t\t\t\tthis.save_key = this.keyName + \"_\" + fieldKey\n\n\t\t\t\t// 解析配置信息（注意：textarea没有inputmode属性）\n\t\t\t\tconst extalJson = fieldObj.extra as UTSJSONObject\n\t\t\t\tthis.minLength = extalJson.getNumber(\"minLength\") ?? 0\n\t\t\t\tthis.maxLength = extalJson.getNumber(\"maxLength\") ?? -1\n\t\t\t\tthis.placeholder = extalJson.getString(\"placeholder\") ?? \"\"\n\t\t\t\tthis.tip = extalJson.getString(\"tip\") ?? \"\"\n\n\t\t\t\t// 获取缓存\n\t\t\t\tthis.getCache()\n\t\t\t},\n\n\t\t\tgetCache(): void {\n\t\t\t\tif (this.isSave) {\n\t\t\t\t\tconst that = this\n\t\t\t\t\tuni.getStorage({\n\t\t\t\t\t\tkey: this.save_key,\n\t\t\t\t\t\tsuccess: (res: GetStorageSuccess) => {\n\t\t\t\t\t\t\tconst save_value = res.data as string\n\t\t\t\t\t\t\tthat.fieldValue = save_value\r\n\t\t\t\t\t\t\tthis.manualChange()\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tsetCache(): void {\n\t\t\t\tif (this.isSave) {\n\t\t\t\t\tuni.setStorage({\n\t\t\t\t\t\tkey: this.save_key,\n\t\t\t\t\t\tdata: this.fieldValue\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tvalidate(): boolean {\n\t\t\t\t// 如果没有设置最大长度限制，只检查最小长度\n\t\t\t\tif (this.maxLength == -1) {\n\t\t\t\t\tif (this.minLength > 0 && this.fieldValue.length < this.minLength) {\n\t\t\t\t\t\tthis.showError = true\n\t\t\t\t\t\tthis.errorMessage = `输入内容不能少于${this.minLength}个字符`\n\t\t\t\t\t\treturn false\n\t\t\t\t\t}\n\t\t\t\t\tthis.showError = false\n\t\t\t\t\tthis.errorMessage = \"\"\n\t\t\t\t\treturn true\n\t\t\t\t}\n\n\t\t\t\t// 检查最小长度\n\t\t\t\tif (this.fieldValue.length < this.minLength) {\n\t\t\t\t\tthis.showError = true\n\t\t\t\t\tthis.errorMessage = `输入内容不能少于${this.minLength}个字符`\n\t\t\t\t\treturn false\n\t\t\t\t}\n\n\t\t\t\t// 检查最大长度\n\t\t\t\tif (this.maxLength > 0 && this.fieldValue.length > this.maxLength) {\n\t\t\t\t\tthis.showError = true\n\t\t\t\t\tthis.errorMessage = `输入内容不能超过${this.maxLength}个字符`\n\t\t\t\t\treturn false\n\t\t\t\t}\n\n\t\t\t\tthis.showError = false\n\t\t\t\tthis.errorMessage = \"\"\n\t\t\t\treturn true\n\t\t\t},\n\t\t\t\n\t\t\tchange(event: FormChangeEvent): void {\n\t\t\t\t// 更新字段值\n\t\t\t\tthis.fieldValue = event.value as string\n\t\t\t\t// 保存缓存\n\t\t\t\tthis.setCache()\n\t\t\t\t// 触发父组件事件\n\t\t\t\tthis.$emit('change', event)\n\t\t\t},\n\t\t\tmanualChange(): void {\r\n\t\t\t\tconst result: FormChangeEvent = {\r\n\t\t\t\t\tindex: this.index,\r\n\t\t\t\t\tvalue: this.fieldValue\r\n\t\t\t\t}\r\n\t\t\t\tthis.change(result)\r\n\t\t\t},\n\t\t\tonInput(event: UniInputEvent): void {\n\t\t\t\tconst result: FormChangeEvent = {\n\t\t\t\t\tindex: this.index,\n\t\t\t\t\tvalue: event.detail.value\n\t\t\t\t}\n\t\t\t\tthis.change(result)\n\t\t\t},\n\n\t\t\tonBlur(): void {\n\t\t\t\t// 在失去焦点时进行验证\n\t\t\t\tthis.validate()\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t.form-textarea-element {\n\t\tflex: 1;\n\t\tmin-height: 120rpx;\n\t}\n</style>\n"]}