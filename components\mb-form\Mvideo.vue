<template>
	<view class="Mvideo">
		<view class="Mvideo-name" :style="{color:color}">
			{{data.name}}
		</view>
	
		<view class="Mvideo-box qShadow1" :style="{backgroundColor:bgColor}" >
			<view class="mb-defaultImage" v-if="mValue==''" @click="selectImage">
				<mb-icons type="Add-1" color="#666" size="60rpx"></mb-icons>
				
			</view>
			
			<view class="mb-showImage" v-else>
				<video :src="mValue" mode=""  ></video>
			</view>
			
			
		</view>
		<view class="mb-changeBtn" v-if="mValue!=''" @click="selectImage">
			
			<text>重新选择</text>
		</view>
	</view>
	
	
	
</template>

<script>
	export default {
		name: "Mvideo",
		props: {
			data: {
				type: Object,
				default: {}
			},
			keyName: {
				type: String,
				default: ""
			
			},
			index: {
				type: Number,
				default: 0
			},
			color: {
				type: String,
				default: '#000'
			},
			bgColor: {
				type: String,
				default: "#f1f4f9"
			
			}
		},
		created() {
			
			
			if(this.data.value!=''){
				this.mValue=this.data.value
			}
			
		
		},
		data() {
			return {
				mValue:""
			};
		},
		watch:{
			data: {
				
				handler(newValue, oldValue) {
					this.mValue= this.data.value
					
				},
				deep: true
			}
			
		},
		methods:{
			
			clear(){
				this.mValue=""
				this.$emit("change",{index:this.index,value:""})
			},
			selectImage(){
				this.$mb.selectVideo().then(res=>{
					this.mValue=res
					this.$emit("change",{index:this.index,value:res})
				})
			}
		}
	}
</script>

<style scoped>
.Mvideo {
		width: 100%;
		display: flex;
		flex-direction: column;
		padding: 0 20rpx;
		margin-bottom: 20rpx;
	
	}
	
	.Mvideo-name {
		width: 100%;
		font-size: 32rpx;
		margin-bottom: 10rpx;
	}
	
	.Mvideo-box {
		width: 100%;
		height:350rpx;
		border-radius: 20rpx;
		background-color: #f2f4f8;
		position: relative;
	}

	.mb-defaultImage{
		width: 100%;
		height: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
	}
	.mb-showImage{
		width: 100%;
		height: 100%;
	}
	.mb-showImage video{
		width: 100%;
		height: 100%;
	}
	.mb-changeBtn{
		width: 160rpx;
		height: 80rpx;
		text-align: center;
		line-height: 80rpx;
		margin-top: 20rpx;
		background-color: #eee;
		color: #000;
		border-radius: 10rpx;
	}
	
</style>