
export type IconsData = {
	id : string
	name : string
	font_family : string
	css_prefix_text : string
	description : string
	glyphs : Array<IconsDataItem>
}

export type IconsDataItem = {
	font_class : string
	unicode : string
}


export const fontData = [
 {
   "font_class": "Briefcase-1",
   "unicode": "\ue6ca"
 },
 {
   "font_class": "Drink-1",
   "unicode": "\ue7ca"
 },
 {
   "font_class": "MyLocation",
   "unicode": "\ue8ca"
 },
 {
   "font_class": "Store",
   "unicode": "\ue9ca"
 },
 {
   "font_class": "TapSingle-1",
   "unicode": "\ueaca"
 },
 {
   "font_class": "CalendarAgenda-1",
   "unicode": "\ue6cb"
 },
 {
   "font_class": "Drag",
   "unicode": "\ue7cb"
 },
 {
   "font_class": "News",
   "unicode": "\ue8cb"
 },
 {
   "font_class": "KeyboardDock",
   "unicode": "\ue9cb"
 },
 {
   "font_class": "Whiteboard-1",
   "unicode": "\ueacb"
 },
 {
   "font_class": "BookFormulaStatistics-1",
   "unicode": "\ue6cc"
 },
 {
   "font_class": "DriveMode-1",
   "unicode": "\ue7cc"
 },
 {
   "font_class": "More-1",
   "unicode": "\ue8cc"
 },
 {
   "font_class": "Subway",
   "unicode": "\ue9cc"
 },
 {
   "font_class": "USBStick-1",
   "unicode": "\ueacc"
 },
 {
   "font_class": "Briefcase",
   "unicode": "\ue6cd"
 },
 {
   "font_class": "ConvertToText",
   "unicode": "\ue7cd"
 },
 {
   "font_class": "NetworkCheck-1",
   "unicode": "\ue8cd"
 },
 {
   "font_class": "SubGrid",
   "unicode": "\ue9cd"
 },
 {
   "font_class": "Target-1",
   "unicode": "\ueacd"
 },
 {
   "font_class": "CalendarCancel-1",
   "unicode": "\ue6ce"
 },
 {
   "font_class": "DockRow-1",
   "unicode": "\ue7ce"
 },
 {
   "font_class": "NotebookError-1",
   "unicode": "\ue8ce"
 },
 {
   "font_class": "SurfaceHub",
   "unicode": "\ue9ce"
 },
 {
   "font_class": "WeatherSnowflake-1",
   "unicode": "\ueace"
 },
 {
   "font_class": "BookFormulaLookup-1",
   "unicode": "\ue6cf"
 },
 {
   "font_class": "EmojiSad",
   "unicode": "\ue7cf"
 },
 {
   "font_class": "News-1",
   "unicode": "\ue8cf"
 },
 {
   "font_class": "Suggestion",
   "unicode": "\ue9cf"
 },
 {
   "font_class": "LauncherSettings",
   "unicode": "\ueacf"
 },
 {
   "font_class": "Building-1",
   "unicode": "\ue6d0"
 },
 {
   "font_class": "EmojiAngry",
   "unicode": "\ue7d0"
 },
 {
   "font_class": "Drag-1",
   "unicode": "\ue8d0"
 },
 {
   "font_class": "Signed-1",
   "unicode": "\ue9d0"
 },
 {
   "font_class": "TextQuote",
   "unicode": "\uead0"
 },
 {
   "font_class": "BookFormulaText-1",
   "unicode": "\ue6d1"
 },
 {
   "font_class": "ContactCardGroup",
   "unicode": "\ue7d1"
 },
 {
   "font_class": "NotebookError",
   "unicode": "\ue8d1"
 },
 {
   "font_class": "SurfaceHub-1",
   "unicode": "\ue9d1"
 },
 {
   "font_class": "WeatherRainSnow",
   "unicode": "\uead1"
 },
 {
   "font_class": "CalendarCancel",
   "unicode": "\ue6d2"
 },
 {
   "font_class": "EmojiSad-1",
   "unicode": "\ue7d2"
 },
 {
   "font_class": "NotebookSection",
   "unicode": "\ue8d2"
 },
 {
   "font_class": "SurfaceEarbuds-1",
   "unicode": "\ue9d2"
 },
 {
   "font_class": "MobileOptimized-1",
   "unicode": "\uead2"
 },
 {
   "font_class": "CalendarRecurring-1",
   "unicode": "\ue6d3"
 },
 {
   "font_class": "Edit-1",
   "unicode": "\ue7d3"
 },
 {
   "font_class": "DoubleTapSwipeUp",
   "unicode": "\ue8d3"
 },
 {
   "font_class": "SurfaceEarbuds",
   "unicode": "\ue9d3"
 },
 {
   "font_class": "WeatherRainShowersDay",
   "unicode": "\uead3"
 },
 {
   "font_class": "BugReport-1",
   "unicode": "\ue6d4"
 },
 {
   "font_class": "EmojiAngry-1",
   "unicode": "\ue7d4"
 },
 {
   "font_class": "NotebookQuestionMark-1",
   "unicode": "\ue8d4"
 },
 {
   "font_class": "Suggestion-1",
   "unicode": "\ue9d4"
 },
 {
   "font_class": "SwitchVideo",
   "unicode": "\uead4"
 },
 {
   "font_class": "CalendarStarred",
   "unicode": "\ue6d5"
 },
 {
   "font_class": "EmojiAdd",
   "unicode": "\ue7d5"
 },
 {
   "font_class": "NotebookLightning-1",
   "unicode": "\ue8d5"
 },
 {
   "font_class": "SwipeDown-1",
   "unicode": "\ue9d5"
 },
 {
   "font_class": "SwipeRight-1",
   "unicode": "\uead5"
 },
 {
   "font_class": "Calendar-1",
   "unicode": "\ue6d6"
 },
 {
   "font_class": "DriveMode",
   "unicode": "\ue7d6"
 },
 {
   "font_class": "NotebookSection-1",
   "unicode": "\ue8d6"
 },
 {
   "font_class": "TabInPrivate-1",
   "unicode": "\ue9d6"
 },
 {
   "font_class": "System",
   "unicode": "\uead6"
 },
 {
   "font_class": "CalendarAvailability-1",
   "unicode": "\ue6d7"
 },
 {
   "font_class": "EmojiSurprise-1",
   "unicode": "\ue7d7"
 },
 {
   "font_class": "NotebookQuestionMark",
   "unicode": "\ue8d7"
 },
 {
   "font_class": "SwipeUp",
   "unicode": "\ue9d7"
 },
 {
   "font_class": "TabSweep-1",
   "unicode": "\uead7"
 },
 {
   "font_class": "BugReport",
   "unicode": "\ue6d8"
 },
 {
   "font_class": "EraserTool-1",
   "unicode": "\ue7d8"
 },
 {
   "font_class": "NotebookLightning",
   "unicode": "\ue8d8"
 },
 {
   "font_class": "SwipeUp-1",
   "unicode": "\ue9d8"
 },
 {
   "font_class": "Symbols-1",
   "unicode": "\uead8"
 },
 {
   "font_class": "Calendar3Day",
   "unicode": "\ue6d9"
 },
 {
   "font_class": "Emoji-1",
   "unicode": "\ue7d9"
 },
 {
   "font_class": "Edit",
   "unicode": "\ue8d9"
 },
 {
   "font_class": "System-1",
   "unicode": "\ue9d9"
 },
 {
   "font_class": "TextNumberListRTL",
   "unicode": "\uead9"
 },
 {
   "font_class": "CalendarAgenda",
   "unicode": "\ue6da"
 },
 {
   "font_class": "Emoji",
   "unicode": "\ue7da"
 },
 {
   "font_class": "Drink",
   "unicode": "\ue8da"
 },
 {
   "font_class": "KeyboardTab",
   "unicode": "\ue9da"
 },
 {
   "font_class": "SoundSource",
   "unicode": "\ueada"
 },
 {
   "font_class": "CalendarDate-1",
   "unicode": "\ue6db"
 },
 {
   "font_class": "Erase",
   "unicode": "\ue7db"
 },
 {
   "font_class": "EmojiAdd-1",
   "unicode": "\ue8db"
 },
 {
   "font_class": "SwipeDown",
   "unicode": "\ue9db"
 },
 {
   "font_class": "TextIndentIncrease-1",
   "unicode": "\ueadb"
 },
 {
   "font_class": "CalendarMonth",
   "unicode": "\ue6dc"
 },
 {
   "font_class": "CopyMove-1",
   "unicode": "\ue7dc"
 },
 {
   "font_class": "NumberRow-1",
   "unicode": "\ue8dc"
 },
 {
   "font_class": "Symbols",
   "unicode": "\ue9dc"
 },
 {
   "font_class": "TextNumberListLTR-1",
   "unicode": "\ueadc"
 },
 {
   "font_class": "CalendarStarred-1",
   "unicode": "\ue6dd"
 },
 {
   "font_class": "Erase-1",
   "unicode": "\ue7dd"
 },
 {
   "font_class": "NotebookSync-1",
   "unicode": "\ue8dd"
 },
 {
   "font_class": "WeatherFog-1",
   "unicode": "\ue9dd"
 },
 {
   "font_class": "TextHanging-1",
   "unicode": "\ueadd"
 },
 {
   "font_class": "BookFormulaInformation",
   "unicode": "\ue6de"
 },
 {
   "font_class": "ErrorCircle-1",
   "unicode": "\ue7de"
 },
 {
   "font_class": "Notebook-1",
   "unicode": "\ue8de"
 },
 {
   "font_class": "Trophy-1",
   "unicode": "\ue9de"
 },
 {
   "font_class": "WeatherDuststorm",
   "unicode": "\ueade"
 },
 {
   "font_class": "CalendarWeekNumbers-1",
   "unicode": "\ue6df"
 },
 {
   "font_class": "PeopleSearch-1",
   "unicode": "\ue7df"
 },
 {
   "font_class": "NumberRow",
   "unicode": "\ue8df"
 },
 {
   "font_class": "WeatherDuststorm-1",
   "unicode": "\ue9df"
 },
 {
   "font_class": "DocumentPDF-1",
   "unicode": "\ueadf"
 },
 {
   "font_class": "CalendarMonth-1",
   "unicode": "\ue6e0"
 },
 {
   "font_class": "PersonAccounts-1",
   "unicode": "\ue7e0"
 },
 {
   "font_class": "OfficeApps-1",
   "unicode": "\ue8e0"
 },
 {
   "font_class": "TextHanging",
   "unicode": "\ue9e0"
 },
 {
   "font_class": "VideoOff-1",
   "unicode": "\ueae0"
 },
 {
   "font_class": "CalendarAvailability",
   "unicode": "\ue6e1"
 },
 {
   "font_class": "ExtendedDock-1",
   "unicode": "\ue7e1"
 },
 {
   "font_class": "NumberSymbol",
   "unicode": "\ue8e1"
 },
 {
   "font_class": "TextIndentDecrease-1",
   "unicode": "\ue9e1"
 },
 {
   "font_class": "WiFi2-1",
   "unicode": "\ueae1"
 },
 {
   "font_class": "CalendarEmpty",
   "unicode": "\ue6e2"
 },
 {
   "font_class": "EraserTool",
   "unicode": "\ue7e2"
 },
 {
   "font_class": "EmojiSurprise",
   "unicode": "\ue8e2"
 },
 {
   "font_class": "MarkRead-1",
   "unicode": "\ue9e2"
 },
 {
   "font_class": "TextBulletListSquare",
   "unicode": "\ueae2"
 },
 {
   "font_class": "CalendarDate",
   "unicode": "\ue6e3"
 },
 {
   "font_class": "Extension-1",
   "unicode": "\ue7e3"
 },
 {
   "font_class": "NumberSymbol-1",
   "unicode": "\ue8e3"
 },
 {
   "font_class": "USBPort",
   "unicode": "\ue9e3"
 },
 {
   "font_class": "TeamAdd",
   "unicode": "\ueae3"
 },
 {
   "font_class": "CallEnd-1",
   "unicode": "\ue6e4"
 },
 {
   "font_class": "EyeShow-1",
   "unicode": "\ue7e4"
 },
 {
   "font_class": "OfficeApps",
   "unicode": "\ue8e4"
 },
 {
   "font_class": "TextColor",
   "unicode": "\ue9e4"
 },
 {
   "font_class": "Speaker1",
   "unicode": "\ueae4"
 },
 {
   "font_class": "CalendarRecurring",
   "unicode": "\ue6e5"
 },
 {
   "font_class": "EyeShow",
   "unicode": "\ue7e5"
 },
 {
   "font_class": "OpenFolder-1",
   "unicode": "\ue8e5"
 },
 {
   "font_class": "ThumbLike-1",
   "unicode": "\ue9e5"
 },
 {
   "font_class": "Navigation-1",
   "unicode": "\ueae5"
 },
 {
   "font_class": "CallEnd",
   "unicode": "\ue6e6"
 },
 {
   "font_class": "EyeHide",
   "unicode": "\ue7e6"
 },
 {
   "font_class": "OpenInBrowser-1",
   "unicode": "\ue8e6"
 },
 {
   "font_class": "ThumbDislike",
   "unicode": "\ue9e6"
 },
 {
   "font_class": "Video-1",
   "unicode": "\ueae6"
 },
 {
   "font_class": "CallAdd-1",
   "unicode": "\ue6e7"
 },
 {
   "font_class": "ChevronLeft-1",
   "unicode": "\ue7e7"
 },
 {
   "font_class": "Open-1",
   "unicode": "\ue8e7"
 },
 {
   "font_class": "TextDescription-1",
   "unicode": "\ue9e7"
 },
 {
   "font_class": "MicOff-1",
   "unicode": "\ueae7"
 },
 {
   "font_class": "CalendarWeekStart-1",
   "unicode": "\ue6e8"
 },
 {
   "font_class": "FastForward",
   "unicode": "\ue7e8"
 },
 {
   "font_class": "OpenFolder",
   "unicode": "\ue8e8"
 },
 {
   "font_class": "TextIndentDecrease",
   "unicode": "\ue9e8"
 },
 {
   "font_class": "OpenInBrowser",
   "unicode": "\ueae8"
 },
 {
   "font_class": "BookFormulaLookup",
   "unicode": "\ue6e9"
 },
 {
   "font_class": "Extension",
   "unicode": "\ue7e9"
 },
 {
   "font_class": "OrganizationChart-1",
   "unicode": "\ue8e9"
 },
 {
   "font_class": "WebAsset-1",
   "unicode": "\ue9e9"
 },
 {
   "font_class": "Note-1",
   "unicode": "\ueae9"
 },
 {
   "font_class": "CalendarWeekNumbers",
   "unicode": "\ue6ea"
 },
 {
   "font_class": "FastAcceleration",
   "unicode": "\ue7ea"
 },
 {
   "font_class": "Owner",
   "unicode": "\ue8ea"
 },
 {
   "font_class": "WebAsset",
   "unicode": "\ue9ea"
 },
 {
   "font_class": "Speaker",
   "unicode": "\ueaea"
 },
 {
   "font_class": "CalendarDay",
   "unicode": "\ue6eb"
 },
 {
   "font_class": "FastForward-1",
   "unicode": "\ue7eb"
 },
 {
   "font_class": "OrganizationChart",
   "unicode": "\ue8eb"
 },
 {
   "font_class": "PhoneLaptop-1",
   "unicode": "\ue9eb"
 },
 {
   "font_class": "WeatherFog",
   "unicode": "\ueaeb"
 },
 {
   "font_class": "CalendarWeekStart",
   "unicode": "\ue6ec"
 },
 {
   "font_class": "FastMode",
   "unicode": "\ue7ec"
 },
 {
   "font_class": "PageView",
   "unicode": "\ue8ec"
 },
 {
   "font_class": "Temperature-1",
   "unicode": "\ue9ec"
 },
 {
   "font_class": "ViewDesktopMobile",
   "unicode": "\ueaec"
 },
 {
   "font_class": "CallForward",
   "unicode": "\ue6ed"
 },
 {
   "font_class": "Filter-1",
   "unicode": "\ue7ed"
 },
 {
   "font_class": "PageView-1",
   "unicode": "\ue8ed"
 },
 {
   "font_class": "WeatherRainShowersDay-1",
   "unicode": "\ue9ed"
 },
 {
   "font_class": "WeatherMoon",
   "unicode": "\ueaed"
 },
 {
   "font_class": "CallInbound-1",
   "unicode": "\ue6ee"
 },
 {
   "font_class": "Filter",
   "unicode": "\ue7ee"
 },
 {
   "font_class": "Open",
   "unicode": "\ue8ee"
 },
 {
   "font_class": "PhoneError-1",
   "unicode": "\ue9ee"
 },
 {
   "font_class": "Unlock-1",
   "unicode": "\ueaee"
 },
 {
   "font_class": "CalendarEmpty-1",
   "unicode": "\ue6ef"
 },
 {
   "font_class": "FastAcceleration-1",
   "unicode": "\ue7ef"
 },
 {
   "font_class": "Patient-1",
   "unicode": "\ue8ef"
 },
 {
   "font_class": "USBStick",
   "unicode": "\ue9ef"
 },
 {
   "font_class": "TripleColumn-1",
   "unicode": "\ueaef"
 },
 {
   "font_class": "Camera",
   "unicode": "\ue6f0"
 },
 {
   "font_class": "FitWidth-1",
   "unicode": "\ue7f0"
 },
 {
   "font_class": "Patient",
   "unicode": "\ue8f0"
 },
 {
   "font_class": "TabInPrivate",
   "unicode": "\ue9f0"
 },
 {
   "font_class": "Mail",
   "unicode": "\ueaf0"
 },
 {
   "font_class": "CallMissed",
   "unicode": "\ue6f1"
 },
 {
   "font_class": "Flag",
   "unicode": "\ue7f1"
 },
 {
   "font_class": "Payment",
   "unicode": "\ue8f1"
 },
 {
   "font_class": "WeatherRainSnow-1",
   "unicode": "\ue9f1"
 },
 {
   "font_class": "TextLineSpacing-1",
   "unicode": "\ueaf1"
 },
 {
   "font_class": "CallInbound",
   "unicode": "\ue6f2"
 },
 {
   "font_class": "FitWidth",
   "unicode": "\ue7f2"
 },
 {
   "font_class": "Pause",
   "unicode": "\ue8f2"
 },
 {
   "font_class": "VideoPlayPause-1",
   "unicode": "\ue9f2"
 },
 {
   "font_class": "ViewDesktop-1",
   "unicode": "\ueaf2"
 },
 {
   "font_class": "CallMissed-1",
   "unicode": "\ue6f3"
 },
 {
   "font_class": "Flag-1",
   "unicode": "\ue7f3"
 },
 {
   "font_class": "Payment-1",
   "unicode": "\ue8f3"
 },
 {
   "font_class": "WeatherThunderstorm",
   "unicode": "\ue9f3"
 },
 {
   "font_class": "TableAdd-1",
   "unicode": "\ueaf3"
 },
 {
   "font_class": "Cast-1",
   "unicode": "\ue6f4"
 },
 {
   "font_class": "Fingerprint",
   "unicode": "\ue7f4"
 },
 {
   "font_class": "Pair-1",
   "unicode": "\ue8f4"
 },
 {
   "font_class": "Weekend-1",
   "unicode": "\ue9f4"
 },
 {
   "font_class": "MoviesandTV-1",
   "unicode": "\ueaf4"
 },
 {
   "font_class": "Cellular4G",
   "unicode": "\ue6f5"
 },
 {
   "font_class": "FlashOn-1",
   "unicode": "\ue7f5"
 },
 {
   "font_class": "Pair",
   "unicode": "\ue8f5"
 },
 {
   "font_class": "WeatherHailNight-1",
   "unicode": "\ue9f5"
 },
 {
   "font_class": "TimeAndWeather",
   "unicode": "\ueaf5"
 },
 {
   "font_class": "CallAdd",
   "unicode": "\ue6f6"
 },
 {
   "font_class": "DataArea-1",
   "unicode": "\ue7f6"
 },
 {
   "font_class": "PenSettings-1",
   "unicode": "\ue8f6"
 },
 {
   "font_class": "Weekend",
   "unicode": "\ue9f6"
 },
 {
   "font_class": "Translate-1",
   "unicode": "\ueaf6"
 },
 {
   "font_class": "CellularData2",
   "unicode": "\ue6f7"
 },
 {
   "font_class": "FlashOn",
   "unicode": "\ue7f7"
 },
 {
   "font_class": "PeopleCommunity-1",
   "unicode": "\ue8f7"
 },
 {
   "font_class": "WiFi4-1",
   "unicode": "\ue9f7"
 },
 {
   "font_class": "WeatherSnowflake",
   "unicode": "\ueaf7"
 },
 {
   "font_class": "Cellular3G",
   "unicode": "\ue6f8"
 },
 {
   "font_class": "FlashOff",
   "unicode": "\ue7f8"
 },
 {
   "font_class": "PeopleCommunityAdd-1",
   "unicode": "\ue8f8"
 },
 {
   "font_class": "TabTrackingPrevention",
   "unicode": "\ue9f8"
 },
 {
   "font_class": "Wallpaper-1",
   "unicode": "\ueaf8"
 },
 {
   "font_class": "CellularData1-1",
   "unicode": "\ue6f9"
 },
 {
   "font_class": "FlashAuto-1",
   "unicode": "\ue7f9"
 },
 {
   "font_class": "PeopleAdd",
   "unicode": "\ue8f9"
 },
 {
   "font_class": "WeatherSnowShowerNight-1",
   "unicode": "\ue9f9"
 },
 {
   "font_class": "ThumbDislike-1",
   "unicode": "\ueaf9"
 },
 {
   "font_class": "CallForward-1",
   "unicode": "\ue6fa"
 },
 {
   "font_class": "FlashAuto",
   "unicode": "\ue7fa"
 },
 {
   "font_class": "PeopleAdd-1",
   "unicode": "\ue8fa"
 },
 {
   "font_class": "WiFi3",
   "unicode": "\ue9fa"
 },
 {
   "font_class": "TabNew",
   "unicode": "\ueafa"
 },
 {
   "font_class": "Cellular3G-1",
   "unicode": "\ue6fb"
 },
 {
   "font_class": "PhoneUpdate",
   "unicode": "\ue7fb"
 },
 {
   "font_class": "PenSettings",
   "unicode": "\ue8fb"
 },
 {
   "font_class": "PSTNCall-1",
   "unicode": "\ue9fb"
 },
 {
   "font_class": "Thinking-1",
   "unicode": "\ueafb"
 },
 {
   "font_class": "CellularData1",
   "unicode": "\ue6fc"
 },
 {
   "font_class": "Flashlight-1",
   "unicode": "\ue7fc"
 },
 {
   "font_class": "PeopleCommunityAdd",
   "unicode": "\ue8fc"
 },
 {
   "font_class": "Shield-1",
   "unicode": "\ue9fc"
 },
 {
   "font_class": "TextQuote-1",
   "unicode": "\ueafc"
 },
 {
   "font_class": "ClipboardText",
   "unicode": "\ue6fd"
 },
 {
   "font_class": "FolderJunk",
   "unicode": "\ue7fd"
 },
 {
   "font_class": "People-1",
   "unicode": "\ue8fd"
 },
 {
   "font_class": "WindowDevTools",
   "unicode": "\ue9fd"
 },
 {
   "font_class": "TextUnderline-1",
   "unicode": "\ueafd"
 },
 {
   "font_class": "CellularData5-1",
   "unicode": "\ue6fe"
 },
 {
   "font_class": "FlashlightOff",
   "unicode": "\ue7fe"
 },
 {
   "font_class": "People",
   "unicode": "\ue8fe"
 },
 {
   "font_class": "SelectAllOff",
   "unicode": "\ue9fe"
 },
 {
   "font_class": "TextItalic",
   "unicode": "\ueafe"
 },
 {
   "font_class": "CellularData4-1",
   "unicode": "\ue6ff"
 },
 {
   "font_class": "FolderAdd-1",
   "unicode": "\ue7ff"
 },
 {
   "font_class": "ErrorCircle",
   "unicode": "\ue8ff"
 },
 {
   "font_class": "ShareScreen-1",
   "unicode": "\ue9ff"
 },
 {
   "font_class": "TextFont-1",
   "unicode": "\ueaff"
 },
 {
   "font_class": "CellularData3",
   "unicode": "\ue700"
 },
 {
   "font_class": "Phone",
   "unicode": "\ue800"
 },
 {
   "font_class": "PeopleTeam",
   "unicode": "\ue900"
 },
 {
   "font_class": "FontSpaceTrackingIn",
   "unicode": "\uea00"
 },
 {
   "font_class": "Ticket",
   "unicode": "\ueb00"
 },
 {
   "font_class": "CellularData2-1",
   "unicode": "\ue701"
 },
 {
   "font_class": "FlagOff",
   "unicode": "\ue801"
 },
 {
   "font_class": "PeopleCommunity",
   "unicode": "\ue901"
 },
 {
   "font_class": "Rewind",
   "unicode": "\uea01"
 },
 {
   "font_class": "MoviesandTV",
   "unicode": "\ueb01"
 },
 {
   "font_class": "CellularData3-1",
   "unicode": "\ue702"
 },
 {
   "font_class": "FolderLink-1",
   "unicode": "\ue802"
 },
 {
   "font_class": "PeopleSearch",
   "unicode": "\ue902"
 },
 {
   "font_class": "RadioButton",
   "unicode": "\uea02"
 },
 {
   "font_class": "UninstallApp",
   "unicode": "\ueb02"
 },
 {
   "font_class": "Accessibility",
   "unicode": "\ue603"
 },
 {
   "font_class": "CellularData5",
   "unicode": "\ue703"
 },
 {
   "font_class": "PlayCircle",
   "unicode": "\ue803"
 },
 {
   "font_class": "ExtendedDock",
   "unicode": "\ue903"
 },
 {
   "font_class": "ReadAloud-1",
   "unicode": "\uea03"
 },
 {
   "font_class": "TabNew-1",
   "unicode": "\ueb03"
 },
 {
   "font_class": "shenglve",
   "unicode": "\ue604"
 },
 {
   "font_class": "CellularOff-1",
   "unicode": "\ue704"
 },
 {
   "font_class": "FolderMove",
   "unicode": "\ue804"
 },
 {
   "font_class": "PersonBlock",
   "unicode": "\ue904"
 },
 {
   "font_class": "Presenter-1",
   "unicode": "\uea04"
 },
 {
   "font_class": "WeatherSnowShowerDay-1",
   "unicode": "\ueb04"
 },
 {
   "font_class": "Accessibility-1",
   "unicode": "\ue605"
 },
 {
   "font_class": "CellularUnavailable",
   "unicode": "\ue705"
 },
 {
   "font_class": "FolderLink",
   "unicode": "\ue805"
 },
 {
   "font_class": "PersonAdd-1",
   "unicode": "\ue905"
 },
 {
   "font_class": "Predictions-1",
   "unicode": "\uea05"
 },
 {
   "font_class": "TextAlignLeft-1",
   "unicode": "\ueb05"
 },
 {
   "font_class": "AccessTime-1",
   "unicode": "\ue606"
 },
 {
   "font_class": "CellularOff",
   "unicode": "\ue706"
 },
 {
   "font_class": "FolderOpen-1",
   "unicode": "\ue806"
 },
 {
   "font_class": "PersonAvailable-1",
   "unicode": "\ue906"
 },
 {
   "font_class": "PPTOutline",
   "unicode": "\uea06"
 },
 {
   "font_class": "AddCircle",
   "unicode": "\ue607"
 },
 {
   "font_class": "CellularUnavailable-1",
   "unicode": "\ue707"
 },
 {
   "font_class": "FolderOpen",
   "unicode": "\ue807"
 },
 {
   "font_class": "PersonDelete-1",
   "unicode": "\ue907"
 },
 {
   "font_class": "PPTOutline-1",
   "unicode": "\uea07"
 },
 {
   "font_class": "AccessTime",
   "unicode": "\ue608"
 },
 {
   "font_class": "CalendarDay-1",
   "unicode": "\ue708"
 },
 {
   "font_class": "Folder",
   "unicode": "\ue808"
 },
 {
   "font_class": "PersonAccounts",
   "unicode": "\ue908"
 },
 {
   "font_class": "Premium",
   "unicode": "\uea08"
 },
 {
   "font_class": "AddCircle-1",
   "unicode": "\ue609"
 },
 {
   "font_class": "Cast",
   "unicode": "\ue709"
 },
 {
   "font_class": "FontDecrease-1",
   "unicode": "\ue809"
 },
 {
   "font_class": "PersonAvailable",
   "unicode": "\ue909"
 },
 {
   "font_class": "FlashlightOff-1",
   "unicode": "\uea09"
 },
 {
   "font_class": "Activity-1",
   "unicode": "\ue60a"
 },
 {
   "font_class": "ChannelNotifications",
   "unicode": "\ue70a"
 },
 {
   "font_class": "FontIncrease",
   "unicode": "\ue80a"
 },
 {
   "font_class": "EyeHide-1",
   "unicode": "\ue90a"
 },
 {
   "font_class": "WiFi2",
   "unicode": "\uea0a"
 },
 {
   "font_class": "Activity",
   "unicode": "\ue60b"
 },
 {
   "font_class": "ChannelUnfollow",
   "unicode": "\ue70b"
 },
 {
   "font_class": "Folder-1",
   "unicode": "\ue80b"
 },
 {
   "font_class": "PersonFeedback",
   "unicode": "\ue90b"
 },
 {
   "font_class": "XboxConsole",
   "unicode": "\uea0b"
 },
 {
   "font_class": "Add",
   "unicode": "\ue60c"
 },
 {
   "font_class": "ChevronRight-1",
   "unicode": "\ue70c"
 },
 {
   "font_class": "FontDecrease",
   "unicode": "\ue80c"
 },
 {
   "font_class": "PersonLeave-1",
   "unicode": "\ue90c"
 },
 {
   "font_class": "ReOrder",
   "unicode": "\uea0c"
 },
 {
   "font_class": "AddressBookNumber",
   "unicode": "\ue60d"
 },
 {
   "font_class": "ChannelUnfollow-1",
   "unicode": "\ue70d"
 },
 {
   "font_class": "FontSpaceTrackingIn-1",
   "unicode": "\ue80d"
 },
 {
   "font_class": "PersonTentative",
   "unicode": "\ue90d"
 },
 {
   "font_class": "Tab-1",
   "unicode": "\uea0d"
 },
 {
   "font_class": "AddressBookNumber-1",
   "unicode": "\ue60e"
 },
 {
   "font_class": "ChevronRight",
   "unicode": "\ue70e"
 },
 {
   "font_class": "Food-1",
   "unicode": "\ue80e"
 },
 {
   "font_class": "PersonLeave",
   "unicode": "\ue90e"
 },
 {
   "font_class": "TextFont",
   "unicode": "\uea0e"
 },
 {
   "font_class": "Airplane-1",
   "unicode": "\ue60f"
 },
 {
   "font_class": "Channel",
   "unicode": "\ue70f"
 },
 {
   "font_class": "Food",
   "unicode": "\ue80f"
 },
 {
   "font_class": "PersonTentative-1",
   "unicode": "\ue90f"
 },
 {
   "font_class": "WindowDevTools-1",
   "unicode": "\uea0f"
 },
 {
   "font_class": "Add-1",
   "unicode": "\ue610"
 },
 {
   "font_class": "ChevronUp-1",
   "unicode": "\ue710"
 },
 {
   "font_class": "FontSpaceTrackingOut-1",
   "unicode": "\ue810"
 },
 {
   "font_class": "PersonDelete",
   "unicode": "\ue910"
 },
 {
   "font_class": "TabSweep",
   "unicode": "\uea10"
 },
 {
   "font_class": "AlertOff-1",
   "unicode": "\ue611"
 },
 {
   "font_class": "ChevronUp",
   "unicode": "\ue711"
 },
 {
   "font_class": "FontSpaceTrackingOut",
   "unicode": "\ue811"
 },
 {
   "font_class": "PersonFeedback-1",
   "unicode": "\ue911"
 },
 {
   "font_class": "WiFi4",
   "unicode": "\uea11"
 },
 {
   "font_class": "AlertOn-1",
   "unicode": "\ue612"
 },
 {
   "font_class": "Class-1",
   "unicode": "\ue712"
 },
 {
   "font_class": "FullScreenZoom-1",
   "unicode": "\ue812"
 },
 {
   "font_class": "PhoneAddNewApp",
   "unicode": "\ue912"
 },
 {
   "font_class": "ReadingModeMobile",
   "unicode": "\uea12"
 },
 {
   "font_class": "AlertSnooze-1",
   "unicode": "\ue613"
 },
 {
   "font_class": "Class",
   "unicode": "\ue713"
 },
 {
   "font_class": "Gesture-1",
   "unicode": "\ue813"
 },
 {
   "font_class": "Person-1",
   "unicode": "\ue913"
 },
 {
   "font_class": "Shifts-1",
   "unicode": "\uea13"
 },
 {
   "font_class": "AlertOff",
   "unicode": "\ue614"
 },
 {
   "font_class": "ClipboardPaste-1",
   "unicode": "\ue714"
 },
 {
   "font_class": "Games",
   "unicode": "\ue814"
 },
 {
   "font_class": "Person",
   "unicode": "\ue914"
 },
 {
   "font_class": "Save-1",
   "unicode": "\uea14"
 },
 {
   "font_class": "Airplane",
   "unicode": "\ue615"
 },
 {
   "font_class": "Cellular4G-1",
   "unicode": "\ue715"
 },
 {
   "font_class": "Games-1",
   "unicode": "\ue815"
 },
 {
   "font_class": "FastMode-1",
   "unicode": "\ue915"
 },
 {
   "font_class": "SlideAdd",
   "unicode": "\uea15"
 },
 {
   "font_class": "AlertSnooze",
   "unicode": "\ue616"
 },
 {
   "font_class": "CellularData4",
   "unicode": "\ue716"
 },
 {
   "font_class": "FullScreenZoom",
   "unicode": "\ue816"
 },
 {
   "font_class": "PersonVoice",
   "unicode": "\ue916"
 },
 {
   "font_class": "WiFi5",
   "unicode": "\uea16"
 },
 {
   "font_class": "AppGeneric",
   "unicode": "\ue617"
 },
 {
   "font_class": "Certificate",
   "unicode": "\ue717"
 },
 {
   "font_class": "FPS240",
   "unicode": "\ue817"
 },
 {
   "font_class": "PhoneDesktop",
   "unicode": "\ue917"
 },
 {
   "font_class": "QRCode-1",
   "unicode": "\uea17"
 },
 {
   "font_class": "Alert",
   "unicode": "\ue618"
 },
 {
   "font_class": "ClipboardPaste",
   "unicode": "\ue718"
 },
 {
   "font_class": "Gift-1",
   "unicode": "\ue818"
 },
 {
   "font_class": "PhoneHomeLock-1",
   "unicode": "\ue918"
 },
 {
   "font_class": "Premium-1",
   "unicode": "\uea18"
 },
 {
   "font_class": "Alert-1",
   "unicode": "\ue619"
 },
 {
   "font_class": "Classification-1",
   "unicode": "\ue719"
 },
 {
   "font_class": "GIF-1",
   "unicode": "\ue819"
 },
 {
   "font_class": "PhoneLaptop",
   "unicode": "\ue919"
 },
 {
   "font_class": "ShiftsTeam-1",
   "unicode": "\uea19"
 },
 {
   "font_class": "AlertUrgent",
   "unicode": "\ue61a"
 },
 {
   "font_class": "Certificate-1",
   "unicode": "\ue71a"
 },
 {
   "font_class": "Glance-1",
   "unicode": "\ue81a"
 },
 {
   "font_class": "PhoneHomeLock",
   "unicode": "\ue91a"
 },
 {
   "font_class": "PSTNCall",
   "unicode": "\uea1a"
 },
 {
   "font_class": "AppGeneric-1",
   "unicode": "\ue61b"
 },
 {
   "font_class": "ChannelFollow-1",
   "unicode": "\ue71b"
 },
 {
   "font_class": "Gesture",
   "unicode": "\ue81b"
 },
 {
   "font_class": "PhoneError",
   "unicode": "\ue91b"
 },
 {
   "font_class": "Remove",
   "unicode": "\uea1b"
 },
 {
   "font_class": "AlertUrgent-1",
   "unicode": "\ue61c"
 },
 {
   "font_class": "CallPark-1",
   "unicode": "\ue71c"
 },
 {
   "font_class": "Gift",
   "unicode": "\ue81c"
 },
 {
   "font_class": "PhoneLinkSetup-1",
   "unicode": "\ue91c"
 },
 {
   "font_class": "Print-1",
   "unicode": "\uea1c"
 },
 {
   "font_class": "AppSpan",
   "unicode": "\ue61d"
 },
 {
   "font_class": "Clock-1",
   "unicode": "\ue71d"
 },
 {
   "font_class": "FPS960-1",
   "unicode": "\ue81d"
 },
 {
   "font_class": "FlagOff-1",
   "unicode": "\ue91d"
 },
 {
   "font_class": "ShareCloseTray-1",
   "unicode": "\uea1d"
 },
 {
   "font_class": "AppRecent",
   "unicode": "\ue61e"
 },
 {
   "font_class": "ClosedCaption-1",
   "unicode": "\ue71e"
 },
 {
   "font_class": "GlobeDesktop",
   "unicode": "\ue81e"
 },
 {
   "font_class": "Fingerprint-1",
   "unicode": "\ue91e"
 },
 {
   "font_class": "PhoneStatusBar",
   "unicode": "\uea1e"
 },
 {
   "font_class": "AppTitle",
   "unicode": "\ue61f"
 },
 {
   "font_class": "CallOutbound-1",
   "unicode": "\ue71f"
 },
 {
   "font_class": "GlobeAdd",
   "unicode": "\ue81f"
 },
 {
   "font_class": "PhoneScreenTime",
   "unicode": "\ue91f"
 },
 {
   "font_class": "Sleep-1",
   "unicode": "\uea1f"
 },
 {
   "font_class": "AppFolder-1",
   "unicode": "\ue620"
 },
 {
   "font_class": "CallOutbound",
   "unicode": "\ue720"
 },
 {
   "font_class": "FontIncrease-1",
   "unicode": "\ue820"
 },
 {
   "font_class": "PhonePageHeader",
   "unicode": "\ue920"
 },
 {
   "font_class": "Presenter",
   "unicode": "\uea20"
 },
 {
   "font_class": "Archive-1",
   "unicode": "\ue621"
 },
 {
   "font_class": "Clock",
   "unicode": "\ue721"
 },
 {
   "font_class": "GlobeDesktop-1",
   "unicode": "\ue821"
 },
 {
   "font_class": "PhonePageHeader-1",
   "unicode": "\ue921"
 },
 {
   "font_class": "Print",
   "unicode": "\uea21"
 },
 {
   "font_class": "Archive",
   "unicode": "\ue622"
 },
 {
   "font_class": "ClosedCaption",
   "unicode": "\ue722"
 },
 {
   "font_class": "GlobeAdd-1",
   "unicode": "\ue822"
 },
 {
   "font_class": "PhoneMobile-1",
   "unicode": "\ue922"
 },
 {
   "font_class": "ShiftsTeam",
   "unicode": "\uea22"
 },
 {
   "font_class": "AppUnspan-1",
   "unicode": "\ue623"
 },
 {
   "font_class": "CallPark",
   "unicode": "\ue723"
 },
 {
   "font_class": "GIF",
   "unicode": "\ue823"
 },
 {
   "font_class": "PhoneStatusBar-1",
   "unicode": "\ue923"
 },
 {
   "font_class": "WiFiProtected-1",
   "unicode": "\uea23"
 },
 {
   "font_class": "AppsAddIn",
   "unicode": "\ue624"
 },
 {
   "font_class": "Channel-1",
   "unicode": "\ue724"
 },
 {
   "font_class": "Globe",
   "unicode": "\ue824"
 },
 {
   "font_class": "PhonePagination-1",
   "unicode": "\ue924"
 },
 {
   "font_class": "Power",
   "unicode": "\uea24"
 },
 {
   "font_class": "AppsList",
   "unicode": "\ue625"
 },
 {
   "font_class": "ChatHelp-1",
   "unicode": "\ue725"
 },
 {
   "font_class": "GlobeVideo",
   "unicode": "\ue825"
 },
 {
   "font_class": "PhoneScreenTime-1",
   "unicode": "\ue925"
 },
 {
   "font_class": "PhoneUpdate-1",
   "unicode": "\uea25"
 },
 {
   "font_class": "AppSpan-1",
   "unicode": "\ue626"
 },
 {
   "font_class": "Chat-1",
   "unicode": "\ue726"
 },
 {
   "font_class": "Globe-1",
   "unicode": "\ue826"
 },
 {
   "font_class": "PhoneDesktop-1",
   "unicode": "\ue926"
 },
 {
   "font_class": "RadioButton-1",
   "unicode": "\uea26"
 },
 {
   "font_class": "AppsList-1",
   "unicode": "\ue627"
 },
 {
   "font_class": "CheckboxChecked-1",
   "unicode": "\ue727"
 },
 {
   "font_class": "Grid",
   "unicode": "\ue827"
 },
 {
   "font_class": "PhoneLinkSetup",
   "unicode": "\ue927"
 },
 {
   "font_class": "PhoneVibrate-1",
   "unicode": "\uea27"
 },
 {
   "font_class": "AppUnspan",
   "unicode": "\ue628"
 },
 {
   "font_class": "ChatHelp",
   "unicode": "\ue728"
 },
 {
   "font_class": "GlobeLocation",
   "unicode": "\ue828"
 },
 {
   "font_class": "PhoneMobile",
   "unicode": "\ue928"
 },
 {
   "font_class": "XboxConsole-1",
   "unicode": "\uea28"
 },
 {
   "font_class": "Apps",
   "unicode": "\ue629"
 },
 {
   "font_class": "Chat",
   "unicode": "\ue729"
 },
 {
   "font_class": "GlobeLocation-1",
   "unicode": "\ue829"
 },
 {
   "font_class": "FlashOff-1",
   "unicode": "\ue929"
 },
 {
   "font_class": "WiFi3-1",
   "unicode": "\uea29"
 },
 {
   "font_class": "AppRecent-1",
   "unicode": "\ue62a"
 },
 {
   "font_class": "CheckboxChecked",
   "unicode": "\ue72a"
 },
 {
   "font_class": "GroupList-1",
   "unicode": "\ue82a"
 },
 {
   "font_class": "PhoneVerticalScroll-1",
   "unicode": "\ue92a"
 },
 {
   "font_class": "VideoClip-1",
   "unicode": "\uea2a"
 },
 {
   "font_class": "AppTitle-1",
   "unicode": "\ue62b"
 },
 {
   "font_class": "ChevronDownCircle",
   "unicode": "\ue72b"
 },
 {
   "font_class": "GroupList",
   "unicode": "\ue82b"
 },
 {
   "font_class": "PhoneToPC-1",
   "unicode": "\ue92b"
 },
 {
   "font_class": "QRCode",
   "unicode": "\uea2b"
 },
 {
   "font_class": "ArrowClockwise",
   "unicode": "\ue62c"
 },
 {
   "font_class": "Checkmark",
   "unicode": "\ue72c"
 },
 {
   "font_class": "Guest-1",
   "unicode": "\ue82c"
 },
 {
   "font_class": "PhoneTablet-1",
   "unicode": "\ue92c"
 },
 {
   "font_class": "WiFi1-1",
   "unicode": "\uea2c"
 },
 {
   "font_class": "ArrowDownRightCircle",
   "unicode": "\ue62d"
 },
 {
   "font_class": "ChevronDownCircle-1",
   "unicode": "\ue72d"
 },
 {
   "font_class": "Headphones",
   "unicode": "\ue82d"
 },
 {
   "font_class": "PhoneVerticalScroll",
   "unicode": "\ue92d"
 },
 {
   "font_class": "Whiteboard",
   "unicode": "\uea2d"
 },
 {
   "font_class": "ArrowCounterclockwise-1",
   "unicode": "\ue62e"
 },
 {
   "font_class": "ChevronLeft",
   "unicode": "\ue72e"
 },
 {
   "font_class": "Guest",
   "unicode": "\ue82e"
 },
 {
   "font_class": "Flashlight",
   "unicode": "\ue92e"
 },
 {
   "font_class": "ReOrder-1",
   "unicode": "\uea2e"
 },
 {
   "font_class": "ArrowDownRightCircle-1",
   "unicode": "\ue62f"
 },
 {
   "font_class": "ChevronLeft2",
   "unicode": "\ue72f"
 },
 {
   "font_class": "Headphones-1",
   "unicode": "\ue82f"
 },
 {
   "font_class": "WiFiProtected",
   "unicode": "\ue92f"
 },
 {
   "font_class": "Voicemail-1",
   "unicode": "\uea2f"
 },
 {
   "font_class": "ArrowDownLeft",
   "unicode": "\ue630"
 },
 {
   "font_class": "Checkmark-1",
   "unicode": "\ue730"
 },
 {
   "font_class": "Collections",
   "unicode": "\ue830"
 },
 {
   "font_class": "PhoneToPC",
   "unicode": "\ue930"
 },
 {
   "font_class": "WeatherBlowingSnow",
   "unicode": "\uea30"
 },
 {
   "font_class": "ArrowCounterclockwise",
   "unicode": "\ue631"
 },
 {
   "font_class": "ChevronDown-1",
   "unicode": "\ue731"
 },
 {
   "font_class": "Headset",
   "unicode": "\ue831"
 },
 {
   "font_class": "PhotoRotate-1",
   "unicode": "\ue931"
 },
 {
   "font_class": "WeatherRain",
   "unicode": "\uea31"
 },
 {
   "font_class": "AppsAddIn-1",
   "unicode": "\ue632"
 },
 {
   "font_class": "CloudBackup",
   "unicode": "\ue732"
 },
 {
   "font_class": "HeadsetVR-1",
   "unicode": "\ue832"
 },
 {
   "font_class": "FolderAdd",
   "unicode": "\ue932"
 },
 {
   "font_class": "Tablet",
   "unicode": "\uea32"
 },
 {
   "font_class": "ArrowDownload",
   "unicode": "\ue633"
 },
 {
   "font_class": "ChevronDown",
   "unicode": "\ue733"
 },
 {
   "font_class": "Headset-1",
   "unicode": "\ue833"
 },
 {
   "font_class": "FolderJunk-1",
   "unicode": "\ue933"
 },
 {
   "font_class": "WeatherSunny-1",
   "unicode": "\uea33"
 },
 {
   "font_class": "ArrowDown",
   "unicode": "\ue634"
 },
 {
   "font_class": "CloudBackup-1",
   "unicode": "\ue734"
 },
 {
   "font_class": "HDR",
   "unicode": "\ue834"
 },
 {
   "font_class": "PhotoFilter",
   "unicode": "\ue934"
 },
 {
   "font_class": "VideoSecurity-1",
   "unicode": "\uea34"
 },
 {
   "font_class": "ArrowForward",
   "unicode": "\ue635"
 },
 {
   "font_class": "CloudDownload-1",
   "unicode": "\ue735"
 },
 {
   "font_class": "HelpCircle-1",
   "unicode": "\ue835"
 },
 {
   "font_class": "PhotoRotate",
   "unicode": "\ue935"
 },
 {
   "font_class": "WeatherHailDay",
   "unicode": "\uea35"
 },
 {
   "font_class": "ArrowDownload-1",
   "unicode": "\ue636"
 },
 {
   "font_class": "CloudOff-1",
   "unicode": "\ue736"
 },
 {
   "font_class": "Heart-1",
   "unicode": "\ue836"
 },
 {
   "font_class": "Phone-1",
   "unicode": "\ue936"
 },
 {
   "font_class": "WeatherSnowShowerDay",
   "unicode": "\uea36"
 },
 {
   "font_class": "Apps-1",
   "unicode": "\ue637"
 },
 {
   "font_class": "CommentNext",
   "unicode": "\ue737"
 },
 {
   "font_class": "HelpCircle",
   "unicode": "\ue837"
 },
 {
   "font_class": "PhoneTablet",
   "unicode": "\ue937"
 },
 {
   "font_class": "ResizeImage-1",
   "unicode": "\uea37"
 },
 {
   "font_class": "ArrowEnter",
   "unicode": "\ue638"
 },
 {
   "font_class": "CloudOffline-1",
   "unicode": "\ue738"
 },
 {
   "font_class": "Heart",
   "unicode": "\ue838"
 },
 {
   "font_class": "PhotoFilter-1",
   "unicode": "\ue938"
 },
 {
   "font_class": "GlobeVideo-1",
   "unicode": "\uea38"
 },
 {
   "font_class": "ArrowForward-1",
   "unicode": "\ue639"
 },
 {
   "font_class": "CommentNext-1",
   "unicode": "\ue739"
 },
 {
   "font_class": "HeadsetVR",
   "unicode": "\ue839"
 },
 {
   "font_class": "PinOff-1",
   "unicode": "\ue939"
 },
 {
   "font_class": "PlayCircle-1",
   "unicode": "\uea39"
 },
 {
   "font_class": "ArrowExpand-1",
   "unicode": "\ue63a"
 },
 {
   "font_class": "CommentDelete-1",
   "unicode": "\ue73a"
 },
 {
   "font_class": "DataPie",
   "unicode": "\ue83a"
 },
 {
   "font_class": "Play-1",
   "unicode": "\ue93a"
 },
 {
   "font_class": "Settings-1",
   "unicode": "\uea3a"
 },
 {
   "font_class": "ArrowExport",
   "unicode": "\ue63b"
 },
 {
   "font_class": "CommentDelete",
   "unicode": "\ue73b"
 },
 {
   "font_class": "History-1",
   "unicode": "\ue83b"
 },
 {
   "font_class": "Play",
   "unicode": "\ue93b"
 },
 {
   "font_class": "WeatherCloudy",
   "unicode": "\uea3b"
 },
 {
   "font_class": "ArrowExpand",
   "unicode": "\ue63c"
 },
 {
   "font_class": "CloudOffline",
   "unicode": "\ue73c"
 },
 {
   "font_class": "Highlight",
   "unicode": "\ue83c"
 },
 {
   "font_class": "PhoneVibrate",
   "unicode": "\ue93c"
 },
 {
   "font_class": "TextAlignJustify",
   "unicode": "\uea3c"
 },
 {
   "font_class": "ArrowDown-1",
   "unicode": "\ue63d"
 },
 {
   "font_class": "CloudSyncComplete-1",
   "unicode": "\ue73d"
 },
 {
   "font_class": "HideSlide",
   "unicode": "\ue83d"
 },
 {
   "font_class": "Pin",
   "unicode": "\ue93d"
 },
 {
   "font_class": "WeatherSnowShowerNight",
   "unicode": "\uea3d"
 },
 {
   "font_class": "ArrowClockwise-1",
   "unicode": "\ue63e"
 },
 {
   "font_class": "CloudOff",
   "unicode": "\ue73e"
 },
 {
   "font_class": "HomeAdd-1",
   "unicode": "\ue83e"
 },
 {
   "font_class": "PointScan-1",
   "unicode": "\ue93e"
 },
 {
   "font_class": "WeatherSnow-1",
   "unicode": "\uea3e"
 },
 {
   "font_class": "ArrowLeft-1",
   "unicode": "\ue63f"
 },
 {
   "font_class": "CameraAdd",
   "unicode": "\ue73f"
 },
 {
   "font_class": "DataSunburst",
   "unicode": "\ue83f"
 },
 {
   "font_class": "Pin-1",
   "unicode": "\ue93f"
 },
 {
   "font_class": "TextFirstLine-1",
   "unicode": "\uea3f"
 },
 {
   "font_class": "ArrowImport",
   "unicode": "\ue640"
 },
 {
   "font_class": "CameraAdd-1",
   "unicode": "\ue740"
 },
 {
   "font_class": "Hub",
   "unicode": "\ue840"
 },
 {
   "font_class": "FolderMove-1",
   "unicode": "\ue940"
 },
 {
   "font_class": "Predictions",
   "unicode": "\uea40"
 },
 {
   "font_class": "ArrowMinimize",
   "unicode": "\ue641"
 },
 {
   "font_class": "CloudDownload",
   "unicode": "\ue741"
 },
 {
   "font_class": "History",
   "unicode": "\ue841"
 },
 {
   "font_class": "PinOff",
   "unicode": "\ue941"
 },
 {
   "font_class": "TextEditStyle-1",
   "unicode": "\uea41"
 },
 {
   "font_class": "ArrowLeft",
   "unicode": "\ue642"
 },
 {
   "font_class": "Calendar",
   "unicode": "\ue742"
 },
 {
   "font_class": "SiteBlocked-1",
   "unicode": "\ue842"
 },
 {
   "font_class": "Poll-1",
   "unicode": "\ue942"
 },
 {
   "font_class": "TextBold-1",
   "unicode": "\uea42"
 },
 {
   "font_class": "ArrowMaximize",
   "unicode": "\ue643"
 },
 {
   "font_class": "Clipboard-1",
   "unicode": "\ue743"
 },
 {
   "font_class": "Home-1",
   "unicode": "\ue843"
 },
 {
   "font_class": "PointScan",
   "unicode": "\ue943"
 },
 {
   "font_class": "TextEditStyle",
   "unicode": "\uea43"
 },
 {
   "font_class": "ArrowExport-1",
   "unicode": "\ue644"
 },
 {
   "font_class": "ChannelFollow",
   "unicode": "\ue744"
 },
 {
   "font_class": "ImageAdd",
   "unicode": "\ue844"
 },
 {
   "font_class": "ReadOnly-1",
   "unicode": "\ue944"
 },
 {
   "font_class": "TextAlignDistributed",
   "unicode": "\uea44"
 },
 {
   "font_class": "ArrowEnter-1",
   "unicode": "\ue645"
 },
 {
   "font_class": "CameraSwitch",
   "unicode": "\ue745"
 },
 {
   "font_class": "ImageAdd-1",
   "unicode": "\ue845"
 },
 {
   "font_class": "Power-1",
   "unicode": "\ue945"
 },
 {
   "font_class": "TextAdd",
   "unicode": "\uea45"
 },
 {
   "font_class": "ArrowMinimize-1",
   "unicode": "\ue646"
 },
 {
   "font_class": "ChannelNotifications-1",
   "unicode": "\ue746"
 },
 {
   "font_class": "ImageLibrary",
   "unicode": "\ue846"
 },
 {
   "font_class": "ReadOnly",
   "unicode": "\ue946"
 },
 {
   "font_class": "TextDescription",
   "unicode": "\uea46"
 },
 {
   "font_class": "ArrowNext",
   "unicode": "\ue647"
 },
 {
   "font_class": "Camera-1",
   "unicode": "\ue747"
 },
 {
   "font_class": "Highlight-1",
   "unicode": "\ue847"
 },
 {
   "font_class": "Poll",
   "unicode": "\ue947"
 },
 {
   "font_class": "TableEdit",
   "unicode": "\uea47"
 },
 {
   "font_class": "ArrowDownLeft-1",
   "unicode": "\ue648"
 },
 {
   "font_class": "Clipboard",
   "unicode": "\ue748"
 },
 {
   "font_class": "ImmersiveReader-1",
   "unicode": "\ue848"
 },
 {
   "font_class": "ReadAloud",
   "unicode": "\ue948"
 },
 {
   "font_class": "WeatherRain-1",
   "unicode": "\uea48"
 },
 {
   "font_class": "ArrowPrevious",
   "unicode": "\ue649"
 },
 {
   "font_class": "CommentResolve-1",
   "unicode": "\ue749"
 },
 {
   "font_class": "Image-1",
   "unicode": "\ue849"
 },
 {
   "font_class": "ReadingListAdd-1",
   "unicode": "\ue949"
 },
 {
   "font_class": "TextFontSize-1",
   "unicode": "\uea49"
 },
 {
   "font_class": "ArrowRedo",
   "unicode": "\ue64a"
 },
 {
   "font_class": "CommentResolve",
   "unicode": "\ue74a"
 },
 {
   "font_class": "ImmersiveReader",
   "unicode": "\ue84a"
 },
 {
   "font_class": "ReadingList-1",
   "unicode": "\ue94a"
 },
 {
   "font_class": "Temperature",
   "unicode": "\uea4a"
 },
 {
   "font_class": "ArrowRepeatAllOff",
   "unicode": "\ue64b"
 },
 {
   "font_class": "CommentPrevious-1",
   "unicode": "\ue74b"
 },
 {
   "font_class": "Important-1",
   "unicode": "\ue84b"
 },
 {
   "font_class": "ReadingList",
   "unicode": "\ue94b"
 },
 {
   "font_class": "WeatherRainShowersNight-1",
   "unicode": "\uea4b"
 },
 {
   "font_class": "ArrowPrevious-1",
   "unicode": "\ue64c"
 },
 {
   "font_class": "CommentPrevious",
   "unicode": "\ue74c"
 },
 {
   "font_class": "Incognito-1",
   "unicode": "\ue84c"
 },
 {
   "font_class": "ReadingListAdd",
   "unicode": "\ue94c"
 },
 {
   "font_class": "WiFi1",
   "unicode": "\uea4c"
 },
 {
   "font_class": "AlertOn",
   "unicode": "\ue64d"
 },
 {
   "font_class": "Comment-1",
   "unicode": "\ue74d"
 },
 {
   "font_class": "DataWaterfall-1",
   "unicode": "\ue84d"
 },
 {
   "font_class": "ReadingModeMobile-1",
   "unicode": "\ue94d"
 },
 {
   "font_class": "WeatherCloudy-1",
   "unicode": "\uea4d"
 },
 {
   "font_class": "ArrowImport-1",
   "unicode": "\ue64e"
 },
 {
   "font_class": "Comment",
   "unicode": "\ue74e"
 },
 {
   "font_class": "Incognito",
   "unicode": "\ue84e"
 },
 {
   "font_class": "FPS240-1",
   "unicode": "\ue94e"
 },
 {
   "font_class": "MailInbox-1",
   "unicode": "\uea4e"
 },
 {
   "font_class": "ArrowRepeatAllOff-1",
   "unicode": "\ue64f"
 },
 {
   "font_class": "Compose-1",
   "unicode": "\ue74f"
 },
 {
   "font_class": "IncomingVideoOff",
   "unicode": "\ue84f"
 },
 {
   "font_class": "Remove-1",
   "unicode": "\ue94f"
 },
 {
   "font_class": "TabTrackingPrevention-1",
   "unicode": "\uea4f"
 },
 {
   "font_class": "ArrowMaximize-1",
   "unicode": "\ue650"
 },
 {
   "font_class": "ContactCard",
   "unicode": "\ue750"
 },
 {
   "font_class": "IncomingVideo",
   "unicode": "\ue850"
 },
 {
   "font_class": "ReadingMode-1",
   "unicode": "\ue950"
 },
 {
   "font_class": "PersonBlock-1",
   "unicode": "\uea50"
 },
 {
   "font_class": "ArrowReply-1",
   "unicode": "\ue651"
 },
 {
   "font_class": "Compose",
   "unicode": "\ue751"
 },
 {
   "font_class": "Home",
   "unicode": "\ue851"
 },
 {
   "font_class": "FPS960",
   "unicode": "\ue951"
 },
 {
   "font_class": "Location-1",
   "unicode": "\uea51"
 },
 {
   "font_class": "ArrowRight-1",
   "unicode": "\ue652"
 },
 {
   "font_class": "ContactCard-1",
   "unicode": "\ue752"
 },
 {
   "font_class": "Info-1",
   "unicode": "\ue852"
 },
 {
   "font_class": "ReadingMode",
   "unicode": "\ue952"
 },
 {
   "font_class": "WeatherPartlyCloudyNight",
   "unicode": "\uea52"
 },
 {
   "font_class": "AppFolder",
   "unicode": "\ue653"
 },
 {
   "font_class": "CheckboxUnchecked",
   "unicode": "\ue753"
 },
 {
   "font_class": "Info",
   "unicode": "\ue853"
 },
 {
   "font_class": "Recommended",
   "unicode": "\ue953"
 },
 {
   "font_class": "WeatherRainShowersNight",
   "unicode": "\uea53"
 },
 {
   "font_class": "ArrowRight",
   "unicode": "\ue654"
 },
 {
   "font_class": "CheckboxUnchecked-1",
   "unicode": "\ue754"
 },
 {
   "font_class": "Important",
   "unicode": "\ue854"
 },
 {
   "font_class": "Recommended-1",
   "unicode": "\ue954"
 },
 {
   "font_class": "Table",
   "unicode": "\uea54"
 },
 {
   "font_class": "ArrowRightCircle",
   "unicode": "\ue655"
 },
 {
   "font_class": "Contacts",
   "unicode": "\ue755"
 },
 {
   "font_class": "IncomingVideo-1",
   "unicode": "\ue855"
 },
 {
   "font_class": "RemoveRecent",
   "unicode": "\ue955"
 },
 {
   "font_class": "TextFontSize",
   "unicode": "\uea55"
 },
 {
   "font_class": "ArrowNext-1",
   "unicode": "\ue656"
 },
 {
   "font_class": "CheckmarkCircle",
   "unicode": "\ue756"
 },
 {
   "font_class": "InPrivateAccount-1",
   "unicode": "\ue856"
 },
 {
   "font_class": "RemoveRecent-1",
   "unicode": "\ue956"
 },
 {
   "font_class": "Voicemail",
   "unicode": "\uea56"
 },
 {
   "font_class": "ArrowSort",
   "unicode": "\ue657"
 },
 {
   "font_class": "Contacts-1",
   "unicode": "\ue757"
 },
 {
   "font_class": "InkingTool",
   "unicode": "\ue857"
 },
 {
   "font_class": "Rename",
   "unicode": "\ue957"
 },
 {
   "font_class": "TestCall-1",
   "unicode": "\uea57"
 },
 {
   "font_class": "ArrowReplyAll-1",
   "unicode": "\ue658"
 },
 {
   "font_class": "Document",
   "unicode": "\ue758"
 },
 {
   "font_class": "Key",
   "unicode": "\ue858"
 },
 {
   "font_class": "Reward",
   "unicode": "\ue958"
 },
 {
   "font_class": "WeatherPartlyCloudyNight-1",
   "unicode": "\uea58"
 },
 {
   "font_class": "ArrowRightCircle-1",
   "unicode": "\ue659"
 },
 {
   "font_class": "CheckmarkSquare-1",
   "unicode": "\ue759"
 },
 {
   "font_class": "InkingTool-1",
   "unicode": "\ue859"
 },
 {
   "font_class": "Rename-1",
   "unicode": "\ue959"
 },
 {
   "font_class": "TextAlignRight",
   "unicode": "\uea59"
 },
 {
   "font_class": "ArrowReply",
   "unicode": "\ue65a"
 },
 {
   "font_class": "CheckmarkSquare",
   "unicode": "\ue75a"
 },
 {
   "font_class": "ImageLibrary-1",
   "unicode": "\ue85a"
 },
 {
   "font_class": "Reward-1",
   "unicode": "\ue95a"
 },
 {
   "font_class": "Pause-1",
   "unicode": "\uea5a"
 },
 {
   "font_class": "ArrowSwap-1",
   "unicode": "\ue65b"
 },
 {
   "font_class": "ConvertToTable-1",
   "unicode": "\ue75b"
 },
 {
   "font_class": "Key-1",
   "unicode": "\ue85b"
 },
 {
   "font_class": "Rewind-1",
   "unicode": "\ue95b"
 },
 {
   "font_class": "Tag-1",
   "unicode": "\uea5b"
 },
 {
   "font_class": "ArrowReplyAll",
   "unicode": "\ue65c"
 },
 {
   "font_class": "ConvertToTable",
   "unicode": "\ue75c"
 },
 {
   "font_class": "KeyboardDock-1",
   "unicode": "\ue85c"
 },
 {
   "font_class": "Router",
   "unicode": "\ue95c"
 },
 {
   "font_class": "TextFirstLine",
   "unicode": "\uea5c"
 },
 {
   "font_class": "ArrowSort-1",
   "unicode": "\ue65d"
 },
 {
   "font_class": "Cookies-1",
   "unicode": "\ue75d"
 },
 {
   "font_class": "InPrivateAccount",
   "unicode": "\ue85d"
 },
 {
   "font_class": "ResizeVideo",
   "unicode": "\ue95d"
 },
 {
   "font_class": "Teddy",
   "unicode": "\uea5d"
 },
 {
   "font_class": "ArrowTrending-1",
   "unicode": "\ue65e"
 },
 {
   "font_class": "ConvertToText-1",
   "unicode": "\ue75e"
 },
 {
   "font_class": "iOSArrowLeft-1",
   "unicode": "\ue85e"
 },
 {
   "font_class": "Router-1",
   "unicode": "\ue95e"
 },
 {
   "font_class": "MailAllAccounts-1",
   "unicode": "\uea5e"
 },
 {
   "font_class": "ArrowSync",
   "unicode": "\ue65f"
 },
 {
   "font_class": "Cookies",
   "unicode": "\ue75f"
 },
 {
   "font_class": "KeyboardLayoutOneHandedLeft-1",
   "unicode": "\ue85f"
 },
 {
   "font_class": "ResizeImage",
   "unicode": "\ue95f"
 },
 {
   "font_class": "VideoClip",
   "unicode": "\uea5f"
 },
 {
   "font_class": "ArrowSync-1",
   "unicode": "\ue660"
 },
 {
   "font_class": "CopyImage-1",
   "unicode": "\ue760"
 },
 {
   "font_class": "KeyboardLayoutFloat",
   "unicode": "\ue860"
 },
 {
   "font_class": "ResizeVideo-1",
   "unicode": "\ue960"
 },
 {
   "font_class": "MagicWand",
   "unicode": "\uea60"
 },
 {
   "font_class": "ArrowUndo",
   "unicode": "\ue661"
 },
 {
   "font_class": "CopyImage",
   "unicode": "\ue761"
 },
 {
   "font_class": "KeyboardLayoutFloat-1",
   "unicode": "\ue861"
 },
 {
   "font_class": "Safe",
   "unicode": "\ue961"
 },
 {
   "font_class": "MatchAppLayout",
   "unicode": "\uea61"
 },
 {
   "font_class": "ArrowSwap",
   "unicode": "\ue662"
 },
 {
   "font_class": "CopyLink-1",
   "unicode": "\ue762"
 },
 {
   "font_class": "KeyboardLayoutResize-1",
   "unicode": "\ue862"
 },
 {
   "font_class": "SaveCopy-1",
   "unicode": "\ue962"
 },
 {
   "font_class": "WeatherThunderstorm-1",
   "unicode": "\uea62"
 },
 {
   "font_class": "ArrowTrending",
   "unicode": "\ue663"
 },
 {
   "font_class": "Copy-1",
   "unicode": "\ue763"
 },
 {
   "font_class": "KeyboardLayoutSplit",
   "unicode": "\ue863"
 },
 {
   "font_class": "SaveCopy",
   "unicode": "\ue963"
 },
 {
   "font_class": "Tablet-1",
   "unicode": "\uea63"
 },
 {
   "font_class": "ArrowSyncCircle",
   "unicode": "\ue664"
 },
 {
   "font_class": "CopyLink",
   "unicode": "\ue764"
 },
 {
   "font_class": "KeyboardLayoutSplit-1",
   "unicode": "\ue864"
 },
 {
   "font_class": "Safe-1",
   "unicode": "\ue964"
 },
 {
   "font_class": "TableSettings-1",
   "unicode": "\uea64"
 },
 {
   "font_class": "ArrowUndo-1",
   "unicode": "\ue665"
 },
 {
   "font_class": "Copy",
   "unicode": "\ue765"
 },
 {
   "font_class": "KeyboardShiftUppercase-1",
   "unicode": "\ue865"
 },
 {
   "font_class": "Save",
   "unicode": "\ue965"
 },
 {
   "font_class": "TextAlignCenter",
   "unicode": "\uea65"
 },
 {
   "font_class": "ArrowRedo-1",
   "unicode": "\ue666"
 },
 {
   "font_class": "CopyMove",
   "unicode": "\ue766"
 },
 {
   "font_class": "KeyboardLayoutResize",
   "unicode": "\ue866"
 },
 {
   "font_class": "SaveAs",
   "unicode": "\ue966"
 },
 {
   "font_class": "Teddy-1",
   "unicode": "\uea66"
 },
 {
   "font_class": "ArrowUpLeft",
   "unicode": "\ue667"
 },
 {
   "font_class": "CropInterimOff-1",
   "unicode": "\ue767"
 },
 {
   "font_class": "DeviceEQ",
   "unicode": "\ue867"
 },
 {
   "font_class": "Glance",
   "unicode": "\ue967"
 },
 {
   "font_class": "TestCall",
   "unicode": "\uea67"
 },
 {
   "font_class": "ArrowUp",
   "unicode": "\ue668"
 },
 {
   "font_class": "CropInterim-1",
   "unicode": "\ue768"
 },
 {
   "font_class": "KeyboardTab-1",
   "unicode": "\ue868"
 },
 {
   "font_class": "SaveAs-1",
   "unicode": "\ue968"
 },
 {
   "font_class": "TableSettings",
   "unicode": "\uea68"
 },
 {
   "font_class": "ArrowSyncCircle-1",
   "unicode": "\ue669"
 },
 {
   "font_class": "CropInterimOff",
   "unicode": "\ue769"
 },
 {
   "font_class": "DeviceEQ-1",
   "unicode": "\ue869"
 },
 {
   "font_class": "Scan-1",
   "unicode": "\ue969"
 },
 {
   "font_class": "TapDouble",
   "unicode": "\uea69"
 },
 {
   "font_class": "ArrowUp-1",
   "unicode": "\ue66a"
 },
 {
   "font_class": "Crop-1",
   "unicode": "\ue76a"
 },
 {
   "font_class": "KeyboardShift",
   "unicode": "\ue86a"
 },
 {
   "font_class": "Screenshot",
   "unicode": "\ue96a"
 },
 {
   "font_class": "TeamDelete-1",
   "unicode": "\uea6a"
 },
 {
   "font_class": "ArrowUpRight",
   "unicode": "\ue66b"
 },
 {
   "font_class": "Crop",
   "unicode": "\ue76b"
 },
 {
   "font_class": "KeyboardShiftUppercase",
   "unicode": "\ue86b"
 },
 {
   "font_class": "Grid-1",
   "unicode": "\ue96b"
 },
 {
   "font_class": "Warning-1",
   "unicode": "\uea6b"
 },
 {
   "font_class": "ArrowUpload",
   "unicode": "\ue66c"
 },
 {
   "font_class": "Currency-1",
   "unicode": "\ue76c"
 },
 {
   "font_class": "DialpadOff-1",
   "unicode": "\ue86c"
 },
 {
   "font_class": "SearchSquare-1",
   "unicode": "\ue96c"
 },
 {
   "font_class": "TeamDelete",
   "unicode": "\uea6c"
 },
 {
   "font_class": "ArrowUpRight-1",
   "unicode": "\ue66d"
 },
 {
   "font_class": "DataArea",
   "unicode": "\ue76d"
 },
 {
   "font_class": "KeyboardShift-1",
   "unicode": "\ue86d"
 },
 {
   "font_class": "Screenshot-1",
   "unicode": "\ue96d"
 },
 {
   "font_class": "Tag",
   "unicode": "\uea6d"
 },
 {
   "font_class": "ArrowsBidirectional",
   "unicode": "\ue66e"
 },
 {
   "font_class": "DarkTheme-1",
   "unicode": "\ue76e"
 },
 {
   "font_class": "Laptop-1",
   "unicode": "\ue86e"
 },
 {
   "font_class": "Scratchpad",
   "unicode": "\ue96e"
 },
 {
   "font_class": "Table-1",
   "unicode": "\uea6e"
 },
 {
   "font_class": "ArrowUpload-1",
   "unicode": "\ue66f"
 },
 {
   "font_class": "DarkTheme",
   "unicode": "\ue76f"
 },
 {
   "font_class": "Keyboard-1",
   "unicode": "\ue86f"
 },
 {
   "font_class": "Scratchpad-1",
   "unicode": "\ue96f"
 },
 {
   "font_class": "WeatherPartlyCloudyDay-1",
   "unicode": "\uea6f"
 },
 {
   "font_class": "ArrowsBidirectional-1",
   "unicode": "\ue670"
 },
 {
   "font_class": "CropInterim",
   "unicode": "\ue770"
 },
 {
   "font_class": "SwipeRight",
   "unicode": "\ue870"
 },
 {
   "font_class": "Scan",
   "unicode": "\ue970"
 },
 {
   "font_class": "TextAlignRight-1",
   "unicode": "\uea70"
 },
 {
   "font_class": "Assignments-1",
   "unicode": "\ue671"
 },
 {
   "font_class": "DataBarHorizontal",
   "unicode": "\ue771"
 },
 {
   "font_class": "Keyboard",
   "unicode": "\ue871"
 },
 {
   "font_class": "SelectAllOff-1",
   "unicode": "\ue971"
 },
 {
   "font_class": "PeopleTeam-1",
   "unicode": "\uea71"
 },
 {
   "font_class": "AttachWithText",
   "unicode": "\ue672"
 },
 {
   "font_class": "DataBarVertical-1",
   "unicode": "\ue772"
 },
 {
   "font_class": "Laptop",
   "unicode": "\ue872"
 },
 {
   "font_class": "SearchSquare",
   "unicode": "\ue972"
 },
 {
   "font_class": "TextBulletListSquare-1",
   "unicode": "\uea72"
 },
 {
   "font_class": "ArrowUpLeft-1",
   "unicode": "\ue673"
 },
 {
   "font_class": "DataBarHorizontal-1",
   "unicode": "\ue773"
 },
 {
   "font_class": "Directions",
   "unicode": "\ue873"
 },
 {
   "font_class": "Search",
   "unicode": "\ue973"
 },
 {
   "font_class": "TextBold",
   "unicode": "\uea73"
 },
 {
   "font_class": "Attach",
   "unicode": "\ue674"
 },
 {
   "font_class": "Currency",
   "unicode": "\ue774"
 },
 {
   "font_class": "Layer-1",
   "unicode": "\ue874"
 },
 {
   "font_class": "Search-1",
   "unicode": "\ue974"
 },
 {
   "font_class": "PersonVoice-1",
   "unicode": "\uea74"
 },
 {
   "font_class": "Autocorrect",
   "unicode": "\ue675"
 },
 {
   "font_class": "DataBarVertical",
   "unicode": "\ue775"
 },
 {
   "font_class": "Library-1",
   "unicode": "\ue875"
 },
 {
   "font_class": "SendLogging-1",
   "unicode": "\ue975"
 },
 {
   "font_class": "VideoOff",
   "unicode": "\uea75"
 },
 {
   "font_class": "Autocorrect-1",
   "unicode": "\ue676"
 },
 {
   "font_class": "DataFunnel-1",
   "unicode": "\ue776"
 },
 {
   "font_class": "LauncherSettings-1",
   "unicode": "\ue876"
 },
 {
   "font_class": "SelectAll-1",
   "unicode": "\ue976"
 },
 {
   "font_class": "Tabs-1",
   "unicode": "\uea76"
 },
 {
   "font_class": "Attach-1",
   "unicode": "\ue677"
 },
 {
   "font_class": "DataFunnel",
   "unicode": "\ue777"
 },
 {
   "font_class": "Lasso",
   "unicode": "\ue877"
 },
 {
   "font_class": "SelectAll",
   "unicode": "\ue977"
 },
 {
   "font_class": "Target",
   "unicode": "\uea77"
 },
 {
   "font_class": "AttachWithText-1",
   "unicode": "\ue678"
 },
 {
   "font_class": "DataHistogram",
   "unicode": "\ue778"
 },
 {
   "font_class": "Layer",
   "unicode": "\ue878"
 },
 {
   "font_class": "SendCopy-1",
   "unicode": "\ue978"
 },
 {
   "font_class": "TextAlignDistributed-1",
   "unicode": "\uea78"
 },
 {
   "font_class": "BackgroundEffect-1",
   "unicode": "\ue679"
 },
 {
   "font_class": "DataHistogram-1",
   "unicode": "\ue779"
 },
 {
   "font_class": "List-1",
   "unicode": "\ue879"
 },
 {
   "font_class": "SendCopy",
   "unicode": "\ue979"
 },
 {
   "font_class": "SwitchVideo-1",
   "unicode": "\uea79"
 },
 {
   "font_class": "BackgroundEffect",
   "unicode": "\ue67a"
 },
 {
   "font_class": "Cloud-1",
   "unicode": "\ue77a"
 },
 {
   "font_class": "DividerShort",
   "unicode": "\ue87a"
 },
 {
   "font_class": "SettingsDev-1",
   "unicode": "\ue97a"
 },
 {
   "font_class": "TextItalic-1",
   "unicode": "\uea7a"
 },
 {
   "font_class": "Battery1",
   "unicode": "\ue67b"
 },
 {
   "font_class": "Code",
   "unicode": "\ue77b"
 },
 {
   "font_class": "Lightbulb-1",
   "unicode": "\ue87b"
 },
 {
   "font_class": "Send",
   "unicode": "\ue97b"
 },
 {
   "font_class": "WeatherSnow",
   "unicode": "\uea7b"
 },
 {
   "font_class": "Backspace-1",
   "unicode": "\ue67c"
 },
 {
   "font_class": "Cloud",
   "unicode": "\ue77c"
 },
 {
   "font_class": "LightbulbCircle-1",
   "unicode": "\ue87c"
 },
 {
   "font_class": "SettingsDev",
   "unicode": "\ue97c"
 },
 {
   "font_class": "TextIndentIncrease",
   "unicode": "\uea7c"
 },
 {
   "font_class": "Battery0",
   "unicode": "\ue67d"
 },
 {
   "font_class": "DataLine-1",
   "unicode": "\ue77d"
 },
 {
   "font_class": "Lasso-1",
   "unicode": "\ue87d"
 },
 {
   "font_class": "SendLogging",
   "unicode": "\ue97d"
 },
 {
   "font_class": "TextStrikethrough",
   "unicode": "\uea7d"
 },
 {
   "font_class": "Battery2-1",
   "unicode": "\ue67e"
 },
 {
   "font_class": "Code-1",
   "unicode": "\ue77e"
 },
 {
   "font_class": "List",
   "unicode": "\ue87e"
 },
 {
   "font_class": "SettingMic-1",
   "unicode": "\ue97e"
 },
 {
   "font_class": "WeatherSqualls-1",
   "unicode": "\uea7e"
 },
 {
   "font_class": "Battery2",
   "unicode": "\ue67f"
 },
 {
   "font_class": "CloudSyncComplete",
   "unicode": "\ue77f"
 },
 {
   "font_class": "Library",
   "unicode": "\ue87f"
 },
 {
   "font_class": "SettingMic",
   "unicode": "\ue97f"
 },
 {
   "font_class": "TextLineSpacing",
   "unicode": "\uea7f"
 },
 {
   "font_class": "Assignments",
   "unicode": "\ue680"
 },
 {
   "font_class": "ClipboardText-1",
   "unicode": "\ue780"
 },
 {
   "font_class": "Lightbulb",
   "unicode": "\ue880"
 },
 {
   "font_class": "Send-1",
   "unicode": "\ue980"
 },
 {
   "font_class": "TextNumberListLTR",
   "unicode": "\uea80"
 },
 {
   "font_class": "Battery6-1",
   "unicode": "\ue681"
 },
 {
   "font_class": "ClearFormatting-1",
   "unicode": "\ue781"
 },
 {
   "font_class": "Link",
   "unicode": "\ue881"
 },
 {
   "font_class": "ShareAndroid-1",
   "unicode": "\ue981"
 },
 {
   "font_class": "Text",
   "unicode": "\uea81"
 },
 {
   "font_class": "Battery4-1",
   "unicode": "\ue682"
 },
 {
   "font_class": "ClearFormatting",
   "unicode": "\ue782"
 },
 {
   "font_class": "LightbulbCircle",
   "unicode": "\ue882"
 },
 {
   "font_class": "Settings",
   "unicode": "\ue982"
 },
 {
   "font_class": "TextAlignLeft",
   "unicode": "\uea82"
 },
 {
   "font_class": "Badge-1",
   "unicode": "\ue683"
 },
 {
   "font_class": "Classification",
   "unicode": "\ue783"
 },
 {
   "font_class": "LocalLanguage-1",
   "unicode": "\ue883"
 },
 {
   "font_class": "ShareDesktop",
   "unicode": "\ue983"
 },
 {
   "font_class": "TimeAndWeather-1",
   "unicode": "\uea83"
 },
 {
   "font_class": "Badge",
   "unicode": "\ue684"
 },
 {
   "font_class": "Color-1",
   "unicode": "\ue784"
 },
 {
   "font_class": "Live-1",
   "unicode": "\ue884"
 },
 {
   "font_class": "ShareCloseTray",
   "unicode": "\ue984"
 },
 {
   "font_class": "TableDelete-1",
   "unicode": "\uea84"
 },
 {
   "font_class": "Battery6",
   "unicode": "\ue685"
 },
 {
   "font_class": "HDR-1",
   "unicode": "\ue785"
 },
 {
   "font_class": "Location",
   "unicode": "\ue885"
 },
 {
   "font_class": "Shapes-1",
   "unicode": "\ue985"
 },
 {
   "font_class": "Unsave-1",
   "unicode": "\uea85"
 },
 {
   "font_class": "Battery5",
   "unicode": "\ue686"
 },
 {
   "font_class": "Collections-1",
   "unicode": "\ue786"
 },
 {
   "font_class": "LocationNotFound-1",
   "unicode": "\ue886"
 },
 {
   "font_class": "ShareAndroid",
   "unicode": "\ue986"
 },
 {
   "font_class": "TimePicker",
   "unicode": "\uea86"
 },
 {
   "font_class": "Battery3-1",
   "unicode": "\ue687"
 },
 {
   "font_class": "CommentAdd",
   "unicode": "\ue787"
 },
 {
   "font_class": "DockRow",
   "unicode": "\ue887"
 },
 {
   "font_class": "ShareiOS-1",
   "unicode": "\ue987"
 },
 {
   "font_class": "TimerOff",
   "unicode": "\uea87"
 },
 {
   "font_class": "Battery0-1",
   "unicode": "\ue688"
 },
 {
   "font_class": "DataPie-1",
   "unicode": "\ue788"
 },
 {
   "font_class": "LocalLanguage",
   "unicode": "\ue888"
 },
 {
   "font_class": "ShareDesktop-1",
   "unicode": "\ue988"
 },
 {
   "font_class": "Translate",
   "unicode": "\uea88"
 },
 {
   "font_class": "Battery1-1",
   "unicode": "\ue689"
 },
 {
   "font_class": "CommentAdd-1",
   "unicode": "\ue789"
 },
 {
   "font_class": "Lock-1",
   "unicode": "\ue889"
 },
 {
   "font_class": "ShareStop",
   "unicode": "\ue989"
 },
 {
   "font_class": "TableAdd",
   "unicode": "\uea89"
 },
 {
   "font_class": "Battery5-1",
   "unicode": "\ue68a"
 },
 {
   "font_class": "DataLine",
   "unicode": "\ue78a"
 },
 {
   "font_class": "MailAdd-1",
   "unicode": "\ue88a"
 },
 {
   "font_class": "ShareStop-1",
   "unicode": "\ue98a"
 },
 {
   "font_class": "WeatherHailNight",
   "unicode": "\uea8a"
 },
 {
   "font_class": "Battery7",
   "unicode": "\ue68b"
 },
 {
   "font_class": "Color",
   "unicode": "\ue78b"
 },
 {
   "font_class": "TeamAdd-1",
   "unicode": "\ue88b"
 },
 {
   "font_class": "ShareiOS",
   "unicode": "\ue98b"
 },
 {
   "font_class": "ThumbLike",
   "unicode": "\uea8b"
 },
 {
   "font_class": "Battery3",
   "unicode": "\ue68c"
 },
 {
   "font_class": "DataScatter",
   "unicode": "\ue78c"
 },
 {
   "font_class": "LockShield-1",
   "unicode": "\ue88c"
 },
 {
   "font_class": "Share-1",
   "unicode": "\ue98c"
 },
 {
   "font_class": "UninstallApp-1",
   "unicode": "\uea8c"
 },
 {
   "font_class": "Battery4",
   "unicode": "\ue68d"
 },
 {
   "font_class": "DataTreemap-1",
   "unicode": "\ue78d"
 },
 {
   "font_class": "MagicWand-1",
   "unicode": "\ue88d"
 },
 {
   "font_class": "ShareScreen",
   "unicode": "\ue98d"
 },
 {
   "font_class": "USBPort-1",
   "unicode": "\uea8d"
 },
 {
   "font_class": "Battery9",
   "unicode": "\ue68e"
 },
 {
   "font_class": "DataTreemap",
   "unicode": "\ue78e"
 },
 {
   "font_class": "LocationNotFound",
   "unicode": "\ue88e"
 },
 {
   "font_class": "Share",
   "unicode": "\ue98e"
 },
 {
   "font_class": "Timer10-1",
   "unicode": "\uea8e"
 },
 {
   "font_class": "BatteryFull-1",
   "unicode": "\ue68f"
 },
 {
   "font_class": "DataScatter-1",
   "unicode": "\ue78f"
 },
 {
   "font_class": "MailAllAccounts",
   "unicode": "\ue88f"
 },
 {
   "font_class": "HideSlide-1",
   "unicode": "\ue98f"
 },
 {
   "font_class": "TableDelete",
   "unicode": "\uea8f"
 },
 {
   "font_class": "Battery7-1",
   "unicode": "\ue690"
 },
 {
   "font_class": "DataSunburst-1",
   "unicode": "\ue790"
 },
 {
   "font_class": "MailAll-1",
   "unicode": "\ue890"
 },
 {
   "font_class": "Shield",
   "unicode": "\ue990"
 },
 {
   "font_class": "TopSpeed",
   "unicode": "\uea90"
 },
 {
   "font_class": "Battery8",
   "unicode": "\ue691"
 },
 {
   "font_class": "DataWaterfall",
   "unicode": "\ue791"
 },
 {
   "font_class": "MailAll",
   "unicode": "\ue891"
 },
 {
   "font_class": "HomeAdd",
   "unicode": "\ue991"
 },
 {
   "font_class": "None",
   "unicode": "\uea91"
 },
 {
   "font_class": "Backspace",
   "unicode": "\ue692"
 },
 {
   "font_class": "DataWhisker-1",
   "unicode": "\ue792"
 },
 {
   "font_class": "MailRead-1",
   "unicode": "\ue892"
 },
 {
   "font_class": "ShiftsPending-1",
   "unicode": "\ue992"
 },
 {
   "font_class": "Timeline",
   "unicode": "\uea92"
 },
 {
   "font_class": "Battery9-1",
   "unicode": "\ue693"
 },
 {
   "font_class": "DataWhisker",
   "unicode": "\ue793"
 },
 {
   "font_class": "Lock",
   "unicode": "\ue893"
 },
 {
   "font_class": "Shifts",
   "unicode": "\ue993"
 },
 {
   "font_class": "TimerOff-1",
   "unicode": "\uea93"
 },
 {
   "font_class": "BatteryCharge-1",
   "unicode": "\ue694"
 },
 {
   "font_class": "DataUsage-1",
   "unicode": "\ue794"
 },
 {
   "font_class": "MailOutbox",
   "unicode": "\ue894"
 },
 {
   "font_class": "SignOut-1",
   "unicode": "\ue994"
 },
 {
   "font_class": "VideoPlayPause",
   "unicode": "\uea94"
 },
 {
   "font_class": "BatteryFull",
   "unicode": "\ue695"
 },
 {
   "font_class": "DataUsage",
   "unicode": "\ue795"
 },
 {
   "font_class": "MailMoveToFocussed-1",
   "unicode": "\ue895"
 },
 {
   "font_class": "SignOut",
   "unicode": "\ue995"
 },
 {
   "font_class": "LockShield",
   "unicode": "\uea95"
 },
 {
   "font_class": "BatterySaver-1",
   "unicode": "\ue696"
 },
 {
   "font_class": "DeleteForever-1",
   "unicode": "\ue796"
 },
 {
   "font_class": "MailAdd",
   "unicode": "\ue896"
 },
 {
   "font_class": "ShiftsPending",
   "unicode": "\ue996"
 },
 {
   "font_class": "ViewDesktop",
   "unicode": "\uea96"
 },
 {
   "font_class": "Battery8-1",
   "unicode": "\ue697"
 },
 {
   "font_class": "DeleteForever",
   "unicode": "\ue797"
 },
 {
   "font_class": "MailOutbox-1",
   "unicode": "\ue897"
 },
 {
   "font_class": "Signature-1",
   "unicode": "\ue997"
 },
 {
   "font_class": "Timer2",
   "unicode": "\uea97"
 },
 {
   "font_class": "BatteryCharge",
   "unicode": "\ue698"
 },
 {
   "font_class": "CameraSwitch-1",
   "unicode": "\ue798"
 },
 {
   "font_class": "MailMoveToFocussed",
   "unicode": "\ue898"
 },
 {
   "font_class": "Hub-1",
   "unicode": "\ue998"
 },
 {
   "font_class": "ViewDesktopMobile-1",
   "unicode": "\uea98"
 },
 {
   "font_class": "Bed-1",
   "unicode": "\ue699"
 },
 {
   "font_class": "Delete-1",
   "unicode": "\ue799"
 },
 {
   "font_class": "MailUnread-1",
   "unicode": "\ue899"
 },
 {
   "font_class": "SiteBlocked",
   "unicode": "\ue999"
 },
 {
   "font_class": "Timer2-1",
   "unicode": "\uea99"
 },
 {
   "font_class": "Block-1",
   "unicode": "\ue69a"
 },
 {
   "font_class": "Desktop-1",
   "unicode": "\ue79a"
 },
 {
   "font_class": "MalwareDetected",
   "unicode": "\ue89a"
 },
 {
   "font_class": "Signature",
   "unicode": "\ue99a"
 },
 {
   "font_class": "TextBulletList",
   "unicode": "\uea9a"
 },
 {
   "font_class": "Block",
   "unicode": "\ue69b"
 },
 {
   "font_class": "DeleteOff",
   "unicode": "\ue79b"
 },
 {
   "font_class": "MailUnsubscribe",
   "unicode": "\ue89b"
 },
 {
   "font_class": "Signed",
   "unicode": "\ue99b"
 },
 {
   "font_class": "Timer-1",
   "unicode": "\uea9b"
 },
 {
   "font_class": "Bed",
   "unicode": "\ue69c"
 },
 {
   "font_class": "Desktop",
   "unicode": "\ue79c"
 },
 {
   "font_class": "MailInbox",
   "unicode": "\ue89c"
 },
 {
   "font_class": "SiteWarning-1",
   "unicode": "\ue99c"
 },
 {
   "font_class": "TextAdd-1",
   "unicode": "\uea9c"
 },
 {
   "font_class": "BatteryWarning",
   "unicode": "\ue69d"
 },
 {
   "font_class": "DeveloperBoard-1",
   "unicode": "\ue79d"
 },
 {
   "font_class": "MailRead",
   "unicode": "\ue89d"
 },
 {
   "font_class": "SlideAdd-1",
   "unicode": "\ue99d"
 },
 {
   "font_class": "TimePicker-1",
   "unicode": "\uea9d"
 },
 {
   "font_class": "BatterySaver",
   "unicode": "\ue69e"
 },
 {
   "font_class": "DeleteOff-1",
   "unicode": "\ue79e"
 },
 {
   "font_class": "TextBulletList-1",
   "unicode": "\ue89e"
 },
 {
   "font_class": "Sleep",
   "unicode": "\ue99e"
 },
 {
   "font_class": "Timer10",
   "unicode": "\uea9e"
 },
 {
   "font_class": "BluetoothConnected-1",
   "unicode": "\ue69f"
 },
 {
   "font_class": "DeveloperBoard",
   "unicode": "\ue79f"
 },
 {
   "font_class": "MailUnread",
   "unicode": "\ue89f"
 },
 {
   "font_class": "SiteWarning",
   "unicode": "\ue99f"
 },
 {
   "font_class": "TopSpeed-1",
   "unicode": "\uea9f"
 },
 {
   "font_class": "BluetoothConnected",
   "unicode": "\ue6a0"
 },
 {
   "font_class": "Delete",
   "unicode": "\ue7a0"
 },
 {
   "font_class": "Manufacturer-1",
   "unicode": "\ue8a0"
 },
 {
   "font_class": "SlowMode",
   "unicode": "\ue9a0"
 },
 {
   "font_class": "WeatherSqualls",
   "unicode": "\ueaa0"
 },
 {
   "font_class": "Bluetooth",
   "unicode": "\ue6a1"
 },
 {
   "font_class": "Dialpad",
   "unicode": "\ue7a1"
 },
 {
   "font_class": "MalwareDetected-1",
   "unicode": "\ue8a1"
 },
 {
   "font_class": "SlowMode-1",
   "unicode": "\ue9a1"
 },
 {
   "font_class": "TextColor-1",
   "unicode": "\ueaa1"
 },
 {
   "font_class": "BookFormulaCompatibility",
   "unicode": "\ue6a2"
 },
 {
   "font_class": "Dialpad-1",
   "unicode": "\ue7a2"
 },
 {
   "font_class": "MatchAppLayout-1",
   "unicode": "\ue8a2"
 },
 {
   "font_class": "Snooze-1",
   "unicode": "\ue9a2"
 },
 {
   "font_class": "TextNumberListRTL-1",
   "unicode": "\ueaa2"
 },
 {
   "font_class": "Board-1",
   "unicode": "\ue6a3"
 },
 {
   "font_class": "DialpadOff",
   "unicode": "\ue7a3"
 },
 {
   "font_class": "Map",
   "unicode": "\ue8a3"
 },
 {
   "font_class": "Image",
   "unicode": "\ue9a3"
 },
 {
   "font_class": "WeatherBlowingSnow-1",
   "unicode": "\ueaa3"
 },
 {
   "font_class": "BluetoothDisabled",
   "unicode": "\ue6a4"
 },
 {
   "font_class": "Directions-1",
   "unicode": "\ue7a4"
 },
 {
   "font_class": "MarkUnread-1",
   "unicode": "\ue8a4"
 },
 {
   "font_class": "Spacebar-1",
   "unicode": "\ue9a4"
 },
 {
   "font_class": "Mail-1",
   "unicode": "\ueaa4"
 },
 {
   "font_class": "BluetoothDisabled-1",
   "unicode": "\ue6a5"
 },
 {
   "font_class": "Dismiss",
   "unicode": "\ue7a5"
 },
 {
   "font_class": "MarkRead",
   "unicode": "\ue8a5"
 },
 {
   "font_class": "Speaker1-1",
   "unicode": "\ue9a5"
 },
 {
   "font_class": "TextUnderline",
   "unicode": "\ueaa5"
 },
 {
   "font_class": "Bluetooth-1",
   "unicode": "\ue6a6"
 },
 {
   "font_class": "DividerTall-1",
   "unicode": "\ue7a6"
 },
 {
   "font_class": "Manufacturer",
   "unicode": "\ue8a6"
 },
 {
   "font_class": "Speaker0",
   "unicode": "\ue9a6"
 },
 {
   "font_class": "WeatherHailDay-1",
   "unicode": "\ueaa6"
 },
 {
   "font_class": "BookFormulaCompatibility-1",
   "unicode": "\ue6a7"
 },
 {
   "font_class": "DismissCircle",
   "unicode": "\ue7a7"
 },
 {
   "font_class": "MeetNow-1",
   "unicode": "\ue8a7"
 },
 {
   "font_class": "Spacebar",
   "unicode": "\ue9a7"
 },
 {
   "font_class": "SpeakerBluetooth-1",
   "unicode": "\ueaa7"
 },
 {
   "font_class": "BluetoothSearching-1",
   "unicode": "\ue6a8"
 },
 {
   "font_class": "Dismiss-1",
   "unicode": "\ue7a8"
 },
 {
   "font_class": "MegaphoneOff",
   "unicode": "\ue8a8"
 },
 {
   "font_class": "Speaker0-1",
   "unicode": "\ue9a8"
 },
 {
   "font_class": "TextStrikethrough-1",
   "unicode": "\ueaa8"
 },
 {
   "font_class": "BluetoothSearching",
   "unicode": "\ue6a9"
 },
 {
   "font_class": "DividerShort-1",
   "unicode": "\ue7a9"
 },
 {
   "font_class": "ConferenceRoom-1",
   "unicode": "\ue8a9"
 },
 {
   "font_class": "SpeakerBluetooth",
   "unicode": "\ue9a9"
 },
 {
   "font_class": "Notepad-1",
   "unicode": "\ueaa9"
 },
 {
   "font_class": "Board",
   "unicode": "\ue6aa"
 },
 {
   "font_class": "DismissCircle-1",
   "unicode": "\ue7aa"
 },
 {
   "font_class": "MailUnsubscribe-1",
   "unicode": "\ue8aa"
 },
 {
   "font_class": "Snooze",
   "unicode": "\ue9aa"
 },
 {
   "font_class": "Timeline-1",
   "unicode": "\ueaaa"
 },
 {
   "font_class": "BookFormulaDatabase-1",
   "unicode": "\ue6ab"
 },
 {
   "font_class": "Link-1",
   "unicode": "\ue7ab"
 },
 {
   "font_class": "Mention-1",
   "unicode": "\ue8ab"
 },
 {
   "font_class": "SpeakerNone",
   "unicode": "\ue9ab"
 },
 {
   "font_class": "Tab",
   "unicode": "\ueaab"
 },
 {
   "font_class": "BookFormulaDate",
   "unicode": "\ue6ac"
 },
 {
   "font_class": "DockLeft-1",
   "unicode": "\ue7ac"
 },
 {
   "font_class": "Map-1",
   "unicode": "\ue8ac"
 },
 {
   "font_class": "SoundSource-1",
   "unicode": "\ue9ac"
 },
 {
   "font_class": "None-1",
   "unicode": "\ueaac"
 },
 {
   "font_class": "BookFormulaDate-1",
   "unicode": "\ue6ad"
 },
 {
   "font_class": "DividerTall",
   "unicode": "\ue7ad"
 },
 {
   "font_class": "Connector",
   "unicode": "\ue8ad"
 },
 {
   "font_class": "SpeakerNone-1",
   "unicode": "\ue9ad"
 },
 {
   "font_class": "Ticket-1",
   "unicode": "\ueaad"
 },
 {
   "font_class": "BookFormulaDatabase",
   "unicode": "\ue6ae"
 },
 {
   "font_class": "DockLeft",
   "unicode": "\ue7ae"
 },
 {
   "font_class": "Mention",
   "unicode": "\ue8ae"
 },
 {
   "font_class": "SpeakerOff-1",
   "unicode": "\ue9ae"
 },
 {
   "font_class": "Notebook",
   "unicode": "\ueaae"
 },
 {
   "font_class": "BookFormulaFinancial-1",
   "unicode": "\ue6af"
 },
 {
   "font_class": "Dock-1",
   "unicode": "\ue7af"
 },
 {
   "font_class": "MeetNow",
   "unicode": "\ue8af"
 },
 {
   "font_class": "SpeakerOff",
   "unicode": "\ue9af"
 },
 {
   "font_class": "Notepad",
   "unicode": "\ueaaf"
 },
 {
   "font_class": "BookFormulaEngineering-1",
   "unicode": "\ue6b0"
 },
 {
   "font_class": "Connector-1",
   "unicode": "\ue7b0"
 },
 {
   "font_class": "MicOn-1",
   "unicode": "\ue8b0"
 },
 {
   "font_class": "IncomingVideoOff-1",
   "unicode": "\ue9b0"
 },
 {
   "font_class": "TapSingle",
   "unicode": "\ueab0"
 },
 {
   "font_class": "BookFormulaInformation-1",
   "unicode": "\ue6b1"
 },
 {
   "font_class": "ConferenceRoom",
   "unicode": "\ue7b1"
 },
 {
   "font_class": "Megaphone",
   "unicode": "\ue8b1"
 },
 {
   "font_class": "StarAdd-1",
   "unicode": "\ue9b1"
 },
 {
   "font_class": "Trophy",
   "unicode": "\ueab1"
 },
 {
   "font_class": "BookFormulaFinancial",
   "unicode": "\ue6b2"
 },
 {
   "font_class": "Dock",
   "unicode": "\ue7b2"
 },
 {
   "font_class": "MicOn",
   "unicode": "\ue8b2"
 },
 {
   "font_class": "SpeakerSettings",
   "unicode": "\ue9b2"
 },
 {
   "font_class": "Timer",
   "unicode": "\ueab2"
 },
 {
   "font_class": "BookFormulaEngineering",
   "unicode": "\ue6b3"
 },
 {
   "font_class": "DocumentAutosave-1",
   "unicode": "\ue7b3"
 },
 {
   "font_class": "MissingMetadata-1",
   "unicode": "\ue8b3"
 },
 {
   "font_class": "Speaker-1",
   "unicode": "\ue9b3"
 },
 {
   "font_class": "Unlock",
   "unicode": "\ueab3"
 },
 {
   "font_class": "BookFormulaLogical",
   "unicode": "\ue6b4"
 },
 {
   "font_class": "DocumentAutosave",
   "unicode": "\ue7b4"
 },
 {
   "font_class": "MissingMetadata",
   "unicode": "\ue8b4"
 },
 {
   "font_class": "StarOff-1",
   "unicode": "\ue9b4"
 },
 {
   "font_class": "TripleColumn",
   "unicode": "\ueab4"
 },
 {
   "font_class": "BookFormulaLogical-1",
   "unicode": "\ue6b5"
 },
 {
   "font_class": "ContactCardGroup-1",
   "unicode": "\ue7b5"
 },
 {
   "font_class": "MegaphoneOff-1",
   "unicode": "\ue8b5"
 },
 {
   "font_class": "iOSArrowLeft",
   "unicode": "\ue9b5"
 },
 {
   "font_class": "Upgrade",
   "unicode": "\ueab5"
 },
 {
   "font_class": "BookFormulaMath-1",
   "unicode": "\ue6b6"
 },
 {
   "font_class": "DocumentCatchUp",
   "unicode": "\ue7b6"
 },
 {
   "font_class": "Megaphone-1",
   "unicode": "\ue8b6"
 },
 {
   "font_class": "Star",
   "unicode": "\ue9b6"
 },
 {
   "font_class": "Upgrade-1",
   "unicode": "\ueab6"
 },
 {
   "font_class": "BookFormulaMath",
   "unicode": "\ue6b7"
 },
 {
   "font_class": "DocumentDiscard-1",
   "unicode": "\ue7b7"
 },
 {
   "font_class": "MicOff",
   "unicode": "\ue8b7"
 },
 {
   "font_class": "StarOff",
   "unicode": "\ue9b7"
 },
 {
   "font_class": "VideoSecurity",
   "unicode": "\ueab7"
 },
 {
   "font_class": "BookFormulaStatistics",
   "unicode": "\ue6b8"
 },
 {
   "font_class": "DocumentCatchUp-1",
   "unicode": "\ue7b8"
 },
 {
   "font_class": "Midi-1",
   "unicode": "\ue8b8"
 },
 {
   "font_class": "Status-1",
   "unicode": "\ue9b8"
 },
 {
   "font_class": "Unsave",
   "unicode": "\ueab8"
 },
 {
   "font_class": "Bookmark",
   "unicode": "\ue6b9"
 },
 {
   "font_class": "MoreVertical",
   "unicode": "\ue7b9"
 },
 {
   "font_class": "MarkUnread",
   "unicode": "\ue8b9"
 },
 {
   "font_class": "Status",
   "unicode": "\ue9b9"
 },
 {
   "font_class": "WeatherMoon-1",
   "unicode": "\ueab9"
 },
 {
   "font_class": "BookFormulaRecent-1",
   "unicode": "\ue6ba"
 },
 {
   "font_class": "DocumentCopy",
   "unicode": "\ue7ba"
 },
 {
   "font_class": "More",
   "unicode": "\ue8ba"
 },
 {
   "font_class": "StickerAdd",
   "unicode": "\ue9ba"
 },
 {
   "font_class": "Owner-1",
   "unicode": "\ueaba"
 },
 {
   "font_class": "Bookmark-1",
   "unicode": "\ue6bb"
 },
 {
   "font_class": "DocumentCopy-1",
   "unicode": "\ue7bb"
 },
 {
   "font_class": "DocumentDiscard",
   "unicode": "\ue8bb"
 },
 {
   "font_class": "Sticker",
   "unicode": "\ue9bb"
 },
 {
   "font_class": "Thinking",
   "unicode": "\ueabb"
 },
 {
   "font_class": "BookFormulaText",
   "unicode": "\ue6bc"
 },
 {
   "font_class": "Document-1",
   "unicode": "\ue7bc"
 },
 {
   "font_class": "Text-1",
   "unicode": "\ue8bc"
 },
 {
   "font_class": "Storage-1",
   "unicode": "\ue9bc"
 },
 {
   "font_class": "TextAlignCenter-1",
   "unicode": "\ueabc"
 },
 {
   "font_class": "Branch-1",
   "unicode": "\ue6bd"
 },
 {
   "font_class": "DocumentSearch-1",
   "unicode": "\ue7bd"
 },
 {
   "font_class": "Music",
   "unicode": "\ue8bd"
 },
 {
   "font_class": "StarAdd",
   "unicode": "\ue9bd"
 },
 {
   "font_class": "PhoneAddNewApp-1",
   "unicode": "\ueabd"
 },
 {
   "font_class": "Branch",
   "unicode": "\ue6be"
 },
 {
   "font_class": "DocumentPDF",
   "unicode": "\ue7be"
 },
 {
   "font_class": "Multiselect",
   "unicode": "\ue8be"
 },
 {
   "font_class": "Sticker-1",
   "unicode": "\ue9be"
 },
 {
   "font_class": "Tabs",
   "unicode": "\ueabe"
 },
 {
   "font_class": "BroadActivityFeed-1",
   "unicode": "\ue6bf"
 },
 {
   "font_class": "DocumentSearch",
   "unicode": "\ue7bf"
 },
 {
   "font_class": "MoreVertical-1",
   "unicode": "\ue8bf"
 },
 {
   "font_class": "Star-1",
   "unicode": "\ue9bf"
 },
 {
   "font_class": "NotebookSync",
   "unicode": "\ueabf"
 },
 {
   "font_class": "Bot",
   "unicode": "\ue6c0"
 },
 {
   "font_class": "DoubleSwipeDown",
   "unicode": "\ue7c0"
 },
 {
   "font_class": "Multiselect-1",
   "unicode": "\ue8c0"
 },
 {
   "font_class": "SpeakerSettings-1",
   "unicode": "\ue9c0"
 },
 {
   "font_class": "Wallpaper",
   "unicode": "\ueac0"
 },
 {
   "font_class": "BroadActivityFeed",
   "unicode": "\ue6c1"
 },
 {
   "font_class": "CheckmarkCircle-1",
   "unicode": "\ue7c1"
 },
 {
   "font_class": "Navigation",
   "unicode": "\ue8c1"
 },
 {
   "font_class": "Shapes",
   "unicode": "\ue9c1"
 },
 {
   "font_class": "TableEdit-1",
   "unicode": "\ueac1"
 },
 {
   "font_class": "BookFormulaRecent",
   "unicode": "\ue6c2"
 },
 {
   "font_class": "DoubleSwipeDown-1",
   "unicode": "\ue7c2"
 },
 {
   "font_class": "New",
   "unicode": "\ue8c2"
 },
 {
   "font_class": "Storage",
   "unicode": "\ue9c2"
 },
 {
   "font_class": "Warning",
   "unicode": "\ueac2"
 },
 {
   "font_class": "BotAdd",
   "unicode": "\ue6c3"
 },
 {
   "font_class": "DoubleTapSwipeDown",
   "unicode": "\ue7c3"
 },
 {
   "font_class": "New-1",
   "unicode": "\ue8c3"
 },
 {
   "font_class": "StickerAdd-1",
   "unicode": "\ue9c3"
 },
 {
   "font_class": "PersonAdd",
   "unicode": "\ueac3"
 },
 {
   "font_class": "BotAdd-1",
   "unicode": "\ue6c4"
 },
 {
   "font_class": "DoubleSwipeUp",
   "unicode": "\ue7c4"
 },
 {
   "font_class": "Music-1",
   "unicode": "\ue8c4"
 },
 {
   "font_class": "Subway-1",
   "unicode": "\ue9c4"
 },
 {
   "font_class": "Live",
   "unicode": "\ueac4"
 },
 {
   "font_class": "Calendar3Day-1",
   "unicode": "\ue6c5"
 },
 {
   "font_class": "DoubleSwipeUp-1",
   "unicode": "\ue7c5"
 },
 {
   "font_class": "Midi",
   "unicode": "\ue8c5"
 },
 {
   "font_class": "StyleGuide-1",
   "unicode": "\ue9c5"
 },
 {
   "font_class": "WeatherSunny",
   "unicode": "\ueac5"
 },
 {
   "font_class": "Building",
   "unicode": "\ue6c6"
 },
 {
   "font_class": "DoubleTapSwipeUp-1",
   "unicode": "\ue7c6"
 },
 {
   "font_class": "NetworkCheck",
   "unicode": "\ue8c6"
 },
 {
   "font_class": "KeyboardLayoutOneHandedLeft",
   "unicode": "\ue9c6"
 },
 {
   "font_class": "TextAlignJustify-1",
   "unicode": "\ueac6"
 },
 {
   "font_class": "CalendarAdd",
   "unicode": "\ue6c7"
 },
 {
   "font_class": "DoubleTapSwipeDown-1",
   "unicode": "\ue7c7"
 },
 {
   "font_class": "MyLocation-1",
   "unicode": "\ue8c7"
 },
 {
   "font_class": "SubGrid-1",
   "unicode": "\ue9c7"
 },
 {
   "font_class": "Video",
   "unicode": "\ueac7"
 },
 {
   "font_class": "Bot-1",
   "unicode": "\ue6c8"
 },
 {
   "font_class": "Drafts",
   "unicode": "\ue7c8"
 },
 {
   "font_class": "MobileOptimized",
   "unicode": "\ue8c8"
 },
 {
   "font_class": "Store-1",
   "unicode": "\ue9c8"
 },
 {
   "font_class": "PhonePagination",
   "unicode": "\ueac8"
 },
 {
   "font_class": "CalendarAdd-1",
   "unicode": "\ue6c9"
 },
 {
   "font_class": "Drafts-1",
   "unicode": "\ue7c9"
 },
 {
   "font_class": "Note",
   "unicode": "\ue8c9"
 },
 {
   "font_class": "StyleGuide",
   "unicode": "\ue9c9"
 },
 {
   "font_class": "WeatherPartlyCloudyDay",
   "unicode": "\ueac9"
 }
] as IconsDataItem[]

// export const fontData = JSON.parse<IconsDataItem>(fontDataJson)
