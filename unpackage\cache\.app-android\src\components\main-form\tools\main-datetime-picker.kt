@file:Suppress("UNCHECKED_CAST", "USELESS_CAST", "INAPPLICABLE_JVM_NAME", "UNUSED_ANONYMOUS_PARAMETER", "NAME_SHADOWING", "UNNECESSARY_NOT_NULL_ASSERTION")
package uni.UNIC178CB1
import io.dcloud.uniapp.*
import io.dcloud.uniapp.extapi.*
import io.dcloud.uniapp.framework.*
import io.dcloud.uniapp.runtime.*
import io.dcloud.uniapp.vue.*
import io.dcloud.uniapp.vue.shared.*
import io.dcloud.unicloud.*
import io.dcloud.uts.*
import io.dcloud.uts.Map
import io.dcloud.uts.Set
import io.dcloud.uts.UTSAndroid
import io.dcloud.uniapp.extapi.showToast as uni_showToast
open class GenComponentsMainFormToolsMainDatetimePicker : VueComponent {
    constructor(__ins: ComponentInternalInstance) : super(__ins) {
        onCreated(fun() {
            this.initData()
        }
        , __ins)
    }
    @Suppress("UNUSED_PARAMETER", "UNUSED_VARIABLE")
    override fun `$render`(): Any? {
        val _ctx = this
        val _cache = this.`$`.renderCache
        val _component_picker_view_column = resolveComponent("picker-view-column")
        val _component_picker_view = resolveComponent("picker-view")
        return if (isTrue(_ctx.visible)) {
            _cE("view", _uM("key" to 0, "class" to "picker-overlay", "onClick" to _ctx.onOverlayClick), _uA(
                _cE("view", _uM("class" to "picker-modal", "onClick" to withModifiers(fun(){}, _uA(
                    "stop"
                ))), _uA(
                    _cE("view", _uM("class" to "datetime-picker-container"), _uA(
                        _cE("view", _uM("class" to "navbar"), _uA(
                            _cE("text", _uM("class" to "nav-btn cancel-btn", "onClick" to _ctx.onCancel), "取消", 8, _uA(
                                "onClick"
                            )),
                            _cE("text", _uM("class" to "nav-title"), _tD(_ctx.displayTitle), 1),
                            _cE("view", _uM("class" to "confirm-btn-container"), _uA(
                                _cE("text", _uM("class" to "nav-btn confirm-btn", "onClick" to _ctx.onConfirm), "确定", 8, _uA(
                                    "onClick"
                                ))
                            ))
                        )),
                        if (_ctx.quickOptions.length > 0) {
                            _cE("view", _uM("key" to 0, "class" to "quick-options"), _uA(
                                _cE(Fragment, null, RenderHelpers.renderList(_ctx.quickOptions, fun(option, index, __index, _cached): Any {
                                    return _cE("text", _uM("key" to index, "class" to _nC(_uA(
                                        "quick-item",
                                        _uM("quick-item-active" to (_ctx.currentQuickIndex == index))
                                    )), "onClick" to fun(){
                                        _ctx.onQuickSelectByIndex(index)
                                    }), _tD(option.label), 11, _uA(
                                        "onClick"
                                    ))
                                }), 128)
                            ))
                        } else {
                            _cC("v-if", true)
                        },
                        if (isTrue(_ctx.isRange)) {
                            _cE("view", _uM("key" to 1, "class" to "range-tabs"), _uA(
                                _cE("text", _uM("class" to _nC(_uA(
                                    "range-tab",
                                    _uM("range-tab-active" to (_ctx.rangeIndex == 0))
                                )), "onClick" to fun(){
                                    _ctx.onRangeChange(0)
                                }), " 开始时间 ", 10, _uA(
                                    "onClick"
                                )),
                                _cE("text", _uM("class" to _nC(_uA(
                                    "range-tab",
                                    _uM("range-tab-active" to (_ctx.rangeIndex == 1))
                                )), "onClick" to fun(){
                                    _ctx.onRangeChange(1)
                                }), " 结束时间 ", 10, _uA(
                                    "onClick"
                                ))
                            ))
                        } else {
                            _cC("v-if", true)
                        },
                        _cE("view", _uM("class" to "picker-body"), _uA(
                            _cV(_component_picker_view, _uM("value" to _ctx.pickerValue, "onChange" to _ctx.onPickerChange, "class" to "picker-view", "indicator-style" to _ctx.indicatorStyle, "mask-style" to _ctx.maskStyle), _uM("default" to withSlotCtx(fun(): UTSArray<Any> {
                                return _uA(
                                    if (isTrue(_ctx.showYear)) {
                                        _cV(_component_picker_view_column, _uM("key" to 0), _uM("default" to withSlotCtx(fun(): UTSArray<Any> {
                                            return _uA(
                                                _cE(Fragment, null, RenderHelpers.renderList(_ctx.years, fun(year, __key, __index, _cached): Any {
                                                    return _cE("view", _uM("class" to "picker-item", "key" to year), _uA(
                                                        _cE("text", _uM("class" to "picker-text"), _tD(year) + "年", 1)
                                                    ))
                                                }), 128)
                                            )
                                        }), "_" to 1))
                                    } else {
                                        _cC("v-if", true)
                                    },
                                    if (isTrue(_ctx.showMonth)) {
                                        _cV(_component_picker_view_column, _uM("key" to 1), _uM("default" to withSlotCtx(fun(): UTSArray<Any> {
                                            return _uA(
                                                _cE(Fragment, null, RenderHelpers.renderList(_ctx.months, fun(month, __key, __index, _cached): Any {
                                                    return _cE("view", _uM("class" to "picker-item", "key" to month), _uA(
                                                        _cE("text", _uM("class" to "picker-text"), _tD(month) + "月", 1)
                                                    ))
                                                }), 128)
                                            )
                                        }), "_" to 1))
                                    } else {
                                        _cC("v-if", true)
                                    },
                                    if (isTrue(_ctx.showDay)) {
                                        _cV(_component_picker_view_column, _uM("key" to 2), _uM("default" to withSlotCtx(fun(): UTSArray<Any> {
                                            return _uA(
                                                _cE(Fragment, null, RenderHelpers.renderList(_ctx.days, fun(day, __key, __index, _cached): Any {
                                                    return _cE("view", _uM("class" to "picker-item", "key" to day), _uA(
                                                        _cE("text", _uM("class" to "picker-text"), _tD(day) + "日", 1)
                                                    ))
                                                }), 128)
                                            )
                                        }), "_" to 1))
                                    } else {
                                        _cC("v-if", true)
                                    },
                                    if (isTrue(_ctx.showHour)) {
                                        _cV(_component_picker_view_column, _uM("key" to 3), _uM("default" to withSlotCtx(fun(): UTSArray<Any> {
                                            return _uA(
                                                _cE(Fragment, null, RenderHelpers.renderList(_ctx.hours, fun(hour, __key, __index, _cached): Any {
                                                    return _cE("view", _uM("class" to "picker-item", "key" to hour), _uA(
                                                        _cE("text", _uM("class" to "picker-text"), _tD(hour) + "时", 1)
                                                    ))
                                                }), 128)
                                            )
                                        }), "_" to 1))
                                    } else {
                                        _cC("v-if", true)
                                    },
                                    if (isTrue(_ctx.showMinute)) {
                                        _cV(_component_picker_view_column, _uM("key" to 4), _uM("default" to withSlotCtx(fun(): UTSArray<Any> {
                                            return _uA(
                                                _cE(Fragment, null, RenderHelpers.renderList(_ctx.minutes, fun(minute, __key, __index, _cached): Any {
                                                    return _cE("view", _uM("class" to "picker-item", "key" to minute), _uA(
                                                        _cE("text", _uM("class" to "picker-text"), _tD(minute) + "分", 1)
                                                    ))
                                                }), 128)
                                            )
                                        }), "_" to 1))
                                    } else {
                                        _cC("v-if", true)
                                    },
                                    if (isTrue(_ctx.showSecond)) {
                                        _cV(_component_picker_view_column, _uM("key" to 5), _uM("default" to withSlotCtx(fun(): UTSArray<Any> {
                                            return _uA(
                                                _cE(Fragment, null, RenderHelpers.renderList(_ctx.seconds, fun(second, __key, __index, _cached): Any {
                                                    return _cE("view", _uM("class" to "picker-item", "key" to second), _uA(
                                                        _cE("text", _uM("class" to "picker-text"), _tD(second) + "秒", 1)
                                                    ))
                                                }), 128)
                                            )
                                        }), "_" to 1))
                                    } else {
                                        _cC("v-if", true)
                                    }
                                )
                            }), "_" to 1), 8, _uA(
                                "value",
                                "onChange",
                                "indicator-style",
                                "mask-style"
                            ))
                        )),
                        _cE("view", _uM("class" to "current-selection"), _uA(
                            _cE("text", _uM("class" to "selection-label"), "当前选择："),
                            _cE("text", _uM("class" to "selection-value"), _tD(_ctx.currentDisplayValue), 1)
                        ))
                    ))
                ), 8, _uA(
                    "onClick"
                ))
            ), 8, _uA(
                "onClick"
            ))
        } else {
            _cC("v-if", true)
        }
    }
    open var mode: String by `$props`
    open var title: String by `$props`
    open var showSeconds: Boolean by `$props`
    open var startYear: Number by `$props`
    open var endYear: Number by `$props`
    open var quickOptions: UTSArray<QuickOption> by `$props`
    open var height: Number by `$props`
    open var visible: Boolean by `$data`
    open var currentDate: Date by `$data`
    open var rangeValues: UTSArray<Date> by `$data`
    open var rangeIndex: Number by `$data`
    open var pickerValue: UTSArray<Number> by `$data`
    open var currentQuickIndex: Number by `$data`
    open var years: UTSArray<String> by `$data`
    open var months: UTSArray<String> by `$data`
    open var days: UTSArray<String> by `$data`
    open var hours: UTSArray<String> by `$data`
    open var minutes: UTSArray<String> by `$data`
    open var seconds: UTSArray<String> by `$data`
    open var isInitialized: Boolean by `$data`
    open var isRange: Boolean by `$data`
    open var showYear: Boolean by `$data`
    open var showMonth: Boolean by `$data`
    open var showDay: Boolean by `$data`
    open var showHour: Boolean by `$data`
    open var showMinute: Boolean by `$data`
    open var showSecond: Boolean by `$data`
    open var indicatorStyle: String by `$data`
    open var maskStyle: String by `$data`
    open var displayTitle: String by `$data`
    open var currentDisplayValue: String by `$data`
    @Suppress("USELESS_CAST")
    override fun data(): Map<String, Any?> {
        val now: Date = Date()
        return _uM("visible" to false as Boolean, "currentDate" to now as Date, "rangeValues" to _uA<Date>(now, now), "rangeIndex" to 0 as Number, "pickerValue" to _uA<Number>(), "currentQuickIndex" to -1 as Number, "years" to _uA<String>(), "months" to _uA<String>(), "days" to _uA<String>(), "hours" to _uA<String>(), "minutes" to _uA<String>(), "seconds" to _uA<String>(), "isInitialized" to false as Boolean, "isRange" to computed<Boolean>(fun(): Boolean {
            return this.mode.includes("range")
        }
        ), "showYear" to computed<Boolean>(fun(): Boolean {
            return !_uA(
                "time",
                "hour-minute",
                "hour-minute-second"
            ).includes(this.mode)
        }
        ), "showMonth" to computed<Boolean>(fun(): Boolean {
            return _uA(
                "datetime",
                "date",
                "year-month",
                "month"
            ).includes(this.mode) || _uA(
                "datetime-range",
                "date-range"
            ).includes(this.mode)
        }
        ), "showDay" to computed<Boolean>(fun(): Boolean {
            return _uA(
                "datetime",
                "date"
            ).includes(this.mode) || _uA(
                "datetime-range",
                "date-range"
            ).includes(this.mode)
        }
        ), "showHour" to computed<Boolean>(fun(): Boolean {
            return _uA(
                "datetime",
                "time",
                "hour-minute",
                "hour-minute-second"
            ).includes(this.mode) || _uA(
                "datetime-range",
                "time-range"
            ).includes(this.mode)
        }
        ), "showMinute" to computed<Boolean>(fun(): Boolean {
            return _uA(
                "datetime",
                "time",
                "hour-minute",
                "hour-minute-second"
            ).includes(this.mode) || _uA(
                "datetime-range",
                "time-range"
            ).includes(this.mode)
        }
        ), "showSecond" to computed<Boolean>(fun(): Boolean {
            return (this.showSeconds && _uA(
                "datetime",
                "time",
                "hour-minute-second"
            ).includes(this.mode)) || (this.showSeconds && _uA(
                "datetime-range",
                "time-range"
            ).includes(this.mode))
        }
        ), "indicatorStyle" to computed<String>(fun(): String {
            return "height: 44px; border-top: 1px solid #eee; border-bottom: 1px solid #eee;"
        }
        ), "maskStyle" to computed<String>(fun(): String {
            return "background-image: linear-gradient(180deg, rgba(255,255,255,0.95), rgba(255,255,255,0.6)), linear-gradient(0deg, rgba(255,255,255,0.95), rgba(255,255,255,0.6));"
        }
        ), "displayTitle" to computed<String>(fun(): String {
            if (this.title != "选择时间") {
                return this.title
            }
            val modeMap: UTSJSONObject = object : UTSJSONObject(UTSSourceMapPosition("modeMap", "components/main-form/tools/main-datetime-picker.uvue", 209, 11)) {
                var datetime = "选择日期时间"
                var date = "选择日期"
                var time = "选择时间"
                var year = "选择年份"
                var `year-month` = "选择年月"
                var month = "选择月份"
                var day = "选择日期"
                var `hour-minute` = "选择时间"
                var `hour-minute-second` = "选择时间"
                var `datetime-range` = "选择日期时间范围"
                var `date-range` = "选择日期范围"
                var `time-range` = "选择时间范围"
            }
            val result = modeMap[this.mode] as String?
            return if (result != null) {
                result
            } else {
                "选择时间"
            }
        }
        ), "currentDisplayValue" to computed<String>(fun(): String {
            if (this.isRange) {
                val startFormatted: String = this.formatDate(this.rangeValues[0], this.mode.replace("-range", "") as DateTimeMode)
                val endFormatted: String = this.formatDate(this.rangeValues[1], this.mode.replace("-range", "") as DateTimeMode)
                return startFormatted + " 至 " + endFormatted
            } else {
                return this.formatDate(this.currentDate, this.mode)
            }
        }
        ))
    }
    open var formatDate = ::gen_formatDate_fn
    open fun gen_formatDate_fn(date: Date?, type: DateTimeMode): String {
        if (date == null) {
            return ""
        }
        try {
            val d: Date = date
            if (!this.validateDate(d)) {
                return ""
            }
            val year: Number = d.getFullYear()
            val month: String = (d.getMonth() + 1).toString(10).padStart(2, "0")
            val day: String = d.getDate().toString(10).padStart(2, "0")
            val hour: String = d.getHours().toString(10).padStart(2, "0")
            val minute: String = d.getMinutes().toString(10).padStart(2, "0")
            val second: String = d.getSeconds().toString(10).padStart(2, "0")
            when (type) {
                "datetime" -> 
                    return "" + year + "-" + month + "-" + day + " " + hour + ":" + minute + (if (this.showSeconds) {
                        ":" + second
                    } else {
                        ""
                    }
                    )
                "date" -> 
                    return "" + year + "-" + month + "-" + day
                "time" -> 
                    return "" + hour + ":" + minute + (if (this.showSeconds) {
                        ":" + second
                    } else {
                        ""
                    }
                    )
                "year" -> 
                    return "" + year
                "year-month" -> 
                    return "" + year + "-" + month
                "month" -> 
                    return month
                "day" -> 
                    return day
                "hour-minute" -> 
                    return "" + hour + ":" + minute
                "hour-minute-second" -> 
                    return "" + hour + ":" + minute + ":" + second
                else -> 
                    return "" + year + "-" + month + "-" + day + " " + hour + ":" + minute + (if (this.showSeconds) {
                        ":" + second
                    } else {
                        ""
                    }
                    )
            }
        }
         catch (error: Throwable) {
            console.error("Format date error:", error, date, " at components/main-form/tools/main-datetime-picker.uvue:281")
            return ""
        }
    }
    open fun parseDate(value: Any?): Date {
        if (value == null) {
            return Date()
        }
        try {
            var date: Date? = null
            if (value is Date) {
                date = Date((value as Date).getTime())
            } else if (UTSAndroid.`typeof`(value) == "number" && !isNaN(value as Number)) {
                date = Date(value as Number)
            } else if (UTSAndroid.`typeof`(value) == "string") {
                if ((value as String).includes("T")) {
                    date = Date(value as String)
                } else if ((value as String).includes("-") || (value as String).includes("/")) {
                    val parts: UTSArray<Number> = (value as String).split(UTSRegExp("[-\\s:/]", "")).map(fun(p): Number {
                        return parseInt(p)
                    })
                    if (parts.length >= 3) {
                        date = Date(parts[0], parts[1] - 1, parts[2], if (parts.length > 3) {
                            parts[3]
                        } else {
                            0
                        }
                        , if (parts.length > 4) {
                            parts[4]
                        } else {
                            0
                        }
                        , if (parts.length > 5) {
                            parts[5]
                        } else {
                            0
                        }
                        )
                    }
                } else {
                    val timestamp: Number = parseInt(value as String)
                    if (!isNaN(timestamp)) {
                        date = Date(timestamp as Number)
                    }
                }
            }
            return if (date != null && !isNaN(date.getTime())) {
                date
            } else {
                Date()
            }
        }
         catch (error: Throwable) {
            console.error("Parse date error:", error, " at components/main-form/tools/main-datetime-picker.uvue:327")
            return Date()
        }
    }
    open var validateDate = ::gen_validateDate_fn
    open fun gen_validateDate_fn(date: Date): Boolean {
        if (isNaN(date.getTime())) {
            console.warn("Invalid date:", date, " at components/main-form/tools/main-datetime-picker.uvue:335")
            return false
        }
        val year: Number = date.getFullYear()
        if (year < this.startYear || year > this.endYear) {
            console.warn("Date out of range:", date, " at components/main-form/tools/main-datetime-picker.uvue:341")
            return false
        }
        return true
    }
    open fun show(value: Any?) {
        this.visible = true
        this.currentQuickIndex = -1
        this.rangeIndex = 0
        try {
            if (this.isRange) {
                if (UTSArray.isArray(value) && (value as UTSArray<Date>).length == 2) {
                    this.rangeValues = (value as UTSArray<Date>).map(fun(v): Date {
                        return this.parseDate(v)
                    })
                } else if (UTSAndroid.`typeof`(value) == "string") {
                    val date: Date = this.parseDate(value as String)
                    this.rangeValues = _uA(
                        date,
                        date
                    )
                } else {
                    val now: Date = Date()
                    this.rangeValues = _uA(
                        now,
                        now
                    )
                }
            } else {
                this.currentDate = this.parseDate(value)
            }
            this.`$nextTick`(fun(){
                this.initData()
                this.updateCurrentValue()
            }
            )
        }
         catch (error: Throwable) {
            console.error("Show picker error:", error, " at components/main-form/tools/main-datetime-picker.uvue:377")
            val now: Date = Date()
            if (this.isRange) {
                this.rangeValues = _uA(
                    now,
                    now
                )
            } else {
                this.currentDate = now
            }
        }
    }
    open var hide = ::gen_hide_fn
    open fun gen_hide_fn() {
        this.visible = false
    }
    open var onOverlayClick = ::gen_onOverlayClick_fn
    open fun gen_onOverlayClick_fn() {
        this.hide()
        this.`$emit`("cancel")
    }
    open var onCancel = ::gen_onCancel_fn
    open fun gen_onCancel_fn() {
        this.hide()
        this.`$emit`("cancel")
    }
    open var onConfirm = ::gen_onConfirm_fn
    open fun gen_onConfirm_fn() {
        try {
            if (this.isRange) {
                if (!this.validateDate(this.rangeValues[0]) || !this.validateDate(this.rangeValues[1])) {
                    uni_showToast(ShowToastOptions(title = "日期格式无效", icon = "none"))
                    return
                }
                if (this.rangeValues[1] < this.rangeValues[0]) {
                    uni_showToast(ShowToastOptions(title = "结束时间不能早于开始时间", icon = "none"))
                    return
                }
                val value: UTSArray<Date> = this.rangeValues.map(fun(date): Date {
                    return Date(date.getTime())
                })
                val formatted: String = value.map(fun(date): String {
                    return this.formatDate(date, this.mode.replace("-range", "") as DateTimeMode)
                }).join(" 至 ")
                this.`$emit`("confirm", _uO("value" to value, "formatted" to formatted))
            } else {
                if (!this.validateDate(this.currentDate)) {
                    uni_showToast(ShowToastOptions(title = "日期格式无效", icon = "none"))
                    return
                }
                val value: Date = Date(this.currentDate.getTime())
                val formatted: String = this.formatDate(value, this.mode)
                this.`$emit`("confirm", _uO("value" to value, "formatted" to formatted))
            }
            this.hide()
        }
         catch (error: Throwable) {
            console.error("Confirm error:", error, " at components/main-form/tools/main-datetime-picker.uvue:451")
            uni_showToast(ShowToastOptions(title = "操作失败", icon = "none"))
        }
    }
    open var onQuickSelectByIndex = ::gen_onQuickSelectByIndex_fn
    open fun gen_onQuickSelectByIndex_fn(index: Number) {
        if (index < 0 || index >= this.quickOptions.length) {
            console.warn("Invalid quick option index:", index, " at components/main-form/tools/main-datetime-picker.uvue:462")
            return
        }
        val option: QuickOption = this.quickOptions[index]
        this.onQuickSelect(option, index)
    }
    open var onQuickSelect = ::gen_onQuickSelect_fn
    open fun gen_onQuickSelect_fn(option: QuickOption, index: Number) {
        this.currentQuickIndex = index
        this.rangeIndex = 0
        try {
            if (this.isRange) {
                if (UTSArray.isArray(option.value)) {
                    val rangeValue: UTSArray<String> = option.value as UTSArray<String>
                    if (rangeValue.length != 2) {
                        console.warn("Quick option value should have 2 items for range mode:", option, " at components/main-form/tools/main-datetime-picker.uvue:482")
                        return
                    }
                    this.rangeValues = rangeValue.map(fun(v): Date {
                        return this.parseDate(v)
                    })
                } else {
                    val date: Date = this.parseDate(option.value as String)
                    this.rangeValues = _uA(
                        date,
                        date
                    )
                }
            } else {
                if (UTSArray.isArray(option.value)) {
                    this.currentDate = this.parseDate((option.value as UTSArray<String>)[0])
                } else {
                    this.currentDate = this.parseDate(option.value as String)
                }
            }
            this.`$nextTick`(fun(){
                this.initData()
                this.updateCurrentValue()
            }
            )
            if (option.autoConfirm == true) {
                this.onConfirm()
            }
        }
         catch (error: Throwable) {
            console.error("Quick select error:", error, " at components/main-form/tools/main-datetime-picker.uvue:511")
            uni_showToast(ShowToastOptions(title = "快捷选项格式无效", icon = "none"))
        }
    }
    open var onPickerChange = ::gen_onPickerChange_fn
    open fun gen_onPickerChange_fn(e: UniPickerViewChangeEvent) {
        this.pickerValue = e.detail.value
        this.currentQuickIndex = -1
        this.updateDateFromValue()
    }
    open var onRangeChange = ::gen_onRangeChange_fn
    open fun gen_onRangeChange_fn(index: Number) {
        if (this.rangeIndex == index) {
            return
        }
        this.rangeIndex = index
        this.`$nextTick`(fun(){
            this.updateCurrentValue()
        }
        )
    }
    open var initData = ::gen_initData_fn
    open fun gen_initData_fn() {
        if (!this.isInitialized) {
            this.years = _uA()
            run {
                var i: Number = this.startYear
                while(i <= this.endYear){
                    this.years.push(i.toString(10))
                    i++
                }
            }
            this.months = _uA()
            run {
                var i: Number = 1
                while(i <= 12){
                    this.months.push(i.toString(10).padStart(2, "0"))
                    i++
                }
            }
            this.hours = _uA()
            run {
                var i: Number = 0
                while(i <= 23){
                    this.hours.push(i.toString(10).padStart(2, "0"))
                    i++
                }
            }
            this.minutes = _uA()
            run {
                var i: Number = 0
                while(i <= 59){
                    this.minutes.push(i.toString(10).padStart(2, "0"))
                    i++
                }
            }
            this.seconds = _uA()
            run {
                var i: Number = 0
                while(i <= 59){
                    this.seconds.push(i.toString(10).padStart(2, "0"))
                    i++
                }
            }
            this.isInitialized = true
        }
        val date: Date = if (this.isRange) {
            this.rangeValues[this.rangeIndex]
        } else {
            this.currentDate
        }
        val year: Number = date.getFullYear()
        val month: Number = date.getMonth() + 1
        val daysInMonth: Number = Date(year, month, 0).getDate()
        this.days = _uA()
        run {
            var i: Number = 1
            while(i <= daysInMonth){
                this.days.push(i.toString(10).padStart(2, "0"))
                i++
            }
        }
    }
    open var updateCurrentValue = ::gen_updateCurrentValue_fn
    open fun gen_updateCurrentValue_fn() {
        val date: Date = if (this.isRange) {
            this.rangeValues[this.rangeIndex]
        } else {
            this.currentDate
        }
        if (isNaN(date.getTime())) {
            console.warn("Invalid date in updateCurrentValue:", date, " at components/main-form/tools/main-datetime-picker.uvue:587")
            return
        }
        val values: UTSArray<Number> = _uA()
        if (this.showYear) {
            val yearIndex: Number = this.years.findIndex(fun(y): Boolean {
                return parseInt(y) == date.getFullYear()
            }
            )
            values.push(if (yearIndex >= 0) {
                yearIndex
            } else {
                0
            }
            )
        }
        if (this.showMonth) {
            val monthStr: String = (date.getMonth() + 1).toString(10).padStart(2, "0")
            val monthIndex: Number = this.months.findIndex(fun(m): Boolean {
                return m == monthStr
            }
            )
            values.push(if (monthIndex >= 0) {
                monthIndex
            } else {
                0
            }
            )
        }
        if (this.showDay) {
            val dayStr: String = date.getDate().toString(10).padStart(2, "0")
            val dayIndex: Number = this.days.findIndex(fun(d): Boolean {
                return d == dayStr
            }
            )
            values.push(if (dayIndex >= 0) {
                dayIndex
            } else {
                0
            }
            )
        }
        if (this.showHour) {
            val hourStr: String = date.getHours().toString(10).padStart(2, "0")
            val hourIndex: Number = this.hours.findIndex(fun(h): Boolean {
                return h == hourStr
            }
            )
            values.push(if (hourIndex >= 0) {
                hourIndex
            } else {
                0
            }
            )
        }
        if (this.showMinute) {
            val minuteStr: String = date.getMinutes().toString(10).padStart(2, "0")
            val minuteIndex: Number = this.minutes.findIndex(fun(m): Boolean {
                return m == minuteStr
            }
            )
            values.push(if (minuteIndex >= 0) {
                minuteIndex
            } else {
                0
            }
            )
        }
        if (this.showSecond) {
            val secondStr: String = date.getSeconds().toString(10).padStart(2, "0")
            val secondIndex: Number = this.seconds.findIndex(fun(s): Boolean {
                return s == secondStr
            }
            )
            values.push(if (secondIndex >= 0) {
                secondIndex
            } else {
                0
            }
            )
        }
        this.pickerValue = values.slice()
    }
    open var updateDateFromValue = ::gen_updateDateFromValue_fn
    open fun gen_updateDateFromValue_fn() {
        if (!UTSArray.isArray(this.pickerValue)) {
            return
        }
        var index: Number = 0
        var year: Number = this.currentDate.getFullYear()
        var month: Number = this.currentDate.getMonth()
        var day: Number = this.currentDate.getDate()
        var hour: Number = this.currentDate.getHours()
        var minute: Number = this.currentDate.getMinutes()
        var second: Number = this.currentDate.getSeconds()
        if (this.showYear && index < this.pickerValue.length) {
            year = parseInt(this.years[this.pickerValue[index]])
            index++
        }
        if (this.showMonth && index < this.pickerValue.length) {
            month = parseInt(this.months[this.pickerValue[index]]) - 1
            index++
        }
        if (this.showDay && index < this.pickerValue.length) {
            day = parseInt(this.days[this.pickerValue[index]])
            index++
        }
        if (this.showHour && index < this.pickerValue.length) {
            hour = parseInt(this.hours[this.pickerValue[index]])
            index++
        }
        if (this.showMinute && index < this.pickerValue.length) {
            minute = parseInt(this.minutes[this.pickerValue[index]])
            index++
        }
        if (this.showSecond && index < this.pickerValue.length) {
            second = parseInt(this.seconds[this.pickerValue[index]])
        }
        val newDate: Date = Date(year, month, day, hour, minute, second)
        if (this.isRange) {
            this.rangeValues[this.rangeIndex] = newDate
            if (this.rangeIndex == 0 && this.rangeValues[1] < newDate) {
                this.rangeValues[1] = Date(newDate.getTime())
            }
        } else {
            this.currentDate = newDate
        }
        this.initData()
    }
    companion object {
        var name = "main-datetime-picker"
        val styles: Map<String, Map<String, Map<String, Any>>> by lazy {
            _nCS(_uA(
                styles0
            ))
        }
        val styles0: Map<String, Map<String, Map<String, Any>>>
            get() {
                return _uM("picker-overlay" to _pS(_uM("position" to "fixed", "top" to 0, "left" to 0, "right" to 0, "bottom" to 0, "backgroundColor" to "rgba(0,0,0,0.5)", "display" to "flex", "alignItems" to "center", "justifyContent" to "center", "zIndex" to 1000)), "picker-modal" to _pS(_uM("width" to "90%", "maxWidth" to "600rpx", "backgroundColor" to "#ffffff", "borderTopLeftRadius" to "20rpx", "borderTopRightRadius" to "20rpx", "borderBottomRightRadius" to "20rpx", "borderBottomLeftRadius" to "20rpx", "overflow" to "hidden", "boxShadow" to "0 8px 32px rgba(0, 0, 0, 0.3)")), "datetime-picker-container" to _pS(_uM("width" to "100%", "backgroundColor" to "#ffffff", "display" to "flex", "flexDirection" to "column")), "navbar" to _pS(_uM("height" to 44, "backgroundColor" to "#f8f8f8", "borderBottomWidth" to 1, "borderBottomStyle" to "solid", "borderBottomColor" to "#e5e5e5", "display" to "flex", "flexDirection" to "row", "alignItems" to "center", "justifyContent" to "space-between", "paddingTop" to 0, "paddingRight" to 10, "paddingBottom" to 0, "paddingLeft" to 10)), "nav-btn" to _pS(_uM("fontSize" to 16, "color" to "#007aff", "paddingTop" to 8, "paddingRight" to 12, "paddingBottom" to 8, "paddingLeft" to 12)), "cancel-btn" to _pS(_uM("color" to "#999999")), "confirm-btn-container" to _pS(_uM("height" to 30, "backgroundColor" to "#007aff", "borderTopLeftRadius" to "8rpx", "borderTopRightRadius" to "8rpx", "borderBottomRightRadius" to "8rpx", "borderBottomLeftRadius" to "8rpx", "display" to "flex", "justifyContent" to "center", "alignItems" to "center", "boxShadow" to "0 2rpx 8rpx rgba(0, 122, 255, 0.3)")), "confirm-btn" to _pS(_uM("color" to "#ffffff", "fontWeight" to "bold")), "nav-title" to _pS(_uM("fontSize" to 17, "color" to "#333333")), "quick-options" to _pS(_uM("display" to "flex", "flexWrap" to "wrap", "paddingTop" to "10rpx", "paddingRight" to "20rpx", "paddingBottom" to "10rpx", "paddingLeft" to "20rpx", "borderBottomWidth" to "1rpx", "borderBottomStyle" to "solid", "borderBottomColor" to "#eeeeee")), "quick-item" to _pS(_uM("paddingTop" to "6rpx", "paddingRight" to "20rpx", "paddingBottom" to "6rpx", "paddingLeft" to "20rpx", "marginTop" to "6rpx", "marginRight" to "6rpx", "marginBottom" to "6rpx", "marginLeft" to "6rpx", "fontSize" to "24rpx", "color" to "#666666", "backgroundColor" to "#f5f5f5", "borderTopLeftRadius" to "6rpx", "borderTopRightRadius" to "6rpx", "borderBottomRightRadius" to "6rpx", "borderBottomLeftRadius" to "6rpx")), "quick-item-active" to _pS(_uM("color" to "#ffffff", "backgroundColor" to "#007AFF")), "range-tabs" to _pS(_uM("display" to "flex", "paddingTop" to "20rpx", "paddingRight" to "20rpx", "paddingBottom" to "20rpx", "paddingLeft" to "20rpx", "borderBottomWidth" to "1rpx", "borderBottomStyle" to "solid", "borderBottomColor" to "#eeeeee")), "range-tab" to _pS(_uM("flex" to 1, "textAlign" to "center", "fontSize" to "28rpx", "color" to "#666666", "paddingTop" to "10rpx", "paddingRight" to 0, "paddingBottom" to "10rpx", "paddingLeft" to 0)), "range-tab-active" to _pS(_uM("color" to "#007AFF", "position" to "relative")), "picker-body" to _pS(_uM("position" to "relative")), "picker-view" to _pS(_uM("width" to "100%", "height" to 264)), "picker-item" to _pS(_uM("display" to "flex", "justifyContent" to "center", "alignItems" to "center", "height" to 44, "overflow" to "hidden")), "picker-text" to _pS(_uM("fontSize" to 16, "color" to "#333333")), "current-selection" to _pS(_uM("paddingTop" to "20rpx", "paddingRight" to "20rpx", "paddingBottom" to "20rpx", "paddingLeft" to "20rpx", "backgroundColor" to "#f8f9fa", "display" to "flex", "flexDirection" to "row", "alignItems" to "center", "justifyContent" to "center")), "selection-label" to _pS(_uM("fontSize" to "28rpx", "color" to "#666666", "marginRight" to "10rpx")), "selection-value" to _pS(_uM("fontSize" to "32rpx", "color" to "#007aff", "fontWeight" to "bold")))
            }
        var inheritAttrs = true
        var inject: Map<String, Map<String, Any?>> = _uM()
        var emits: Map<String, Any?> = _uM("cancel" to null, "confirm" to null, "change" to null)
        var props = _nP(_uM("mode" to _uM("type" to "String", "default" to "datetime" as DateTimeMode), "title" to _uM("type" to "String", "default" to "选择时间"), "showSeconds" to _uM("type" to "Boolean", "default" to false), "startYear" to _uM("type" to "Number", "default" to fun(): Number {
            return Date().getFullYear() - 5
        }
        ), "endYear" to _uM("type" to "Number", "default" to fun(): Number {
            return Date().getFullYear() + 5
        }
        ), "quickOptions" to _uM("type" to "Array", "default" to fun(): UTSArray<QuickOption> {
            return _uA<QuickOption>()
        }
        ), "height" to _uM("type" to "Number", "default" to 264)))
        var propsNeedCastKeys = _uA(
            "mode",
            "title",
            "showSeconds",
            "startYear",
            "endYear",
            "quickOptions",
            "height"
        )
        var components: Map<String, CreateVueComponent> = _uM()
    }
}
