<template>
	<!-- 弹窗遮罩层 -->
	<view v-if="visible" class="picker-overlay" @click="onOverlayClick">
		<view class="picker-modal" @click.stop="">
			<view class="yearmonth-picker-container">
				<!-- 导航栏 -->
				<view class="navbar">
					<text class="nav-btn cancel-btn" @click="onCancel">取消</text>
					<text class="nav-title">选择年月</text>
					<view class="confirm-btn-container">
						<text class="nav-btn confirm-btn" @click="onConfirm">确定</text>
					</view>
				</view>

				<!-- 年月选择区域 -->
				<view class="picker-body">
					<!-- 年份选择区域 -->
					<view class="picker-section">
						<text class="section-title">年份</text>

						<scroll-view direction="vertical" class="year-scroll" :scroll-into-view="intoView" :scroll-top="yearScrollTop">
					
							<view class="year-list">
								<view v-for="year in yearList" :key="year" :id="'year-'+year" class="year-item"
									:class="{ 'year-active': year == selectedYear }" @click="onYearSelect(year)">
									<text class="year-text" :class="{ 'year-text-active': year == selectedYear }">{{ year }}</text>
								</view>
							</view>
						
						</scroll-view>
					</view>

					<!-- 月份选择区域 -->
					<view class="picker-section picker-section-last">
						<text class="section-title">月份</text>
					
						<scroll-view direction="vertical" class="month-scroll">
					
							<view class="month-grid">
								<view v-for="month in monthList" :key="month" class="month-item"
									:class="{ 'month-active': month == selectedMonth }" @click="onMonthSelect(month)">
									<text class="month-text" :class="{ 'month-text-active': month == selectedMonth }">{{ month }}月</text>
								</view>
							</view>
					
						</scroll-view>
					</view>
				</view>

				<!-- 当前选择显示区域 -->
				<view class="current-selection">
					<text class="selection-label">当前选择：</text>
					<text class="selection-value">{{ selectedYear }}年{{ selectedMonth }}月</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	// 定义年月选择器的数据类型
	type YearMonthData = {
		year : number,
		month : number
	}

	type YearRange = {
		before : number,
		after : number
	}

	export default {
		name: "main-yearmonth-picker",
		emits: ['cancel', 'confirm'],
		props: {
			// 初始年份
			initialYear: {
				type: Number,
				default: () => new Date().getFullYear()
			},
			// 初始月份
			initialMonth: {
				type: Number,
				default: () => new Date().getMonth() + 1
			},
			before: {
				type: Number,
				default: () => 50
			},
			after: {
				type: Number,
				default: () => 10

			}
		},
		data() {
			return {
				// 控制弹窗显示
				visible: false as boolean,
				// 当前选中的年份
				selectedYear: new Date().getFullYear() as number,
				// 当前选中的月份
				selectedMonth: (new Date().getMonth() + 1) as number,
				// 年份列表
				yearList: [] as number[],
				// 月份列表
				monthList: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12] as number[],
				// 年份滚动位置
				yearScrollTop: 0 as number,
				intoView:"" as string
			}
		},
		created() {
			this.initializeData()
		},
		methods: {
			// 初始化数据
			initializeData() {
				this.selectedYear = this.initialYear
				this.selectedMonth = this.initialMonth
				this.generateYearList()
			},

			// 生成年份列表
			generateYearList() {
				const currentYear : number = new Date().getFullYear()


				const startYear : number = currentYear - this.before
				const endYear : number = currentYear + this.after

				this.yearList = []
				for (let year = startYear; year <= endYear; year++) {
					this.yearList.push(year)
				}
			},

			// 年份选择事件
			onYearSelect(year : number) {
				this.selectedYear = year
			},

			// 月份选择事件
			onMonthSelect(month : number) {
				this.selectedMonth = month
			},

			// 打开弹窗
			open() {
				this.visible = true
				this.calculateYearScrollPosition()
			},

			// 关闭弹窗
			close() {
				this.visible = false
			},

			// 计算年份滚动位置
			calculateYearScrollPosition() {
				// 在下一个tick中计算滚动位置，确保DOM已更新
				this.$nextTick(() => {
					this.intoView= "year-" + this.selectedYear.toString()
				})
			},

			// 点击遮罩层关闭弹窗
			onOverlayClick() {
				this.close()
				this.$emit('cancel')
			},

			// 取消按钮点击事件
			onCancel() {
				this.close()
				this.$emit('cancel')
			},

			// 确定按钮点击事件
			onConfirm() {
				
				const result : UTSJSONObject= {
					year: this.selectedYear,
					month: this.selectedMonth
				}
				this.$emit('confirm', result)
				this.close()
			}
		}
	}
</script>

<style>
	/* 弹窗遮罩层 */
	.picker-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 1000;
	}

	.picker-modal {
		width: 90%;
		max-width: 600rpx;
		background-color: #ffffff;
		border-radius: 20rpx;
		overflow: hidden;
		box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
	}

	.yearmonth-picker-container {
		width: 100%;
		background-color: #ffffff;
		display: flex;
		flex-direction: column;
	}

	/* 导航栏样式 */
	.navbar {
		height: 44px;
		background-color: #f8f8f8;
		border-bottom: 1px solid #e5e5e5;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		padding: 0 10px;
	}

	.nav-btn {
		font-size: 16px;
		color: #007aff;
		padding: 8px 12px;
	}

	.cancel-btn {
		color: #999999;
	}

	.confirm-btn-container {
		height: 30px;
		background-color: #007aff;
		border-radius: 8rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);
	}

	.confirm-btn {
		color: #ffffff;
		font-weight: bold;
	}

	.nav-title {
		font-size: 17px;
		color: #333333;
	}

	/* 选择器主体区域 */
	.picker-body {
		display: flex;
		flex-direction: row;
		height: 400rpx;
		border-bottom: 1px solid #f0f0f0;
	}

	.picker-section {
		flex: 1;
		display: flex;
		flex-direction: column;
		border-right: 1px solid #f0f0f0;
	}

	.picker-section-last {
		flex:2;
		border-right: none;
	}

	.section-title {
		text-align: center;
		padding: 20rpx 0;
		font-size: 28rpx;
		font-weight: bold;
		color: #333333;
		background-color: #f8f9fa;
		border-bottom: 1px solid #f0f0f0;
	}

	/* 年份选择区域 */
	.year-scroll {
		flex: 1;
		height: 340rpx;
	}

	.year-list {
		padding: 10rpx 0;
	}

	.year-item {
		height: 60rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		margin: 0 15rpx 8rpx 15rpx;
		border-radius: 8rpx;
	}

	.year-item.year-active {
		background-color: #007aff;
		transform: scale(1.05);
	}

	.year-text {
		font-size: 28rpx;
		color: #666666;
	}

	.year-text-active {
		color: #ffffff;
		font-weight: bold;
	}

	/* 月份选择区域 */
	.month-scroll {
		flex: 1;
		height: 340rpx;
	}

	.month-grid {
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;
		padding: 20rpx 15rpx;
		justify-content: space-between;
		align-content: flex-start;
		flex: 1;
	}

	.month-item {
		width: 100rpx;
		height: 60rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		background-color: #f8f9fa;
		border-radius: 10rpx;
		margin-bottom: 12rpx;
	}

	.month-item.month-active {
		background-color: #007aff;
		transform: scale(1.05);
	}

	.month-text {
		font-size: 24rpx;
		color: #666666;
	}

	.month-text-active {
		color: #ffffff;
		font-weight: bold;
	}

	/* 当前选择显示区域 */
	.current-selection {
		padding: 20rpx;
		background-color: #f8f9fa;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
	}

	.selection-label {
		font-size: 28rpx;
		color: #666666;
		margin-right: 10rpx;
	}

	.selection-value {
		font-size: 32rpx;
		color: #007aff;
		font-weight: bold;
	}
</style>