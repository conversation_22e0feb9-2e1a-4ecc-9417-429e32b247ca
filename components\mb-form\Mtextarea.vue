<template>
	
	<view class="Mtextarea-body">
		<view class="Mtextarea" >
			<view class="Mtextarea-name" :style="{color:color}" v-if="data.name!=''">
				{{data.name}}
			</view>
		
		
			<view class="Mtextarea-box qShadow1" :style="{backgroundColor:bgColor,borderColor:showErr?'red':'#fff',height:data.size=='mini'?'200rpx':'300rpx'}">
				<textarea v-model="word"  :maxlength="data.max" :placeholder="data.placeholder" :type="data.inputType" @input="change" @blur="blurfun" />
				
			
			</view>
		</view>
		
		<view class="Mtextarea-tip" v-if="showErr">
			{{tip}}
		</view>
	</view>
</template>

<script>
	export default {
		name: "Mtextarea",
		props: {
			data: {
				type: Object,
				default: {}
			},
			keyName: {
				type: String,
				default: ""
			
			},
			index:{
				type: Number, 
				default:0
			},
			color:{
				type: String, 
				default: '#000'
			},
			bgColor: {
				type: String,
				default: "#f1f4f9"
			
			}
		},
		data() {
			return {
				word:"",
				showErr:false,
				tip:"",
				saveKey:""
			};
		},
		watch:{
			data: {
				
				handler(newValue, oldValue) {
					
					
					this.word=this.data.value
					
				},
				deep: true
			}
			
		},
		created() {
			if(this.data.isSave){
				
				this.saveKey=this.keyName+"_"+this.data.key
				
				uni.getStorage({
					key: this.saveKey,
					success:  (res)=> {
						this.word=res.data
						this.$emit("change",{index:this.index,value:res.data})
					},
					fail: () => {
						this.word=this.data.value
					}
				});
				
				
			}else{
				this.word=this.data.value
			}
		
		},
	
		methods: {
			blurfun(e){
				let inputValue=e.detail.value
			
				if(!this.data.allowNull && inputValue==""){
					this.showErr=true
					this.tip="输入内容不能为空"
				}else{
					this.showErr=false
				}
				
				
			
			},
			change(e){
				let value=e.detail.value
			
				
				this.$emit("change",{index:this.index,value: value})
				if(this.data.isSave){
					try {
						uni.setStorageSync(this.saveKey, value);
					} catch (e) {
						// error
					}
					
				}
			}
		}
	}
</script>

<style>
	.Mtextarea-body{
		width: 100%;
		display: flex;
		flex-direction: column;
		padding: 0 20rpx;
		margin-bottom: 20rpx;
	}
	.Mtextarea {
		width: 100%;
		display: flex;
		flex-direction: column;
		
		
		
		
	}
	.Mtextarea-name {
		width:100%;
		font-size: 32rpx;
		margin-bottom: 10rpx;
	}
	.Mfill{
		width: 100% !important;
	}
	.Mtextarea-box {
		width: 100%;
		height: 300rpx;
		border: 1rpx solid #fff;
		box-sizing: border-box;
		padding: 20rpx;
		border-radius: 20rpx;
	}
	
	.Mtextarea-box textarea {
		width: 100%;
		height: 100%;
	}
	.Mtextarea-tip{
		width: 100%;
		text-align: right;
		color: red;
		font-size: 28rpx;
		margin-top: 10rpx;
	}
</style>