{"version": 3, "sources": ["pages/index/index.uvue"], "sourcesContent": ["<template>\r\n\t<scroll-view  class=\"content\">\r\n\t\t<button @click=\"openColorPicker\">选择颜色</button>\r\n\t\t<button @click=\"openFun\">对话框</button>\r\n\t\t<button @click=\"openDateTimePicker\">测试日期时间选择器</button>\r\n\t\t<main-color-picker ref=\"colorPicker\" @cancel=\"onCancel\" @confirm=\"onConfirm\"></main-color-picker>\r\n\t\t<mainYearmonthPicker\r\n\t\t\t\t\tref=\"yearmonthPicker\"\r\n\r\n\t\t\t\t></mainYearmonthPicker>\r\n\t\t<main-datetime-picker\r\n\t\t\tref=\"datetimePicker\"\r\n\t\t\t:mode=\"dateTimeMode\"\r\n\t\t\t:title=\"dateTimeTitle\"\r\n\t\t\t:showSeconds=\"showSeconds\"\r\n\t\t\t:quickOptions=\"quickOptions\"\r\n\t\t\t@cancel=\"onDateTimeCancel\"\r\n\t\t\t@confirm=\"onDateTimeConfirm\"\r\n\t\t></main-datetime-picker>\r\n\t\t\r\n\t\t<main-form \r\n\t\t\t\t:formData=\"formConfig\"\r\n\t\t\t\ttitle=\"用户信息表单\"\r\n\t\t\t\tkeyName=\"user_form\"\r\n\t\t\t\tref=\"mainForm\"\r\n\t\t\t/>\r\n\t\t<button @click=\"viewForm\">查看表单1</button>\r\n\t</scroll-view>\r\n</template>\r\n\r\n<script>\r\n\timport { FormFieldData } from '@/components/main-form/form_type.uts'\r\n\timport mainYearmonthPicker from '@/components/main-form/tools/main-yearmonth-picker.uvue'\r\n\timport mainDatetimePicker from '@/components/main-form/tools/main-datetime-picker.uvue'\r\n\r\n\t// 定义快捷选项类型\r\n\ttype QuickOption = {\r\n\t\tlabel: string,\r\n\t\tvalue: Date | Date[],\r\n\t\tautoConfirm?: boolean\r\n\t}\r\n\r\n\texport default {\r\n\t\tcomponents:{\r\n\t\t\tmainYearmonthPicker,\r\n\t\t\tmainDatetimePicker\r\n\t\t},\r\n\t\tdata(){\r\n\t\t\treturn {\r\n\t\t\t\t// 日期时间选择器配置\r\n\t\t\t\tdateTimeMode: 'datetime' as string,\r\n\t\t\t\tdateTimeTitle: '选择日期时间' as string,\r\n\t\t\t\tshowSeconds: false as boolean,\r\n\t\t\t\tquickOptions: [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tlabel: '现在',\r\n\t\t\t\t\t\tvalue: new Date(),\r\n\t\t\t\t\t\tautoConfirm: true\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tlabel: '明天',\r\n\t\t\t\t\t\tvalue: new Date(Date.now() + 24 * 60 * 60 * 1000),\r\n\t\t\t\t\t\tautoConfirm: false\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tlabel: '下周',\r\n\t\t\t\t\t\tvalue: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),\r\n\t\t\t\t\t\tautoConfirm: false\r\n\t\t\t\t\t}\r\n\t\t\t\t] as QuickOption[],\r\n\t\t\t    formConfig: [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tkey: \"username\",\r\n\t\t\t\t\t\tname: \"用户名1\",\r\n\t\t\t\t\t\ttype: \"input\",\r\n\t\t\t\t\t\tvalue: \"\",\r\n\t\t\t\t\t\tisSave:true,\r\n\t\t\t\t\t\textra:{\r\n\t\t\t\t\t\t\tminLength: 0,\r\n\t\t\t\t\t\t\tmaxLength: 20,\r\n\t\t\t\t\t\t\tplaceholder: \"请输入用户名\",\r\n\t\t\t\t\t\t\ttip:\"123\",\r\n\t\t\t\t\t\t\tinputmode: \"digit\" \r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tkey: \"password\",\r\n\t\t\t\t\t\tname: \"密码\",\r\n\t\t\t\t\t\ttype: \"input\",\r\n\t\t\t\t\t\tvalue: \"\",\r\n\t\t\t\t\t\tisSave:false,\r\n\t\t\t\t\t\textra:{\r\n\t\t\t\t\t\t\tminLength: 6,\r\n\t\t\t\t\t\t\tmaxLength: 20,\r\n\t\t\t\t\t\t\tplaceholder: \"请输入密码\",\r\n\t\t\t\t\t\t\ttip:\"密码请自己保管好\",\r\n\t\t\t\t\t\t\tinputmode: \"number\"\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tkey: \"email\",\r\n\t\t\t\t\t\tname: \"邮箱地址\",\r\n\t\t\t\t\t\ttype: \"textarea\",\r\n\t\t\t\t\t\tvalue: \"\",\r\n\t\t\t\t\t\tisSave:true,\r\n\t\t\t\t\t\textra:{\r\n\t\t\t\t\t\t\tminLength: 6,\r\n\t\t\t\t\t\t\tmaxLength: 20,\r\n\t\t\t\t\t\t\tplaceholder: \"请输入密码\",\r\n\t\t\t\t\t\t\ttip:\"\"\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t    key: \"enable_feature\",\r\n\t\t\t\t\t    name: \"启用功能\",\r\n\t\t\t\t\t    type: \"switch\",\r\n\t\t\t\t\t    value: 1,\r\n\t\t\t\t\t    isSave: false,\r\n\t\t\t\t\t    extra: { \r\n\t\t\t\t\t        \"varType\": \"number\",\r\n\t\t\t\t\t        \"tip\": \"开启后将启用此功能\"\r\n\t\t\t\t\t    }\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t    key: \"slider\",\r\n\t\t\t\t\t    name: \"slider测试\",\r\n\t\t\t\t\t    type: \"slider\",\r\n\t\t\t\t\t    value: 10,\r\n\t\t\t\t\t    isSave: true,\r\n\t\t\t\t\t    extra: {\r\n\t\t\t\t\t        \"min\": 0,\r\n\t\t\t\t\t        \"max\": 100,\r\n\t\t\t\t\t\t\t\"step\":1\r\n\t\t\t\t\t    }\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t    key: \"numberbox\",\r\n\t\t\t\t\t    name: \"数量选择\",\r\n\t\t\t\t\t    type: \"numberbox\",\r\n\t\t\t\t\t    value: 5,\r\n\t\t\t\t\t    isSave: true,\r\n\t\t\t\t\t    extra: {\r\n\t\t\t\t\t        \"min\": 1,\r\n\t\t\t\t\t        \"max\": 50,\r\n\t\t\t\t\t\t\t\"step\": 1,\r\n\t\t\t\t\t\t\t\"unit\": \"个\",\r\n\t\t\t\t\t\t\t\"tip\": \"请选择数量\"\r\n\t\t\t\t\t    }\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t    key: \"themeColor\",\r\n\t\t\t\t\t    name: \"主题颜色\",\r\n\t\t\t\t\t    type: \"color\",\r\n\t\t\t\t\t    value: \"\",\r\n\t\t\t\t\t    isSave: false,\r\n\t\t\t\t\t    extra: {\r\n\t\t\t\t\t        \"varType\": \"hex\",\r\n\t\t\t\t\t        \"tip\": \"选择您喜欢的主题颜色\"\r\n\t\t\t\t\t    }\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t    key: \"backgroundColor\",\r\n\t\t\t\t\t    name: \"背景颜色\",\r\n\t\t\t\t\t    type: \"color\",\r\n\t\t\t\t\t    value: \"rgba(255, 0, 0, 0.8)\",\r\n\t\t\t\t\t    isSave: true,\r\n\t\t\t\t\t    extra: {\r\n\t\t\t\t\t        \"varType\": \"rgba\",\r\n\t\t\t\t\t        \"tip\": \"选择背景颜色，支持透明度\"\r\n\t\t\t\t\t    }\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t    key: \"birthYearMonth\",\r\n\t\t\t\t\t    name: \"出生年月\",\r\n\t\t\t\t\t    type: \"yearmonth\",\r\n\t\t\t\t\t    value: \"\",\r\n\t\t\t\t\t    isSave: true,\r\n\t\t\t\t\t    extra: {\r\n\t\t\t\t\t        \"tip\": \"请选择您的出生年月1\"\r\n\t\t\t\t\t    }\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t    key: \"city\",\r\n\t\t\t\t\t    name: \"所在城市\",\r\n\t\t\t\t\t    type: \"select\",\r\n\t\t\t\t\t    value: \"\",\r\n\t\t\t\t\t    isSave: true,\r\n\t\t\t\t\t    extra: {\r\n\t\t\t\t\t        \"varType\": \"string\",\r\n\t\t\t\t\t        \"placeholder\": \"请选择城市\",\r\n\t\t\t\t\t        \"tip\": \"选择您所在的城市\",\r\n\t\t\t\t\t        \"options\": [\r\n\t\t\t\t\t            {\"text\": \"北京\", \"value\": \"beijing\"},\r\n\t\t\t\t\t            {\"text\": \"上海\", \"value\": \"shanghai\"},\r\n\t\t\t\t\t            {\"text\": \"广州\", \"value\": \"guangzhou\"},\r\n\t\t\t\t\t            {\"text\": \"深圳\", \"value\": \"shenzhen\"},\r\n\t\t\t\t\t            {\"text\": \"杭州\", \"value\": \"hangzhou\"}\r\n\t\t\t\t\t        ]\r\n\t\t\t\t\t    }\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t    key: \"level\",\r\n\t\t\t\t\t    name: \"用户等级\",\r\n\t\t\t\t\t    type: \"select\",\r\n\t\t\t\t\t    value: 1,\r\n\t\t\t\t\t    isSave: true,\r\n\t\t\t\t\t    extra: {\r\n\t\t\t\t\t        \"varType\": \"int\",\r\n\t\t\t\t\t        \"placeholder\": \"请选择等级\",\r\n\t\t\t\t\t        \"tip\": \"选择您的用户等级\",\r\n\t\t\t\t\t        \"options\": [\r\n\t\t\t\t\t            {\"text\": \"初级用户\", \"value\": 1},\r\n\t\t\t\t\t            {\"text\": \"中级用户\", \"value\": 2},\r\n\t\t\t\t\t            {\"text\": \"高级用户\", \"value\": 3},\r\n\t\t\t\t\t            {\"text\": \"VIP用户\", \"value\": 4}\r\n\t\t\t\t\t        ]\r\n\t\t\t\t\t    }\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t    key: \"score\",\r\n\t\t\t\t\t    name: \"评分\",\r\n\t\t\t\t\t    type: \"select\",\r\n\t\t\t\t\t    value: 4.5,\r\n\t\t\t\t\t    isSave: true,\r\n\t\t\t\t\t    extra: {\r\n\t\t\t\t\t        \"varType\": \"float\",\r\n\t\t\t\t\t        \"placeholder\": \"请选择评分\",\r\n\t\t\t\t\t        \"tip\": \"选择您的评分\",\r\n\t\t\t\t\t        \"options\": [\r\n\t\t\t\t\t            {\"text\": \"1.0分\", \"value\": 1.0},\r\n\t\t\t\t\t            {\"text\": \"2.5分\", \"value\": 2.5},\r\n\t\t\t\t\t            {\"text\": \"3.0分\", \"value\": 3.0},\r\n\t\t\t\t\t            {\"text\": \"4.5分\", \"value\": 4.5},\r\n\t\t\t\t\t            {\"text\": \"5.0分\", \"value\": 5.0}\r\n\t\t\t\t\t        ]\r\n\t\t\t\t\t    }\r\n\t\t\t\t\t}\r\n\t\t\t\t] as FormFieldData[]\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tviewForm(){\r\n\t\t\t\t// this.formConfig[0].value=\"111\"\r\n\t\t\t\t// console.log(this.formConfig)\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl:\"/pages/calendar-test/calendar-test\"\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\topenFun() {\r\n\t\t\t\t(this.$refs['yearmonthPicker'] as ComponentPublicInstance).$callMethod('open')\r\n\t\t\t\r\n\t\t\t\t// uni.showModal({\r\n\t\t\t\t// \ttitle: \"onLoad 调用示例,请手动取消\",\r\n\t\t\t\t// \teditable: true,\r\n\t\t\t\t// \tcontent: \"Hello World\",\r\n\t\t\t\t// \tsuccess: function (res) {\r\n\t\t\t\t// \t\tif (res.confirm) {\r\n\t\t\t\t// \t\t\tconsole.log('用户点击确定')\r\n\t\t\t\t// \t\t} else if (res.cancel) {\r\n\t\t\t\t// \t\t\tconsole.log('用户点击取消')\r\n\t\t\t\t// \t\t}\r\n\t\t\t\t// \t}\r\n\t\t\t\t// })\r\n\t\t\t},\r\n\t\t\topenColorPicker() {\r\n\t\t\t\t// 使用$callMethod方式调用组件方法\r\n\t\t\t\t(this.$refs['colorPicker'] as ComponentPublicInstance).$callMethod('open')\r\n\t\t\t},\r\n\r\n\t\t\tonCancel() {\r\n\t\t\t\tconsole.log('用户取消选择')\r\n\t\t\t},\r\n\r\n\t\t\tonConfirm(result : UTSJSONObject) {\r\n\t\t\t\tconsole.log(result)\r\n\t\t\t\tconsole.log('选择的颜色:', result['color'])\r\n\t\t\t\tconsole.log('RGBA值:', result['rgba'])\r\n\t\t\t},\r\n\r\n\t\t\t// 打开日期时间选择器\r\n\t\t\topenDateTimePicker() {\r\n\t\t\t\t(this.$refs['datetimePicker'] as ComponentPublicInstance).$callMethod('show')\r\n\t\t\t},\r\n\r\n\t\t\t// 日期时间选择器取消事件\r\n\t\t\tonDateTimeCancel() {\r\n\t\t\t\tconsole.log('用户取消选择日期时间')\r\n\t\t\t},\r\n\r\n\t\t\t// 日期时间选择器确认事件\r\n\t\t\tonDateTimeConfirm(result: UTSJSONObject) {\r\n\t\t\t\tconsole.log('选择的日期时间:', result)\r\n\t\t\t\tconsole.log('格式化后的值:', result['formatted'])\r\n\t\t\t\tconsole.log('Date对象:', result['value'])\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n<style>\r\n\t.content {\r\n\t\theight: 100%;\r\n\t\tpadding: 20rpx;\r\n\t}\r\n</style>"], "names": [], "mappings": ";;;;;;;;;;;;;+BAqPQ;AA3MF;;;;;;;;;;eAzCL,IA0Bc,eAAA,IA1BA,WAAM,YAAS;YAC5B,IAA8C,UAAA,IAArC,aAAO,KAAA,eAAe,GAAE,QAAI,CAAA,EAAA;gBAAA;aAAA;YACrC,IAAqC,UAAA,IAA5B,aAAO,KAAA,OAAO,GAAE,OAAG,CAAA,EAAA;gBAAA;aAAA;YAC5B,IAAsD,UAAA,IAA7C,aAAO,KAAA,kBAAkB,GAAE,aAAS,CAAA,EAAA;gBAAA;aAAA;YAC7C,IAAiG,8BAAA,IAA9E,SAAI,eAAe,cAAQ,KAAA,QAAQ,EAAG,eAAS,KAAA,SAAS;;;;YAC3E,IAGyB,gCAAA,IAFtB,SAAI,oBAAiB,IAAA,EAAA,GAAA;YAGxB,IAQwB,iCAAA,IAPvB,SAAI,kBACH,UAAM,KAAA,YAAY,EAClB,WAAO,KAAA,aAAa,EACpB,iBAAa,KAAA,WAAW,EACxB,kBAAc,KAAA,YAAY,EAC1B,cAAQ,KAAA,gBAAgB,EACxB,eAAS,KAAA,iBAAiB;;;;;;;;YAG5B,IAKG,sBAAA,IAJA,cAAU,KAAA,UAAU,EACrB,WAAM,UACN,aAAQ,aACR,SAAI;;;YAEN,IAAwC,UAAA,IAA/B,aAAO,KAAA,QAAQ,GAAE,SAAK,CAAA,EAAA;gBAAA;aAAA;;;aAwB7B,cAA4B,MAAM;aAClC,eAA2B,MAAM;aACjC,aAAsB,OAAO;aAC7B,uBAgBK;aACF;;;mBApBH,kBAAc,WAAS,EAAA,CAAK,MAAM,EAClC,mBAAe,SAAO,EAAA,CAAK,MAAM,EACjC,iBAAa,KAAI,CAAA,EAAA,CAAK,OAAO,EAC7B,kBAAc,IAgBT,2BAdH,QAAO,MACP,QAAO,AAAI,QACX,cAAa,IAAG,gBAGhB,QAAO,MACP,QAAO,AAAI,KAAK,KAAK,GAAG,KAAK,QAAmB,GAChD,cAAa,KAAI,gBAGjB,QAAO,MACP,QAAO,AAAI,KAAK,KAAK,GAAG,KAAK,SAAuB,GACpD,cAAa,KAAI,IAGhB,gBAAY,iCAEb,MAAK,YACL,OAAM,QACN,OAAM,SACN,QAAO,IACP,SAAO,IAAI,EACX,QAAM;YACL,IAAA,oBAAW,CAAC;YACZ,IAAA,oBAAW,EAAE;YACb,IAAA,cAAa;YACb,IAAA,MAAI;YACJ,IAAA,YAAW;SACZ,iBAGA,MAAK,YACL,OAAM,MACN,OAAM,SACN,QAAO,IACP,SAAO,KAAK,EACZ,QAAM;YACL,IAAA,oBAAW,CAAC;YACZ,IAAA,oBAAW,EAAE;YACb,IAAA,cAAa;YACb,IAAA,MAAI;YACJ,IAAA,YAAW;SACZ,iBAGA,MAAK,SACL,OAAM,QACN,OAAM,YACN,QAAO,IACP,SAAO,IAAI,EACX,QAAM;YACL,IAAA,oBAAW,CAAC;YACZ,IAAA,oBAAW,EAAE;YACb,IAAA,cAAa;YACb,IAAA,MAAI;SACL,iBAIG,MAAK,kBACL,OAAM,QACN,OAAM,UACN,QAAO,CAAC,EACR,SAAQ,KAAK,EACb,QAAO;YACH,cAAW;YACX,UAAO;SACX,iBAGA,MAAK,UACL,OAAM,YACN,OAAM,UACN,QAAO,EAAE,EACT,SAAQ,IAAI,EACZ,QAAO;YACH,kBAAO,CAAC;YACR,kBAAO,GAAG;YAChB,mBAAO,CAAA;SACL,iBAGA,MAAK,aACL,OAAM,QACN,OAAM,aACN,QAAO,CAAC,EACR,SAAQ,IAAI,EACZ,QAAO;YACH,kBAAO,CAAC;YACR,kBAAO,EAAE;YACf,mBAAQ,CAAC;YACT,WAAQ;YACR,UAAO;SACL,iBAGA,MAAK,cACL,OAAM,QACN,OAAM,SACN,QAAO,IACP,SAAQ,KAAK,EACb,QAAO;YACH,cAAW;YACX,UAAO;SACX,iBAGA,MAAK,mBACL,OAAM,QACN,OAAM,SACN,QAAO,wBACP,SAAQ,IAAI,EACZ,QAAO;YACH,cAAW;YACX,UAAO;SACX,iBAGA,MAAK,kBACL,OAAM,QACN,OAAM,aACN,QAAO,IACP,SAAQ,IAAI,EACZ,QAAO;YACH,UAAO;SACX,iBAGA,MAAK,QACL,OAAM,QACN,OAAM,UACN,QAAO,IACP,SAAQ,IAAI,EACZ,QAAO;YACH,cAAW;YACX,kBAAe;YACf,UAAO;YACP,cAAW;gBACP;oBAAC,WAAQ;oBAAM,YAAS;iBAAU;gBAClC;oBAAC,WAAQ;oBAAM,YAAS;iBAAW;gBACnC;oBAAC,WAAQ;oBAAM,YAAS;iBAAY;gBACpC;oBAAC,WAAQ;oBAAM,YAAS;iBAAW;gBACnC;oBAAC,WAAQ;oBAAM,YAAS;iBAAU;aACtC;SACJ,iBAGA,MAAK,SACL,OAAM,QACN,OAAM,UACN,QAAO,CAAC,EACR,SAAQ,IAAI,EACZ,QAAO;YACH,cAAW;YACX,kBAAe;YACf,UAAO;YACP,cAAW;gBACP;oBAAC,WAAQ;oBAAQ,oBAAS,CAAC;iBAAC;gBAC5B;oBAAC,WAAQ;oBAAQ,oBAAS,CAAC;iBAAC;gBAC5B;oBAAC,WAAQ;oBAAQ,oBAAS,CAAC;iBAAC;gBAC5B;oBAAC,WAAQ;oBAAS,oBAAS,CAAC;iBAAA;aAChC;SACJ,iBAGA,MAAK,SACL,OAAM,MACN,OAAM,UACN,QAAO,GAAG,EACV,SAAQ,IAAI,EACZ,QAAO;YACH,cAAW;YACX,kBAAe;YACf,UAAO;YACP,cAAW;gBACP;oBAAC,WAAQ;oBAAQ,oBAAS,GAAG;iBAAC;gBAC9B;oBAAC,WAAQ;oBAAQ,oBAAS,GAAG;iBAAC;gBAC9B;oBAAC,WAAQ;oBAAQ,oBAAS,GAAG;iBAAC;gBAC9B;oBAAC,WAAQ;oBAAQ,oBAAS,GAAG;iBAAC;gBAC9B;oBAAC,WAAQ;oBAAQ,oBAAS,GAAG;iBAAA;aACjC;SACJ;;aAMN;aAAA,kBAAQ;QAGP,iCACC,MAAI;IAEN;aACA;aAAA,iBAAO;QACN,CAAC,IAAI,CAAC,OAAK,CAAC,kBAAiB,CAAA,EAAA,CAAK,uBAAuB,EAAE,aAAW,CAAC;IAcxE;aACA;aAAA,yBAAe;QAEd,CAAC,IAAI,CAAC,OAAK,CAAC,cAAa,CAAA,EAAA,CAAK,uBAAuB,EAAE,aAAW,CAAC;IACpE;aAEA;aAAA,kBAAQ;QACP,QAAQ,GAAG,CAAC,UAAQ;IACrB;aAEA;aAAA,iBAAU,QAAS,aAAa,EAAA;QAC/B,QAAQ,GAAG,CAAC,QAAM;QAClB,QAAQ,GAAG,CAAC,UAAU,MAAM,CAAC,QAAQ,EAAA;QACrC,QAAQ,GAAG,CAAC,UAAU,MAAM,CAAC,OAAO,EAAA;IACrC;aAGA;aAAA,4BAAkB;QACjB,CAAC,IAAI,CAAC,OAAK,CAAC,iBAAgB,CAAA,EAAA,CAAK,uBAAuB,EAAE,aAAW,CAAC;IACvE;aAGA;aAAA,0BAAgB;QACf,QAAQ,GAAG,CAAC,cAAY;IACzB;aAGA;aAAA,yBAAkB,QAAQ,aAAa,EAAA;QACtC,QAAQ,GAAG,CAAC,YAAY,QAAM;QAC9B,QAAQ,GAAG,CAAC,WAAW,MAAM,CAAC,YAAY,EAAA;QAC1C,QAAQ,GAAG,CAAC,WAAW,MAAM,CAAC,QAAQ,EAAA;IACvC;;;;;;;;;;;;;;;;;;;;AAEF"}