@file:Suppress("UNCHECKED_CAST", "USELESS_CAST", "INAPPLICABLE_JVM_NAME", "UNUSED_ANONYMOUS_PARAMETER", "NAME_SHADOWING", "UNNECESSARY_NOT_NULL_ASSERTION")
package uni.UNIC178CB1
import io.dcloud.uniapp.*
import io.dcloud.uniapp.extapi.*
import io.dcloud.uniapp.framework.*
import io.dcloud.uniapp.runtime.*
import io.dcloud.uniapp.vue.*
import io.dcloud.uniapp.vue.shared.*
import io.dcloud.unicloud.*
import io.dcloud.uts.*
import io.dcloud.uts.Map
import io.dcloud.uts.Set
import io.dcloud.uts.UTSAndroid
import io.dcloud.uniapp.extapi.getStorage as uni_getStorage
import io.dcloud.uniapp.extapi.setStorageSync as uni_setStorageSync
import io.dcloud.uniapp.extapi.showModal as uni_showModal
open class GenComponentsMainFormComponentsFormDatePicker : VueComponent {
    constructor(__ins: ComponentInternalInstance) : super(__ins) {
        onCreated(fun(): Unit {
            this.initializeValue()
        }
        , __ins)
        this.`$watch`(fun(): Any? {
            return this.data
        }
        , fun(newValue: FormFieldData6): Unit {
            this.updateSelectedDate(newValue.value)
        }
        , WatchOptions(deep = true, immediate = true))
    }
    @Suppress("UNUSED_PARAMETER", "UNUSED_VARIABLE")
    override fun `$render`(): Any? {
        val _ctx = this
        val _cache = this.`$`.renderCache
        val _component_picker = resolveComponent("picker")
        return _cE("view", _uM("class" to "form-date-picker-container"), _uA(
            _cE("view", _uM("class" to "form-date-picker-label", "style" to _nS(_uM("color" to _ctx.color))), _uA(
                _cE("text", null, _tD(_ctx.data.name), 1)
            ), 4),
            _cE("view", _uM("class" to "form-date-picker-box", "style" to _nS(_uM("backgroundColor" to _ctx.bgColor)), "onClick" to _ctx.showDatePicker), _uA(
                _cE("text", _uM("class" to _nC(_uA(
                    "form-date-picker-text",
                    _uM("placeholder" to (_ctx.selectedDate == ""))
                ))), _tD(_ctx.selectedDate || _ctx.data.tip || "请选择日期"), 3),
                _cE("text", _uM("class" to "form-date-picker-icon"), "📅")
            ), 12, _uA(
                "onClick"
            )),
            if (isTrue(_ctx.showPicker)) {
                _cV(_component_picker, _uM("key" to 0, "mode" to "date", "value" to _ctx.pickerValue, "start" to _ctx.startDate, "end" to _ctx.endDate, "onChange" to _ctx.handleDateChange), _uM("default" to withSlotCtx(fun(): UTSArray<Any> {
                    return _uA(
                        _cE("view")
                    )
                }), "_" to 1), 8, _uA(
                    "value",
                    "start",
                    "end",
                    "onChange"
                ))
            } else {
                _cC("v-if", true)
            }
        ))
    }
    open var data: FormFieldData6 by `$props`
    open var index: Number by `$props`
    open var color: String by `$props`
    open var bgColor: String by `$props`
    open var keyName: String by `$props`
    open var selectedDate: String by `$data`
    open var pickerValue: String by `$data`
    open var showPicker: Boolean by `$data`
    open var saveKey: String by `$data`
    open var startDate: String by `$data`
    open var endDate: String by `$data`
    @Suppress("USELESS_CAST")
    override fun data(): Map<String, Any?> {
        return _uM("selectedDate" to "", "pickerValue" to "", "showPicker" to false, "saveKey" to "", "startDate" to computed<String>(fun(): String {
            return this.data.startDate || "1900-01-01"
        }
        ), "endDate" to computed<String>(fun(): String {
            return this.data.endDate || "2100-12-31"
        }
        ))
    }
    open var initializeValue = ::gen_initializeValue_fn
    open fun gen_initializeValue_fn(): Unit {
        if (this.data.isSave == true && this.keyName != "") {
            this.saveKey = this.keyName + "_" + this.data.key
            uni_getStorage(GetStorageOptions(key = this.saveKey, success = fun(res): Unit {
                this.updateSelectedDate(res.data)
                this.emitChange(res.data)
            }, fail = fun(_): Unit {
                this.updateSelectedDate(this.data.value)
            }))
        } else {
            this.updateSelectedDate(this.data.value)
        }
    }
    open var updateSelectedDate = ::gen_updateSelectedDate_fn
    open fun gen_updateSelectedDate_fn(value: String): Unit {
        this.selectedDate = value || ""
        this.pickerValue = value || this.getCurrentDate()
    }
    open var getCurrentDate = ::gen_getCurrentDate_fn
    open fun gen_getCurrentDate_fn(): String {
        val now = Date()
        val year = now.getFullYear()
        val month = String(now.getMonth() + 1).padStart(2, "0")
        val day = String(now.getDate()).padStart(2, "0")
        return "" + year + "-" + month + "-" + day
    }
    open var showDatePicker = ::gen_showDatePicker_fn
    open fun gen_showDatePicker_fn(): Unit {
        this.showPicker = true
        uni_showModal(ShowModalOptions(title = this.data.name, content = "请在系统日期选择器中选择日期", showCancel = true, success = fun(res): Unit {
            if (res.confirm) {
                val currentDate = this.getCurrentDate()
                this.handleDateSelection(currentDate)
            }
            this.showPicker = false
        }
        ))
    }
    open var handleDateChange = ::gen_handleDateChange_fn
    open fun gen_handleDateChange_fn(event: DatePickerEvent): Unit {
        val selectedDate = event.detail.value
        this.handleDateSelection(selectedDate)
    }
    open var handleDateSelection = ::gen_handleDateSelection_fn
    open fun gen_handleDateSelection_fn(date: String): Unit {
        this.selectedDate = date
        this.pickerValue = date
        this.emitChange(date)
        this.saveValue(date)
    }
    open var emitChange = ::gen_emitChange_fn
    open fun gen_emitChange_fn(value: String): Unit {
        this.`$emit`("change", _uO("index" to this.index, "value" to value))
    }
    open var saveValue = ::gen_saveValue_fn
    open fun gen_saveValue_fn(value: String): Unit {
        if (this.data.isSave == true && this.saveKey != "") {
            try {
                uni_setStorageSync(this.saveKey, value)
            }
             catch (e: Throwable) {
                console.error("保存数据失败:", e, " at components/main-form/components/form-date-picker.uvue:190")
            }
        }
    }
    companion object {
        var name = "FormDatePicker"
        val styles: Map<String, Map<String, Map<String, Any>>> by lazy {
            _nCS(_uA(
                styles0
            ))
        }
        val styles0: Map<String, Map<String, Map<String, Any>>>
            get() {
                return _uM("form-date-picker-container" to _pS(_uM("width" to "100%", "display" to "flex", "flexDirection" to "column", "paddingTop" to 0, "paddingRight" to "20rpx", "paddingBottom" to 0, "paddingLeft" to "20rpx", "marginBottom" to "20rpx")), "form-date-picker-label" to _pS(_uM("width" to "100%", "marginBottom" to "10rpx", "fontSize" to "32rpx")), "form-date-picker-box" to _pS(_uM("width" to "100%", "height" to "100rpx", "borderTopWidth" to "1rpx", "borderRightWidth" to "1rpx", "borderBottomWidth" to "1rpx", "borderLeftWidth" to "1rpx", "borderTopStyle" to "solid", "borderRightStyle" to "solid", "borderBottomStyle" to "solid", "borderLeftStyle" to "solid", "borderTopColor" to "#e0e0e0", "borderRightColor" to "#e0e0e0", "borderBottomColor" to "#e0e0e0", "borderLeftColor" to "#e0e0e0", "boxSizing" to "border-box", "paddingTop" to 0, "paddingRight" to "20rpx", "paddingBottom" to 0, "paddingLeft" to "20rpx", "borderTopLeftRadius" to "20rpx", "borderTopRightRadius" to "20rpx", "borderBottomRightRadius" to "20rpx", "borderBottomLeftRadius" to "20rpx", "display" to "flex", "flexDirection" to "row", "alignItems" to "center", "justifyContent" to "space-between")), "form-date-picker-text" to _uM("" to _uM("flex" to 1, "fontSize" to "30rpx", "color" to "#333333"), ".placeholder" to _uM("color" to "#999999")), "form-date-picker-icon" to _pS(_uM("fontSize" to "32rpx", "color" to "#666666")))
            }
        var inheritAttrs = true
        var inject: Map<String, Map<String, Any?>> = _uM()
        var emits: Map<String, Any?> = _uM()
        var props = _nP(_uM("data" to _uM("type" to "Object", "default" to fun(): FormFieldData6 {
            return (FormFieldData6(key = "", name = "", type = "date", value = ""))
        }
        ), "index" to _uM("type" to "Number", "default" to 0), "color" to _uM("type" to "String", "default" to "#333333"), "bgColor" to _uM("type" to "String", "default" to "#f8f9fa"), "keyName" to _uM("type" to "String", "default" to "")))
        var propsNeedCastKeys = _uA(
            "data",
            "index",
            "color",
            "bgColor",
            "keyName"
        )
        var components: Map<String, CreateVueComponent> = _uM()
    }
}
