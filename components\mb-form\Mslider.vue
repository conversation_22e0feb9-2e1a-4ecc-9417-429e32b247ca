<template>
	<view class="Mslider">
		<view class="Mslider-name" :style="{color:color}">
			{{data.name}}
		</view>

		<view class="Mslider-box qShadow1" :style="{backgroundColor:bgColor}">
			<view class="Mslider-slider">
				<slider :value="mvalue" :min="data.min" :max="data.max" :step="data.step" @change="sliderfun" :show-value="false"
					activeColor="#3399FF" backgroundColor="#000000" block-color="#8A6DE9" block-size="20" />
			</view>
			<view class="Mslider-input">
				<input type="number" v-model="ivalue" @input="inputChange" />
			</view>

		</view>

	</view>



</template>

<script>
	export default {
		name: "Mslider",
		props: {
			data: {
				type: Object,
				default: {}
			},
			keyName: {
				type: String,
				default: ""
			
			},
			index: {
				type: Number,
				default: 0
			},
			color: {
				type: String,
				default: '#000'
			},
			bgColor: {
				type: String,
				default: "#f1f4f9"

			}
		},
		watch:{
			data: {
				
				handler(newValue, oldValue) {
					this.ivalue=this.mvalue = this.data.value
					console.log("发生变化")
				},
				deep: true
			}
			
		},
		created() {
			
			if(this.data.isSave){
				
				this.saveKey=this.keyName+"_"+this.data.key
				
				uni.getStorage({
					key: this.saveKey,
					success:  (res)=> {
						this.ivalue=this.mvalue = res.data
						this.$emit("change",{index:this.index,value:res.data})
					},
					fail: () => {
						this.ivalue=this.mvalue = this.data.value
					}
				});
				
				
			}else{
				this.ivalue=this.mvalue = this.data.value
			}
			
		
		},

		data() {
			return {
				mvalue: 0,
				ivalue: 0,
				saveKey:""
			};
		},
		methods: {
			inputChange(e) {
				let value= this.data.value
				
				
				if (Number(e.detail.value) >= this.data.max) {
					value=this.data.max
				} else if (Number(e.detail.value) <= this.min) {
					value=this.data.min
				} else {
					value=Number(e.detail.value)
				}
				this.$emit("change",{index:this.index, value:value })
				this.mvalue =value
				
				
				
				if(this.data.isSave){
					try {
						uni.setStorageSync(this.saveKey, value);
					} catch (e) {
						// error
					}
					
				}

			},
			sliderfun(e) {
				let value= this.data.value
				let detailValue=e.detail.value
				if (Number(detailValue) >= this.data.max) {
					value=this.data.max
				} else if (Number(detailValue) <= this.min) {
					value=this.data.min
				} else {
					
					if(this.data.step % 1 === 0){
						value=parseInt(detailValue)						
					}
					else{
						
						value= Number(detailValue.toFixed(1))
					}
					
				}
				
				this.$emit("change",{index:this.index, value:value })
				this.ivalue =value
				
				
				
				if(this.data.isSave){
					try {
						uni.setStorageSync(this.saveKey, value);
					} catch (e) {
						// error
					}
					
				}

			}

		}
	}
</script>

<style>
	.Mslider {
		width: 100%;
		display: flex;
		flex-direction: column;
		padding: 0 20rpx;
		margin-bottom: 20rpx;

	}

	.Mslider-name {
		width: 100%;
		font-size: 32rpx;
		margin-bottom: 10rpx;
	}

	.Mslider-box {
		width: 100%;
		height: 100rpx;
		border: 1rpx solid #fff;
		box-sizing: border-box;
		padding: 0 10rpx;
		border-radius: 20rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
	}

	.Mslider-slider {
		flex: 1;
	}

	.Mslider-input {
		width: 120rpx;
	}
	.Mslider-input input{
		width: 100%;
		height: 100%;
		text-align: center;
	}
</style>