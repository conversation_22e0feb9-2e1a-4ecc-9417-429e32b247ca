
	import { FormFieldData, FormChangeEvent } from '@/components/main-form/form_type.uts'
	import FormContainer from './form-container.uvue'

	const __sfc__ = defineComponent({
		name: "FormSwitch",
		components: {
			FormContainer
		},
		props: {
			data: {
				type: Object as PropType<FormFieldData>
			},
			index: {
				type: Number,
				default: 0
			},
			keyName: {
				type: String,
				default: ""
			},
			labelColor: {
				type: String,
				default: "#000"
			},
			backgroundColor: {
				type: String,
				default: "#f1f4f9"
			}
		},
		data() {
			return {
				fieldName: "",
				fieldValue: null as any | null,
				isSave: false,
				save_key: "",
				tip: "",
				varType: "number",
				switchValue: false,
				switchColor: "#8A6DE9",
				showError: false,
				errorMessage: ""
			}
		},
		computed: {

		},
		watch: {
			data: {
				handler(obj: FormFieldData) {
					// 只处理value的变化，当外部传入的value与当前fieldValue不同时，才更新fieldValue
					// 这避免了用户输入时的循环更新问题
					const newValue = obj.value
					if (newValue !== this.fieldValue) {
						console.log("触发改变", " at components/main-form/components/form-switch.uvue:66")
						this.fieldValue = newValue
						this.updateSwitchValue()
					}
				},
				deep: true
			}
		},
		created(): void {
			// 初始化时调用一次即可
			const fieldObj = this.$props["data"] as FormFieldData
			this.initFieldData(fieldObj)
		},
		methods: {
			// 初始化字段数据（仅在首次加载时调用）
			initFieldData(fieldObj: FormFieldData): void {
				const fieldKey = fieldObj.key
				const fieldValue = fieldObj.value

				// 设置基本信息
				this.fieldName = fieldObj.name
				this.fieldValue = fieldValue
				this.isSave = fieldObj.isSave ?? false
				this.save_key = this.keyName + "_" + fieldKey
				// 解析配置信息
				const extalJson = fieldObj.extra as UTSJSONObject
				this.tip = extalJson.getString("tip") ?? ""
				this.varType = extalJson.getString("varType") ?? "number"
				
				
				
				this.updateSwitchValue() 
				
				setTimeout(()=>{
					this.getCache()	
				},500)
				
			},

			// 更新开关值（根据 fieldValue 和 varType）
			updateSwitchValue(): void {
				
				const valueType :string=typeof this.fieldValue
				if(valueType!=this.varType){
					console.log("类型不匹配", " at components/main-form/components/form-switch.uvue:110")
					this.switchValue = false
					 if (this.varType == "number") {
						this.fieldValue = 0
					} else{
						this.fieldValue =false
					}
				}
				
				if (this.varType == "boolean") {
					// boolean 类型：直接使用布尔值
					this.switchValue = this.fieldValue as boolean
				} else if (this.varType == "number") {
					// number 类型：判断是否为 1
					this.switchValue = this.fieldValue == 1
				} else {
					// 默认按 number 处理
					this.switchValue = this.fieldValue == 1
				}
				
				
			},

			getCache(): void {
				if (this.isSave) {
					const that = this
					uni.getStorage({
						key: this.save_key,
						success: (res: GetStorageSuccess) => {
							const save_value = res.data
							if(typeof save_value === 'boolean'){
								that.convertAndSetValue(save_value)
							}
							
						
							
						}
					})
				}
			},

			setCache(cacheValue : boolean): void {
				if (this.isSave) {
					
					uni.setStorage({
						key: this.save_key,
						data: cacheValue
					})
				}
			},

			// 根据 varType 转换并设置值
			convertAndSetValue(isChecked: boolean): void {
				let cacheValue: any
				if (this.varType == "boolean") {
					// boolean 类型：直接使用布尔值
					cacheValue = isChecked
				} 
				else if (this.varType === "number") {
					cacheValue = isChecked ? 1 : 0
				} else {
					cacheValue = isChecked 
				}
				this.fieldValue=cacheValue 
				this.switchValue = isChecked as boolean
				
				
				const result={
					index: this.index,
					value: cacheValue
				} as FormChangeEvent
				
				this.$emit("change", result)
				this.setCache(isChecked)
			},


			// 处理开关变更
			handleChange(event: UniSwitchChangeEvent): void {
				console.log("触发改变", " at components/main-form/components/form-switch.uvue:189")
				const isChecked = event.detail.value as boolean
				
				this.convertAndSetValue(isChecked)
			}
		}
	})

export default __sfc__
function GenComponentsMainFormComponentsFormSwitchRender(this: InstanceType<typeof __sfc__>): any | null {
const _ctx = this
const _cache = this.$.renderCache
const _component_switch = resolveComponent("switch")
const _component_form_container = resolveComponent("form-container")

  return _cV(_component_form_container, _uM({
    label: _ctx.fieldName,
    "show-error": _ctx.showError,
    tip: _ctx.tip,
    "error-message": _ctx.errorMessage,
    "label-color": _ctx.labelColor,
    "background-color": _ctx.backgroundColor
  }), _uM({
    "input-content": withSlotCtx((): any[] => [
      _cE("view", _uM({ class: "switch-box" }), [
        _cV(_component_switch, _uM({
          class: "form-switch-element",
          color: _ctx.switchColor,
          checked: _ctx.switchValue,
          onChange: _ctx.handleChange
        }), null, 8 /* PROPS */, ["color", "checked", "onChange"])
      ])
    ]),
    _: 1 /* STABLE */
  }), 8 /* PROPS */, ["label", "show-error", "tip", "error-message", "label-color", "background-color"])
}
const GenComponentsMainFormComponentsFormSwitchStyles = [_uM([["switch-box", _pS(_uM([["width", "100%"], ["display", "flex"], ["flexDirection", "row"], ["alignItems", "center"], ["justifyContent", "flex-end"]]))], ["form-switch-element", _pS(_uM([["marginLeft", "auto"]]))]])]
