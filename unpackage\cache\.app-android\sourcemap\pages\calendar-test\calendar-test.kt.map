{"version": 3, "sources": ["pages/calendar-test/calendar-test.uvue", "App.uvue"], "sourcesContent": ["<template>\n\n\t<scroll-view class=\"container\">\n\n\t\t<view class=\"content\">\n\t\t\t<text class=\"title\">日历弹窗测试</text>\n\t\t\t\n\t\t\t<view class=\"test-section\">\n\t\t\t\t<button class=\"test-btn\" @click=\"openCalendarPicker\">打开日历选择器</button>\n\t\t\t\t\n\t\t\t\t<view class=\"result-section\">\n\t\t\t\t\t<text class=\"result-label\">选择的日期：</text>\n\t\t\t\t\t<text class=\"result-value\">{{ selectedDate  }}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 日历选择器 -->\n\t\t<main-calendar-picker \n\t\t\tref=\"calendarPicker\" \n\t\t\t:initial-date=\"initialDate\"\n\t\t\t@confirm=\"onCalendarConfirm\" \n\t\t\t@cancel=\"onCalendarCancel\">\n\t\t</main-calendar-picker>\n\n\t</scroll-view>\n\n</template>\n\n<script>\n\timport MainCalendarPicker from '@/components/main-calendar-picker.uvue'\n\n\texport default {\n\t\tname: \"calendar-test\",\n\t\tcomponents: {\n\t\t\tMainCalendarPicker\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t// 初始日期\n\t\t\t\tinitialDate: \"\" as string,\n\t\t\t\t// 选择的日期\n\t\t\t\tselectedDate: \"\" as string\n\t\t\t}\n\t\t},\n\t\tcreated() {\n\t\t\t// 设置初始日期为今天\n\t\t\tconst today = new Date()\n\t\t\tconst year = today.getFullYear()\n\t\t\tconst month = today.getMonth() + 1\n\t\t\tconst date = today.getDate()\n\t\t\tthis.initialDate = `${year}-${month < 10 ? '0' + month : month}-${date < 10 ? '0' + date : date}`\n\t\t},\n\t\tmethods: {\n\t\t\t// 打开日历选择器\n\t\t\topenCalendarPicker() {\n\t\t\t\tconst calendarPicker = this.$refs[\"calendarPicker\"] as ComponentPublicInstance\n\t\t\t\tcalendarPicker.$callMethod(\"open\")\n\t\t\t},\n\n\t\t\t// 日历选择确认\n\t\t\tonCalendarConfirm(dateData: UTSJSONObject) {\n\t\t\t\tconsole.log('选择的日期:', dateData)\n\t\t\t\tconst date = dateData.getString(\"fullDate\")\n\t\t\t\tif (date != null) {\n\t\t\t\t\tthis.selectedDate = date\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: `已选择: ${this.selectedDate}`,\n\t\t\t\t\ticon: 'success'\n\t\t\t\t})\n\t\t\t},\n\n\t\t\t// 日历选择取消\n\t\t\tonCalendarCancel() {\n\t\t\t\tconsole.log('取消选择日期')\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '取消选择',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t.container {\n\t\tflex: 1;\n\t\tbackground-color: #f5f5f5;\n\t}\n\n\t.content {\n\t\tpadding: 40rpx;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t}\n\n\t.title {\n\t\tfont-size: 48rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333333;\n\t\tmargin-bottom: 60rpx;\n\t}\n\n\t.test-section {\n\t\twidth: 100%;\n\t\tmax-width: 600rpx;\n\t\tbackground-color: #ffffff;\n\t\tborder-radius: 20rpx;\n\t\tpadding: 40rpx;\n\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\n\t}\n\n\t.test-btn {\n\t\twidth: 100%;\n\t\theight: 80rpx;\n\t\tbackground-color: #007aff;\n\t\tcolor: #ffffff;\n\t\tborder-radius: 12rpx;\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tborder: none;\n\t\tmargin-bottom: 40rpx;\n\t}\n\n\t.result-section {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tpadding: 30rpx;\n\t\tbackground-color: #f8f9fa;\n\t\tborder-radius: 12rpx;\n\t}\n\n\t.result-label {\n\t\tfont-size: 28rpx;\n\t\tcolor: #666666;\n\t\tmargin-bottom: 10rpx;\n\t}\n\n\t.result-value {\n\t\tfont-size: 36rpx;\n\t\tcolor: #007aff;\n\t\tfont-weight: bold;\n\t}\n</style>\n", null], "names": [], "mappings": ";;;;;;;;;;;;;+BA6CS;AAbH;;kBAaJ,MAAO;YAEN,IAAM,QAAQ,AAAI;YAClB,IAAM,OAAO,MAAM,WAAW;YAC9B,IAAM,QAAQ,MAAM,QAAQ,KAAK,CAAA;YACjC,IAAM,OAAO,MAAM,OAAO;YAC1B,IAAI,CAAC,WAAU,GAAI,KAAG,OAAI,MAAI,CAAA,IAAA,QAAQ,EAAC;gBAAI,MAAM;;gBAAQ;;YAAA,IAAK,MAAI,CAAA,IAAA,OAAO,EAAC;gBAAI,MAAM;;gBAAO;;YAAA;QAC5F;;;;;;;;eAlDD,IAuBc,eAAA,IAvBD,WAAM,cAAW;YAE7B,IAWO,QAAA,IAXD,WAAM,YAAS;gBACpB,IAAiC,QAAA,IAA3B,WAAM,UAAQ;gBAEpB,IAOO,QAAA,IAPD,WAAM,iBAAc;oBACzB,IAAqE,UAAA,IAA7D,WAAM,YAAY,aAAO,KAAA,kBAAkB,GAAE,WAAO,CAAA,EAAA;wBAAA;qBAAA;oBAE5D,IAGO,QAAA,IAHD,WAAM,mBAAgB;wBAC3B,IAAwC,QAAA,IAAlC,WAAM,iBAAe;wBAC3B,IAAqD,QAAA,IAA/C,WAAM,iBAAc,IAAI,KAAA,YAAY,GAAA,CAAA;;;;YAM7C,IAKuB,iCAAA,IAJtB,SAAI,kBACH,kBAAc,KAAA,WAAW,EACzB,eAAS,KAAA,iBAAiB,EAC1B,cAAQ,KAAA,gBAAgB;;;;;;;aAkBxB,aAAmB,MAAM;aAEzB,cAAoB,MAAK;;;mBAFzB,iBAAa,GAAC,EAAA,CAAK,MAAM,EAEzB,kBAAc,GAAC,EAAA,CAAK,MAAK;;aAa1B;aAAA,4BAAkB;QACjB,IAAM,iBAAiB,IAAI,CAAC,OAAK,CAAC,iBAAgB,CAAA,EAAA,CAAK;QACvD,eAAe,aAAW,CAAC;IAC5B;aAGA;aAAA,yBAAkB,UAAU,aAAa,EAAA;QACxC,QAAQ,GAAG,CAAC,UAAU,UAAQ;QAC9B,IAAM,OAAO,SAAS,SAAS,CAAC;QAChC,IAAI,QAAQ,IAAI,EAAE;YACjB,IAAI,CAAC,YAAW,GAAI;;QAGrB,+BACC,QAAO,yBAAQ,IAAI,CAAC,YAAY,EAChC,OAAM;IAER;aAGA;aAAA,0BAAgB;QACf,QAAQ,GAAG,CAAC,UAAQ;QACpB,+BACC,QAAO,QACP,OAAM;IAER;;mBAhDK;;;;;;;;;;;;;;;;;;;AAkDP"}