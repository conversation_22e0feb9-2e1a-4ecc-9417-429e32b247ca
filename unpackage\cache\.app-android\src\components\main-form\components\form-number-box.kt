@file:Suppress("UNCHECKED_CAST", "USELESS_CAST", "INAPPLICABLE_JVM_NAME", "UNUSED_ANONYMOUS_PARAMETER", "NAME_SHADOWING", "UNNECESSARY_NOT_NULL_ASSERTION")
package uni.UNIC178CB1
import io.dcloud.uniapp.*
import io.dcloud.uniapp.extapi.*
import io.dcloud.uniapp.framework.*
import io.dcloud.uniapp.runtime.*
import io.dcloud.uniapp.vue.*
import io.dcloud.uniapp.vue.shared.*
import io.dcloud.unicloud.*
import io.dcloud.uts.*
import io.dcloud.uts.Map
import io.dcloud.uts.Set
import io.dcloud.uts.UTSAndroid
import io.dcloud.uniapp.extapi.getStorage as uni_getStorage
import io.dcloud.uniapp.extapi.setStorageSync as uni_setStorageSync
open class GenComponentsMainFormComponentsFormNumberBox : VueComponent {
    constructor(__ins: ComponentInternalInstance) : super(__ins) {
        onCreated(fun(): Unit {
            this.initializeValue()
        }
        , __ins)
        this.`$watch`(fun(): Any? {
            return this.data
        }
        , fun(newValue: FormFieldData5): Unit {
            this.numberValue = Number(newValue.value || 0)
            this.displayValue = String(this.numberValue)
        }
        , WatchOptions(deep = true, immediate = true))
    }
    @Suppress("UNUSED_PARAMETER", "UNUSED_VARIABLE")
    override fun `$render`(): Any? {
        val _ctx = this
        val _cache = this.`$`.renderCache
        return _cE("view", _uM("class" to "form-number-box-container"), _uA(
            _cE("view", _uM("class" to "form-number-box-label", "style" to _nS(_uM("color" to _ctx.color))), _uA(
                _cE("text", null, _tD(_ctx.data.name), 1)
            ), 4),
            _cE("view", _uM("class" to "form-number-box-wrapper", "style" to _nS(_uM("backgroundColor" to _ctx.bgColor))), _uA(
                _cE("view", _uM("class" to "form-number-box-button", "onClick" to _ctx.decrease), _uA(
                    _cE("text", null, "-")
                ), 8, _uA(
                    "onClick"
                )),
                _cE("view", _uM("class" to "form-number-box-input"), _uA(
                    _cE("input", _uM("modelValue" to _ctx.displayValue, "onInput" to _uA(
                        fun(`$event`: UniInputEvent){
                            _ctx.displayValue = `$event`.detail.value
                        }
                        ,
                        _ctx.handleInput
                    ), "type" to "number", "onBlur" to _ctx.handleBlur, "class" to "form-number-input"), null, 40, _uA(
                        "modelValue",
                        "onInput",
                        "onBlur"
                    ))
                )),
                _cE("view", _uM("class" to "form-number-box-button form-number-box-button-right", "onClick" to _ctx.increase), _uA(
                    _cE("text", null, "+")
                ), 8, _uA(
                    "onClick"
                ))
            ), 4),
            if (isTrue(_ctx.data.min != null || _ctx.data.max != null)) {
                _cE("view", _uM("key" to 0, "class" to "form-number-box-info"), _uA(
                    _cE("text", null, "范围: " + _tD(_ctx.data.min || 0) + " - " + _tD(_ctx.data.max || 100), 1)
                ))
            } else {
                _cC("v-if", true)
            }
        ))
    }
    open var data: FormFieldData5 by `$props`
    open var index: Number by `$props`
    open var color: String by `$props`
    open var bgColor: String by `$props`
    open var keyName: String by `$props`
    open var numberValue: Number by `$data`
    open var displayValue: String by `$data`
    open var saveKey: String by `$data`
    @Suppress("USELESS_CAST")
    override fun data(): Map<String, Any?> {
        return _uM("numberValue" to 0, "displayValue" to "0", "saveKey" to "")
    }
    open var initializeValue = ::gen_initializeValue_fn
    open fun gen_initializeValue_fn(): Unit {
        if (this.data.isSave == true && this.keyName != "") {
            this.saveKey = this.keyName + "_" + this.data.key
            uni_getStorage(GetStorageOptions(key = this.saveKey, success = fun(res): Unit {
                this.numberValue = Number(res.data || 0)
                this.displayValue = String(this.numberValue)
                this.emitChange(this.numberValue)
            }, fail = fun(_): Unit {
                this.numberValue = Number(this.data.value || 0)
                this.displayValue = String(this.numberValue)
            }))
        } else {
            this.numberValue = Number(this.data.value || 0)
            this.displayValue = String(this.numberValue)
        }
    }
    open var decrease = ::gen_decrease_fn
    open fun gen_decrease_fn(): Unit {
        val step = this.data.step || 1
        val minValue = if (this.data.min != null) {
            this.data.min!!
        } else {
            -999999
        }
        val newValue = Math.max(this.numberValue - step, minValue)
        this.updateValue(newValue)
    }
    open var increase = ::gen_increase_fn
    open fun gen_increase_fn(): Unit {
        val step = this.data.step || 1
        val maxValue = if (this.data.max != null) {
            this.data.max!!
        } else {
            999999
        }
        val newValue = Math.min(this.numberValue + step, maxValue)
        this.updateValue(newValue)
    }
    open var handleInput = ::gen_handleInput_fn
    open fun gen_handleInput_fn(event: NumberInputEvent): Unit {
        val inputValue = event.detail.value
        val numValue = Number(inputValue)
        if (!isNaN(numValue)) {
            this.numberValue = numValue
            this.emitChange(numValue)
            this.saveValue(numValue)
        }
    }
    open var handleBlur = ::gen_handleBlur_fn
    open fun gen_handleBlur_fn(event: NumberInputEvent): Unit {
        val inputValue = event.detail.value
        var numValue = Number(inputValue)
        if (isNaN(numValue)) {
            numValue = this.data.min || 0
        }
        if (this.data.min != null && numValue < this.data.min!!) {
            numValue = this.data.min!!
        }
        if (this.data.max != null && numValue > this.data.max!!) {
            numValue = this.data.max!!
        }
        this.updateValue(numValue)
    }
    open var updateValue = ::gen_updateValue_fn
    open fun gen_updateValue_fn(value: Number): Unit {
        this.numberValue = value
        this.displayValue = String(value)
        this.emitChange(value)
        this.saveValue(value)
    }
    open var emitChange = ::gen_emitChange_fn
    open fun gen_emitChange_fn(value: Number): Unit {
        this.`$emit`("change", _uO("index" to this.index, "value" to value))
    }
    open var saveValue = ::gen_saveValue_fn
    open fun gen_saveValue_fn(value: Number): Unit {
        if (this.data.isSave == true && this.saveKey != "") {
            try {
                uni_setStorageSync(this.saveKey, value)
            }
             catch (e: Throwable) {
                console.error("保存数据失败:", e, " at components/main-form/components/form-number-box.uvue:198")
            }
        }
    }
    companion object {
        var name = "FormNumberBox"
        val styles: Map<String, Map<String, Map<String, Any>>> by lazy {
            _nCS(_uA(
                styles0
            ))
        }
        val styles0: Map<String, Map<String, Map<String, Any>>>
            get() {
                return _uM("form-number-box-container" to _pS(_uM("width" to "100%", "display" to "flex", "flexDirection" to "column", "paddingTop" to 0, "paddingRight" to "20rpx", "paddingBottom" to 0, "paddingLeft" to "20rpx", "marginBottom" to "20rpx")), "form-number-box-label" to _pS(_uM("width" to "100%", "marginBottom" to "10rpx", "fontSize" to "32rpx")), "form-number-box-wrapper" to _pS(_uM("width" to "100%", "height" to "100rpx", "borderTopWidth" to "1rpx", "borderRightWidth" to "1rpx", "borderBottomWidth" to "1rpx", "borderLeftWidth" to "1rpx", "borderTopStyle" to "solid", "borderRightStyle" to "solid", "borderBottomStyle" to "solid", "borderLeftStyle" to "solid", "borderTopColor" to "#e0e0e0", "borderRightColor" to "#e0e0e0", "borderBottomColor" to "#e0e0e0", "borderLeftColor" to "#e0e0e0", "borderTopLeftRadius" to "20rpx", "borderTopRightRadius" to "20rpx", "borderBottomRightRadius" to "20rpx", "borderBottomLeftRadius" to "20rpx", "display" to "flex", "flexDirection" to "row", "alignItems" to "center", "overflow" to "hidden")), "form-number-box-button" to _pS(_uM("width" to "80rpx", "height" to "100%", "backgroundColor" to "#f0f0f0", "display" to "flex", "justifyContent" to "center", "alignItems" to "center", "borderRightWidth" to "1rpx", "borderRightStyle" to "solid", "borderRightColor" to "#e0e0e0", "fontSize" to "36rpx", "fontWeight" to "bold", "color" to "#666666")), "form-number-box-button-right" to _pS(_uM("borderRightWidth" to "medium", "borderRightStyle" to "none", "borderRightColor" to "#000000", "borderLeftWidth" to "1rpx", "borderLeftStyle" to "solid", "borderLeftColor" to "#e0e0e0")), "form-number-box-input" to _pS(_uM("flex" to 1, "height" to "100%", "display" to "flex", "alignItems" to "center", "paddingTop" to 0, "paddingRight" to "20rpx", "paddingBottom" to 0, "paddingLeft" to "20rpx")), "form-number-input" to _pS(_uM("width" to "100%", "height" to "100%", "textAlign" to "center", "fontSize" to "30rpx")), "form-number-box-info" to _pS(_uM("width" to "100%", "textAlign" to "center", "marginTop" to "10rpx", "fontSize" to "26rpx", "color" to "#999999")))
            }
        var inheritAttrs = true
        var inject: Map<String, Map<String, Any?>> = _uM()
        var emits: Map<String, Any?> = _uM()
        var props = _nP(_uM("data" to _uM("type" to "Object", "default" to fun(): FormFieldData5 {
            return (FormFieldData5(key = "", name = "", type = "numberbox", value = 0, min = 0, max = 100, step = 1))
        }
        ), "index" to _uM("type" to "Number", "default" to 0), "color" to _uM("type" to "String", "default" to "#333333"), "bgColor" to _uM("type" to "String", "default" to "#f8f9fa"), "keyName" to _uM("type" to "String", "default" to "")))
        var propsNeedCastKeys = _uA(
            "data",
            "index",
            "color",
            "bgColor",
            "keyName"
        )
        var components: Map<String, CreateVueComponent> = _uM()
    }
}
