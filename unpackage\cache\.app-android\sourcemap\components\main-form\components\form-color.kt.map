{"version": 3, "sources": ["components/main-form/components/form-color.uvue", "components/main-form/components/form-input.uvue"], "sourcesContent": ["<template>\n\t<form-container :label=\"fieldName\" :show-error=\"showError\" :tip=\"tip\" :error-message=\"errorMessage\" :label-color=\"labelColor\"\n\t\t:background-color=\"backgroundColor\">\n\t\t<template #input-content>\n\t\t\t<view class=\"color-display-container\" @click=\"openColorPicker\">\n\t\t\t\t<view class=\"color-preview\" :style=\"{ backgroundColor: displayColor }\"></view>\n\t\t\t\t<text class=\"color-text\">{{ displayColor }}</text>\n\t\t\t</view>\n\t\t</template>\n\t</form-container>\n\n\t<!-- 颜色选择器 -->\n\t<main-color-picker ref=\"colorPicker\" @confirm=\"onColorConfirm\" @cancel=\"onColorCancel\"></main-color-picker>\n</template>\n\n<script lang=\"uts\">\n\timport { FormFieldData, FormChangeEvent } from '@/components/main-form/form_type.uts'\n\timport FormContainer from './form-container.uvue'\n\timport MainColorPicker from './../tools/main-color-picker.uvue'\n\n\texport default {\n\t\tname: \"FormColor\",\n\t\temits: ['change'],\n\t\tcomponents: {\n\t\t\tFormContainer,\n\t\t\tMainColorPicker\n\t\t},\n\t\tprops: {\n\t\t\tdata: {\n\t\t\t\ttype: null as any as PropType<FormFieldData>\n\t\t\t},\n\t\t\tindex: {\n\t\t\t\ttype: Number,\n\t\t\t\tdefault: 0\n\t\t\t},\n\t\t\tkeyName: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"\"\n\t\t\t},\n\t\t\tlabelColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#000\"\n\t\t\t},\n\t\t\tbackgroundColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#f1f4f9\"\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tfieldName: \"\",\n\t\t\t\tfieldValue: \"\" as string,\n\t\t\t\tisSave: false,\n\t\t\t\tsave_key: \"\",\n\t\t\t\ttip: \"\",\n\t\t\t\tvarType: \"hex\",\n\t\t\t\tdisplayColor: \"#000000\",\n\t\t\t\tshowError: false,\n\t\t\t\terrorMessage: \"\"\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\n\t\t},\n\t\twatch: {\n\t\t\tdata: {\n\t\t\t\thandler(obj: FormFieldData) {\n\t\t\t\t\t// 只处理value的变化，当外部传入的value与当前fieldValue不同时，才更新fieldValue\n\t\t\t\t\t// 这避免了用户输入时的循环更新问题\n\t\t\t\t\tconst newValue = obj.value as string \n\t\t\t\t\tif (newValue !== this.fieldValue) {\n\t\t\t\t\t\tthis.fieldValue = newValue\n\t\t\t\t\t\tthis.updateDisplayColor()\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tdeep: true\n\t\t\t}\n\t\t},\n\t\tcreated(): void {\n\t\t\t// 初始化时调用一次即可\n\t\t\tconst fieldObj = this.$props[\"data\"] as FormFieldData\n\t\t\tthis.initFieldData(fieldObj)\n\t\t},\n\t\tmethods: {\n\t\t\t// 初始化字段数据（仅在首次加载时调用）\n\t\t\tinitFieldData(fieldObj: FormFieldData): void {\n\t\t\t\tconst fieldKey = fieldObj.key\n\t\t\t\tconst fieldValue = fieldObj.value as string \n\n\t\t\t\t// 设置基本信息\n\t\t\t\tthis.fieldName = fieldObj.name\n\t\t\t\tthis.fieldValue = fieldValue\n\t\t\t\tthis.isSave = fieldObj.isSave ?? false\n\t\t\t\tthis.save_key = this.keyName + \"_\" + fieldKey\n\n\t\t\t\t// 解析配置信息\n\t\t\t\tconst extalJson = fieldObj.extra as UTSJSONObject\n\t\t\t\tthis.tip = extalJson.getString(\"tip\") ?? \"\"\n\t\t\t\tthis.varType = extalJson.getString(\"varType\") ?? \"hex\"\n\n\t\t\t\t// 更新显示颜色\n\t\t\t\tthis.updateDisplayColor()\n\n\t\t\t\t// 获取缓存\n\t\t\t\tthis.getCache()\n\t\t\t},\n\n\t\t\t// 更新显示颜色\n\t\t\tupdateDisplayColor(): void {\n\t\t\t\tif (this.fieldValue != \"\") {\n\t\t\t\t\tthis.displayColor = this.fieldValue as string\n\t\t\t\t} else {\n\t\t\t\t\t// 根据varType设置默认颜色\n\t\t\t\t\tif (this.varType == \"rgba\") {\n\t\t\t\t\t\tthis.displayColor = \"rgba(0, 0, 0, 1)\"\n\t\t\t\t\t\tthis.fieldValue = \"rgba(0, 0, 0, 1)\"\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.displayColor = \"#000000\"\n\t\t\t\t\t\tthis.fieldValue = \"#000000\"\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tgetCache(): void {\n\t\t\t\tif (this.isSave) {\n\t\t\t\t\tconst that = this\n\t\t\t\t\tuni.getStorage({\n\t\t\t\t\t\tkey: this.save_key,\n\t\t\t\t\t\tsuccess: (res: GetStorageSuccess) => {\n\t\t\t\t\t\t\tconst save_value = res.data\r\n\t\t\t\t\t\t\tif(typeof save_value === 'string'){\r\n\t\t\t\t\t\t\t\tthat.fieldValue = save_value as string\r\n\t\t\t\t\t\t\t\tthat.updateDisplayColor()\r\n\t\t\t\t\t\t\t\tconst result: FormChangeEvent = {\r\n\t\t\t\t\t\t\t\t\tindex: this.index,\r\n\t\t\t\t\t\t\t\t\tvalue: save_value\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tthis.change(result)\r\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tsetCache(): void { \n\t\t\t\tif (this.isSave && typeof this.fieldValue ===\"string\") {\n\t\t\t\t\tuni.setStorage({\n\t\t\t\t\t\tkey: this.save_key,\n\t\t\t\t\t\tdata: this.fieldValue\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tvalidate(): boolean {\n\t\t\t\t// 颜色值验证\n\t\t\t\tif (this.fieldValue == \"\") {\n\t\t\t\t\tthis.showError = true\n\t\t\t\t\tthis.errorMessage = \"请选择颜色\"\n\t\t\t\t\treturn false\n\t\t\t\t}\n\n\t\t\t\t// 根据varType验证颜色格式\n\t\t\t\tconst colorValue = this.fieldValue as string\n\t\t\t\tif (this.varType == \"hex\") {\n\t\t\t\t\tconst hexPattern = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/\n\t\t\t\t\tif (!hexPattern.test(colorValue)) {\n\t\t\t\t\t\tthis.showError = true\n\t\t\t\t\t\tthis.errorMessage = \"颜色格式不正确\"\n\t\t\t\t\t\treturn false\n\t\t\t\t\t}\n\t\t\t\t} else if (this.varType == \"rgba\") {\n\t\t\t\t\tconst rgbaPattern = /^rgba\\(\\s*\\d+\\s*,\\s*\\d+\\s*,\\s*\\d+\\s*,\\s*[\\d.]+\\s*\\)$/\n\t\t\t\t\tif (!rgbaPattern.test(colorValue)) {\n\t\t\t\t\t\tthis.showError = true\n\t\t\t\t\t\tthis.errorMessage = \"颜色格式不正确\"\n\t\t\t\t\t\treturn false\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tthis.showError = false\n\t\t\t\tthis.errorMessage = \"\"\n\t\t\t\treturn true\n\t\t\t},\n\n\t\t\tchange(event: FormChangeEvent): void {\n\t\t\t\t// 更新字段值\n\t\t\t\tthis.fieldValue = event.value as string \n\t\t\t\t// 更新显示颜色\n\t\t\t\tthis.updateDisplayColor()\n\t\t\t\t// 保存缓存\n\t\t\t\tthis.setCache()\n\t\t\t\t// 触发父组件事件\n\t\t\t\tthis.$emit('change', event)\n\t\t\t},\n\n\t\t\t// 打开颜色选择器\n\t\t\topenColorPicker(): void {\n\t\t\t\tconst colorPicker = this.$refs[\"colorPicker\"] as ComponentPublicInstance\n\t\t\t\tcolorPicker.$callMethod(\"open\")\n\t\t\t},\n\n\t\t\t// 颜色选择确认\n\t\t\tonColorConfirm(colorData: UTSJSONObject): void {\n\t\t\t\tlet selectedColor: string\n\n\t\t\t\tif (this.varType == \"rgba\") {\n\t\t\t\t\t// 使用rgba格式\n\t\t\t\t\tselectedColor = colorData.getString(\"color\") ?? \"rgba(0, 0, 0, 1)\"\n\t\t\t\t} else {\n\t\t\t\t\t// 使用hex格式\n\t\t\t\t\tselectedColor = colorData.getString(\"hex\") ?? \"#000000\"\n\t\t\t\t}\n\n\t\t\t\tconst result: FormChangeEvent = {\n\t\t\t\t\tindex: this.index,\n\t\t\t\t\tvalue: selectedColor\n\t\t\t\t}\n\t\t\t\tthis.change(result)\n\t\t\t},\n\n\t\t\t// 颜色选择取消\n\t\t\tonColorCancel(): void {\n\t\t\t\t// 取消选择，不做任何操作\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t.color-display-container {\n\t\tflex: 1;\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t\tmin-height: 60rpx;\n\t\tpadding: 10rpx;\n\t\tborder-radius: 10rpx;\n\t\tbackground-color: rgba(255, 255, 255, 0.8);\n\t}\n\n\t.color-preview {\n\t\twidth: 60rpx;\n\t\theight: 40rpx;\n\t\tborder-radius: 8rpx;\n\t\tborder: 1rpx solid #e5e5e5;\n\t\tmargin-right: 20rpx;\n\t\tbox-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);\n\t}\n\n\t.color-text {\n\t\tflex: 1;\n\t\tfont-size: 28rpx;\n\t\tcolor: #333333;\n\t\tfont-family: monospace;\n\t}\n</style>", null], "names": [], "mappings": ";;;;;;;;;;;;;+BA6GY;+BAeA;AAxGN;;kBA0DJ,OAAW,IAAG,CAAA;YAEb,IAAM,WAAW,IAAI,CAAC,QAAM,CAAC,OAAM,CAAA,EAAA;YACnC,IAAI,CAAC,aAAa,CAAC;QACpB;;;;;UAhBE,IAAQ,kBAAkB,EAAA;YAGzB,IAAM,WAAW,IAAI,KAAI,CAAA,EAAA,CAAK,MAAK;YACnC,IAAI,aAAa,IAAI,CAAC,UAAU,EAAE;gBACjC,IAAI,CAAC,UAAS,GAAI;gBAClB,IAAI,CAAC,kBAAkB;;QAEzB;uBACA,OAAM,IAAG;;;;;;;;;YA1EZ,IAQiB,2BAAA,IARA,WAAO,KAAA,SAAS,EAAG,gBAAY,KAAA,SAAS,EAAG,SAAK,KAAA,GAAG,EAAG,mBAAe,KAAA,YAAY,EAAG,iBAAa,KAAA,UAAU,EAC1H,sBAAkB,KAAA,eAAe,OACvB,mBAAa,YACvB,gBAGO,GAAA;uBAAA;oBAHP,IAGO,QAAA,IAHD,WAAM,2BAA2B,aAAO,KAAA,eAAe;wBAC5D,IAA8E,QAAA,IAAxE,WAAM,iBAAiB,WAAK,IAAE,IAAA,qBAAA,KAAA,YAAA;wBACpC,IAAkD,QAAA,IAA5C,WAAM,eAAY,IAAI,KAAA,YAAY,GAAA,CAAA;;;;;;;;;;;;;;YAM3C,IAA2G,8BAAA,IAAxF,SAAI,eAAe,eAAS,KAAA,cAAc,EAAG,cAAQ,KAAA,aAAa;;;;;;;;;;;aAsClF;aACA,YAAkB,MAAM;aACxB;aACA;aACA;aACA;aACA;aACA;aACA;;;mBARA,eAAW,IACX,gBAAY,GAAC,EAAA,CAAK,MAAM,EACxB,YAAQ,KAAK,EACb,cAAU,IACV,SAAK,IACL,aAAS,OACT,kBAAc,WACd,eAAW,KAAK,EAChB,kBAAc;;aA2Bf;aAAA,qBAAc,uBAAuB,GAAG,IAAG,CAAA;QAC1C,IAAM,WAAW,SAAS,GAAE;QAC5B,IAAM,aAAa,SAAS,KAAI,CAAA,EAAA,CAAK,MAAK;QAG1C,IAAI,CAAC,SAAQ,GAAI,SAAS,IAAG;QAC7B,IAAI,CAAC,UAAS,GAAI;QAClB,IAAI,CAAC,MAAK,GAAI,SAAS,MAAK,IAAK,KAAI;QACrC,IAAI,CAAC,QAAO,GAAI,IAAI,CAAC,OAAM,GAAI,MAAM;QAGrC,IAAM,YAAY,SAAS,KAAI,CAAA,EAAA,CAAK;QACpC,IAAI,CAAC,GAAE,GAAI,UAAU,SAAS,CAAC,UAAU;QACzC,IAAI,CAAC,OAAM,GAAI,UAAU,SAAS,CAAC,cAAc;QAGjD,IAAI,CAAC,kBAAkB;QAGvB,IAAI,CAAC,QAAQ;IACd;aAGA;aAAA,6BAAsB,IAAG,CAAA;QACxB,IAAI,IAAI,CAAC,UAAS,IAAK,IAAI;YAC1B,IAAI,CAAC,YAAW,GAAI,IAAI,CAAC,UAAS,CAAA,EAAA,CAAK,MAAK;eACtC;YAEN,IAAI,IAAI,CAAC,OAAM,IAAK,QAAQ;gBAC3B,IAAI,CAAC,YAAW,GAAI;gBACpB,IAAI,CAAC,UAAS,GAAI;mBACZ;gBACN,IAAI,CAAC,YAAW,GAAI;gBACpB,IAAI,CAAC,UAAS,GAAI;;;IAGrB;aAEA;aAAA,mBAAY,IAAG,CAAA;QACd,IAAI,IAAI,CAAC,MAAM,EAAE;YAChB,IAAM,OAAO,IAAG;YAChB,iCACC,MAAK,IAAI,CAAC,QAAQ,EAClB,UAAS,IAAC,KAAK,kBAAoB;gBAClC,IAAM,aAAa,IAAI,IAAG;gBAC1B,IAAG,oBAAO,gBAAe,UAAS;oBACjC,KAAK,UAAS,GAAI,WAAS,EAAA,CAAK,MAAK;oBACrC,KAAK,kBAAkB;oBACvB,IAAM,yBACL,QAAO,IAAI,CAAC,KAAK,EACjB,QAAO;oBAER,IAAI,CAAC,MAAM,CAAC;;YAGd;;;IAGH;aAEA;aAAA,mBAAY,IAAG,CAAA;QACd,IAAI,IAAI,CAAC,MAAK,IAAK,oBAAO,IAAI,CAAC,UAAS,MAAK,UAAU;YACtD,iCACC,MAAK,IAAI,CAAC,QAAQ,EAClB,OAAM,IAAI,CAAC,UAAS;;IAGvB;aAEA;aAAA,mBAAY,OAAM,CAAA;QAEjB,IAAI,IAAI,CAAC,UAAS,IAAK,IAAI;YAC1B,IAAI,CAAC,SAAQ,GAAI,IAAG;YACpB,IAAI,CAAC,YAAW,GAAI;YACpB,OAAO,KAAI;;QAIZ,IAAM,aAAa,IAAI,CAAC,UAAS,CAAA,EAAA,CAAK,MAAK;QAC3C,IAAI,IAAI,CAAC,OAAM,IAAK,OAAO;YAC1B,IAAM,aAAa;YACnB,IAAI,CAAC,WAAW,IAAI,CAAC,aAAa;gBACjC,IAAI,CAAC,SAAQ,GAAI,IAAG;gBACpB,IAAI,CAAC,YAAW,GAAI;gBACpB,OAAO,KAAI;;eAEN,IAAI,IAAI,CAAC,OAAM,IAAK,QAAQ;YAClC,IAAM,cAAc;YACpB,IAAI,CAAC,YAAY,IAAI,CAAC,aAAa;gBAClC,IAAI,CAAC,SAAQ,GAAI,IAAG;gBACpB,IAAI,CAAC,YAAW,GAAI;gBACpB,OAAO,KAAI;;;QAIb,IAAI,CAAC,SAAQ,GAAI,KAAI;QACrB,IAAI,CAAC,YAAW,GAAI;QACpB,OAAO,IAAG;IACX;aAEA;aAAA,cAAO,sBAAsB,GAAG,IAAG,CAAA;QAElC,IAAI,CAAC,UAAS,GAAI,MAAM,KAAI,CAAA,EAAA,CAAK,MAAK;QAEtC,IAAI,CAAC,kBAAkB;QAEvB,IAAI,CAAC,QAAQ;QAEb,IAAI,CAAC,OAAK,CAAC,UAAU;IACtB;aAGA;aAAA,0BAAmB,IAAG,CAAA;QACrB,IAAM,cAAc,IAAI,CAAC,OAAK,CAAC,cAAa,CAAA,EAAA,CAAK;QACjD,YAAY,aAAW,CAAC;IACzB;aAGA;aAAA,sBAAe,WAAW,aAAa,GAAG,IAAG,CAAA;QAC5C,IAAI,eAAe,MAAK;QAExB,IAAI,IAAI,CAAC,OAAM,IAAK,QAAQ;YAE3B,gBAAgB,UAAU,SAAS,CAAC,YAAY;eAC1C;YAEN,gBAAgB,UAAU,SAAS,CAAC,UAAU;;QAG/C,IAAM,yBACL,QAAO,IAAI,CAAC,KAAK,EACjB,QAAO;QAER,IAAI,CAAC,MAAM,CAAC;IACb;aAGA;aAAA,wBAAiB,IAAG,CAAA,CAEpB;;mBA3MK;;;;;;;;;;;;;6FAYK,CAAA,qDAIA,0DAIA,mEAIA;;;;;;;;;AAqLZ"}