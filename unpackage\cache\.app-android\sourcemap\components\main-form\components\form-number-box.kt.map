{"version": 3, "sources": ["components/main-form/components/form-number-box.uvue", "components/main-form/components/form-input.uvue"], "sourcesContent": ["<template>\n\t<view class=\"form-number-box-container\">\n\t\t<view class=\"form-number-box-label\" :style=\"{ color: color }\">\n\t\t\t<text>{{ data.name }}</text>\n\t\t</view>\n\n\t\t<view class=\"form-number-box-wrapper\" :style=\"{ backgroundColor: bgColor }\">\n\t\t\t<view class=\"form-number-box-button\" @click=\"decrease\">\n\t\t\t\t<text>-</text>\n\t\t\t</view>\n\t\t\t<view class=\"form-number-box-input\">\n\t\t\t\t<input \n\t\t\t\t\tv-model=\"displayValue\"\n\t\t\t\t\ttype=\"number\"\n\t\t\t\t\t@input=\"handleInput\"\n\t\t\t\t\t@blur=\"handleBlur\"\n\t\t\t\t\tclass=\"form-number-input\"\n\t\t\t\t/>\n\t\t\t</view>\n\t\t\t<view class=\"form-number-box-button form-number-box-button-right\" @click=\"increase\">\n\t\t\t\t<text>+</text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view class=\"form-number-box-info\" v-if=\"data.min != null || data.max != null\">\n\t\t\t<text>范围: {{ data.min || 0 }} - {{ data.max || 100 }}</text>\n\t\t</view>\n\t</view>\n</template>\n\n<script lang=\"uts\">\n\t// 表单项数据类型\n\ttype FormFieldData = {\n\t\tkey: string\n\t\tname: string\n\t\ttype: string\n\t\tvalue: any\n\t\tmin?: number\n\t\tmax?: number\n\t\tstep?: number\n\t\tisSave?: boolean\n\t}\n\n\t// 输入事件类型\n\ttype NumberInputEvent = {\n\t\tdetail: {\n\t\t\tvalue: string\n\t\t}\n\t}\n\n\texport default {\n\t\tname: \"FormNumberBox\",\n\t\tprops: {\n\t\t\tdata: {\n\t\t\t\ttype: Object as PropType<FormFieldData>,\n\t\t\t\tdefault: (): FormFieldData => ({\n\t\t\t\t\tkey: \"\",\n\t\t\t\t\tname: \"\",\n\t\t\t\t\ttype: \"numberbox\",\n\t\t\t\t\tvalue: 0,\n\t\t\t\t\tmin: 0,\n\t\t\t\t\tmax: 100,\n\t\t\t\t\tstep: 1\n\t\t\t\t})\n\t\t\t},\n\t\t\tindex: {\n\t\t\t\ttype: Number,\n\t\t\t\tdefault: 0\n\t\t\t},\n\t\t\tcolor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#333333\"\n\t\t\t},\n\t\t\tbgColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#f8f9fa\"\n\t\t\t},\n\t\t\tkeyName: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"\"\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tnumberValue: 0,\n\t\t\t\tdisplayValue: \"0\",\n\t\t\t\tsaveKey: \"\"\n\t\t\t}\n\t\t},\n\t\twatch: {\n\t\t\tdata: {\n\t\t\t\thandler(newValue: FormFieldData): void {\n\t\t\t\t\tthis.numberValue = Number(newValue.value || 0)\n\t\t\t\t\tthis.displayValue = String(this.numberValue)\n\t\t\t\t},\n\t\t\t\tdeep: true,\n\t\t\t\timmediate: true\n\t\t\t}\n\t\t},\n\t\tcreated(): void {\n\t\t\tthis.initializeValue()\n\t\t},\n\t\tmethods: {\n\t\t\t// 初始化值\n\t\t\tinitializeValue(): void {\n\t\t\t\tif (this.data.isSave == true && this.keyName != \"\") {\n\t\t\t\t\tthis.saveKey = this.keyName + \"_\" + this.data.key\n\t\t\t\t\t\n\t\t\t\t\t// 从存储中获取值\n\t\t\t\t\tuni.getStorage({\n\t\t\t\t\t\tkey: this.saveKey,\n\t\t\t\t\t\tsuccess: (res): void => {\n\t\t\t\t\t\t\tthis.numberValue = Number(res.data || 0)\n\t\t\t\t\t\t\tthis.displayValue = String(this.numberValue)\n\t\t\t\t\t\t\tthis.emitChange(this.numberValue)\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: (): void => {\n\t\t\t\t\t\t\tthis.numberValue = Number(this.data.value || 0)\n\t\t\t\t\t\t\tthis.displayValue = String(this.numberValue)\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t} else {\n\t\t\t\t\tthis.numberValue = Number(this.data.value || 0)\n\t\t\t\t\tthis.displayValue = String(this.numberValue)\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 减少数值\n\t\t\tdecrease(): void {\n\t\t\t\tconst step = this.data.step || 1\n\t\t\t\tconst minValue = this.data.min != null ? this.data.min : -999999\n\t\t\t\tconst newValue = Math.max(this.numberValue - step, minValue)\n\t\t\t\tthis.updateValue(newValue)\n\t\t\t},\n\n\t\t\t// 增加数值\n\t\t\tincrease(): void {\n\t\t\t\tconst step = this.data.step || 1\n\t\t\t\tconst maxValue = this.data.max != null ? this.data.max : 999999\n\t\t\t\tconst newValue = Math.min(this.numberValue + step, maxValue)\n\t\t\t\tthis.updateValue(newValue)\n\t\t\t},\n\n\t\t\t// 处理输入事件\n\t\t\thandleInput(event: NumberInputEvent): void {\n\t\t\t\tconst inputValue = event.detail.value\n\t\t\t\tconst numValue = Number(inputValue)\n\t\t\t\t\n\t\t\t\tif (!isNaN(numValue)) {\n\t\t\t\t\tthis.numberValue = numValue\n\t\t\t\t\tthis.emitChange(numValue)\n\t\t\t\t\tthis.saveValue(numValue)\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 处理失焦事件\n\t\t\thandleBlur(event: NumberInputEvent): void {\n\t\t\t\tconst inputValue = event.detail.value\n\t\t\t\tlet numValue = Number(inputValue)\n\t\t\t\t\n\t\t\t\tif (isNaN(numValue)) {\n\t\t\t\t\tnumValue = this.data.min || 0\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 应用范围限制\n\t\t\t\tif (this.data.min != null && numValue < this.data.min) {\n\t\t\t\t\tnumValue = this.data.min\n\t\t\t\t}\n\t\t\t\tif (this.data.max != null && numValue > this.data.max) {\n\t\t\t\t\tnumValue = this.data.max\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tthis.updateValue(numValue)\n\t\t\t},\n\n\t\t\t// 更新数值\n\t\t\tupdateValue(value: number): void {\n\t\t\t\tthis.numberValue = value\n\t\t\t\tthis.displayValue = String(value)\n\t\t\t\tthis.emitChange(value)\n\t\t\t\tthis.saveValue(value)\n\t\t\t},\n\n\t\t\t// 发送变更事件\n\t\t\temitChange(value: number): void {\n\t\t\t\tthis.$emit(\"change\", {\n\t\t\t\t\tindex: this.index,\n\t\t\t\t\tvalue: value\n\t\t\t\t})\n\t\t\t},\n\n\t\t\t// 保存值到本地存储\n\t\t\tsaveValue(value: number): void {\n\t\t\t\tif (this.data.isSave == true && this.saveKey != \"\") {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tuni.setStorageSync(this.saveKey, value)\n\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\tconsole.error(\"保存数据失败:\", e)\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t.form-number-box-container {\n\t\twidth: 100%;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tpadding: 0 20rpx;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.form-number-box-label {\n\t\twidth: 100%;\n\t\tmargin-bottom: 10rpx;\n\t\tfont-size: 32rpx;\n\t}\n\n\t.form-number-box-wrapper {\n\t\twidth: 100%;\n\t\theight: 100rpx;\n\t\tborder: 1rpx solid #e0e0e0;\n\t\tborder-radius: 20rpx;\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t\toverflow: hidden;\n\t}\n\n\t.form-number-box-button {\n\t\twidth: 80rpx;\n\t\theight: 100%;\n\t\tbackground-color: #f0f0f0;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tborder-right: 1rpx solid #e0e0e0;\n\t}\n\n\t.form-number-box-button {\n\t\tfont-size: 36rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #666666;\n\t}\n\n\t.form-number-box-button-right {\n\t\tborder-right: none;\n\t\tborder-left: 1rpx solid #e0e0e0;\n\t}\n\n\t.form-number-box-input {\n\t\tflex: 1;\n\t\theight: 100%;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tpadding: 0 20rpx;\n\t}\n\n\t.form-number-input {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\ttext-align: center;\n\t\tfont-size: 30rpx;\n\t}\n\n\t.form-number-box-info {\n\t\twidth: 100%;\n\t\ttext-align: center;\n\t\tmargin-top: 10rpx;\n\t\tfont-size: 26rpx;\n\t\tcolor: #999999;\n\t}\n</style>\n", null], "names": [], "mappings": ";;;;;;;;;;;;;+BA8GM;;AA5DA;;kBAiDJ,OAAW,IAAG,CAAA;YACb,IAAI,CAAC,eAAe;QACrB;;;;;UAVE,IAAQ,UAAU,cAAa,GAAG,IAAG,CAAA;YACpC,IAAI,CAAC,WAAU,GAAI,OAAO,SAAS,KAAI,IAAK,CAAC;YAC7C,IAAI,CAAC,YAAW,GAAI,OAAO,IAAI,CAAC,WAAW;QAC5C;uBACA,OAAM,IAAI,EACV,YAAW,IAAG;;;;;;eA/FjB,IA0BO,QAAA,IA1BD,WAAM,8BAA2B;YACtC,IAEO,QAAA,IAFD,WAAM,yBAAyB,WAAK,IAAE,IAAA,WAAA,KAAA,KAAA;gBAC3C,IAA4B,QAAA,IAAA,EAAA,IAAnB,KAAA,IAAI,CAAC,IAAI,GAAA,CAAA;;YAGnB,IAgBO,QAAA,IAhBD,WAAM,2BAA2B,WAAK,IAAE,IAAA,qBAAA,KAAA,OAAA;gBAC7C,IAEO,QAAA,IAFD,WAAM,0BAA0B,aAAO,KAAA,QAAQ;oBACpD,IAAc,QAAA,IAAA,EAAR;;;;gBAEP,IAQO,QAAA,IARD,WAAM,0BAAuB;oBAClC,IAME,SAAA,oBALQ,KAAA,YAAY;;4BAAZ,KAAA,YAAY,GAAA,SAAA,MAAA,CAAA,KAAA;wBAAA;;wBAEb,KAAA,WAAW;qBAAA,EADnB,UAAK,UAEJ,YAAM,KAAA,UAAU,EACjB,WAAM;;;;;;gBAGR,IAEO,QAAA,IAFD,WAAM,uDAAuD,aAAO,KAAA,QAAQ;oBACjF,IAAc,QAAA,IAAA,EAAR;;;;;uBAIiC,KAAA,IAAI,CAAC,GAAG,IAAA,IAAA,IAAY,KAAA,IAAI,CAAC,GAAG,IAAA,IAAA;gBAArE,IAEO,QAAA,gBAFD,WAAM;oBACX,IAA4D,QAAA,IAAA,EAAtD,SAAI,IAAG,KAAA,IAAI,CAAC,GAAG,IAAA,CAAA,IAAQ,QAAG,IAAG,KAAA,IAAI,CAAC,GAAG,IAAA,GAAA,GAAA,CAAA;;;;;;;mBA6BjB;;;;;aA8BzB;aACA;aACA;;;mBAFA,iBAAa,CAAC,EACd,kBAAc,KACd,aAAS;;aAkBV;aAAA,0BAAmB,IAAG,CAAA;QACrB,IAAI,IAAI,CAAC,IAAI,CAAC,MAAK,IAAK,IAAG,IAAK,IAAI,CAAC,OAAM,IAAK,IAAI;YACnD,IAAI,CAAC,OAAM,GAAI,IAAI,CAAC,OAAM,GAAI,MAAM,IAAI,CAAC,IAAI,CAAC,GAAE;YAGhD,iCACC,MAAK,IAAI,CAAC,OAAO,EACjB,UAAS,IAAC,MAAM,IAAG,CAAG;gBACrB,IAAI,CAAC,WAAU,GAAI,OAAO,IAAI,IAAG,IAAK,CAAC;gBACvC,IAAI,CAAC,YAAW,GAAI,OAAO,IAAI,CAAC,WAAW;gBAC3C,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW;YACjC,GACA,OAAM,QAAI,IAAG,CAAG;gBACf,IAAI,CAAC,WAAU,GAAI,OAAO,IAAI,CAAC,IAAI,CAAC,KAAI,IAAK,CAAC;gBAC9C,IAAI,CAAC,YAAW,GAAI,OAAO,IAAI,CAAC,WAAW;YAC5C;eAEK;YACN,IAAI,CAAC,WAAU,GAAI,OAAO,IAAI,CAAC,IAAI,CAAC,KAAI,IAAK,CAAC;YAC9C,IAAI,CAAC,YAAW,GAAI,OAAO,IAAI,CAAC,WAAW;;IAE7C;aAGA;aAAA,mBAAY,IAAG,CAAA;QACd,IAAM,OAAO,IAAI,CAAC,IAAI,CAAC,IAAG,IAAK,CAAA;QAC/B,IAAM,WAAW,IAAA,IAAI,CAAC,IAAI,CAAC,GAAE,IAAK,IAAG;YAAI,IAAI,CAAC,IAAI,CAAC,GAAE;;YAAI,CAAC,MAAK;;QAC/D,IAAM,WAAW,KAAK,GAAG,CAAC,IAAI,CAAC,WAAU,GAAI,MAAM;QACnD,IAAI,CAAC,WAAW,CAAC;IAClB;aAGA;aAAA,mBAAY,IAAG,CAAA;QACd,IAAM,OAAO,IAAI,CAAC,IAAI,CAAC,IAAG,IAAK,CAAA;QAC/B,IAAM,WAAW,IAAA,IAAI,CAAC,IAAI,CAAC,GAAE,IAAK,IAAG;YAAI,IAAI,CAAC,IAAI,CAAC,GAAE;;AAAI,kBAAK;;QAC9D,IAAM,WAAW,KAAK,GAAG,CAAC,IAAI,CAAC,WAAU,GAAI,MAAM;QACnD,IAAI,CAAC,WAAW,CAAC;IAClB;aAGA;aAAA,mBAAY,OAAO,gBAAgB,GAAG,IAAG,CAAA;QACxC,IAAM,aAAa,MAAM,MAAM,CAAC,KAAI;QACpC,IAAM,WAAW,OAAO;QAExB,IAAI,CAAC,MAAM,WAAW;YACrB,IAAI,CAAC,WAAU,GAAI;YACnB,IAAI,CAAC,UAAU,CAAC;YAChB,IAAI,CAAC,SAAS,CAAC;;IAEjB;aAGA;aAAA,kBAAW,OAAO,gBAAgB,GAAG,IAAG,CAAA;QACvC,IAAM,aAAa,MAAM,MAAM,CAAC,KAAI;QACpC,IAAI,WAAW,OAAO;QAEtB,IAAI,MAAM,WAAW;YACpB,WAAW,IAAI,CAAC,IAAI,CAAC,GAAE,IAAK,CAAA;;QAI7B,IAAI,IAAI,CAAC,IAAI,CAAC,GAAE,IAAK,IAAG,IAAK,WAAW,IAAI,CAAC,IAAI,CAAC,GAAG,IAAE;YACtD,WAAW,IAAI,CAAC,IAAI,CAAC,GAAE;;QAExB,IAAI,IAAI,CAAC,IAAI,CAAC,GAAE,IAAK,IAAG,IAAK,WAAW,IAAI,CAAC,IAAI,CAAC,GAAG,IAAE;YACtD,WAAW,IAAI,CAAC,IAAI,CAAC,GAAE;;QAGxB,IAAI,CAAC,WAAW,CAAC;IAClB;aAGA;aAAA,mBAAY,OAAO,MAAM,GAAG,IAAG,CAAA;QAC9B,IAAI,CAAC,WAAU,GAAI;QACnB,IAAI,CAAC,YAAW,GAAI,OAAO;QAC3B,IAAI,CAAC,UAAU,CAAC;QAChB,IAAI,CAAC,SAAS,CAAC;IAChB;aAGA;aAAA,kBAAW,OAAO,MAAM,GAAG,IAAG,CAAA;QAC7B,IAAI,CAAC,OAAK,CAAC,UAAU,IACpB,WAAO,IAAI,CAAC,KAAK,EACjB,WAAO;IAET;aAGA;aAAA,iBAAU,OAAO,MAAM,GAAG,IAAG,CAAA;QAC5B,IAAI,IAAI,CAAC,IAAI,CAAC,MAAK,IAAK,IAAG,IAAK,IAAI,CAAC,OAAM,IAAK,IAAI;YACnD,IAAI;gBACH,mBAAmB,IAAI,CAAC,OAAO,EAAE;;aAChC,OAAO,cAAG;gBACX,QAAQ,KAAK,CAAC,WAAW,GAAC;;;IAG7B;;mBArJK;;;;;;;;;;;;;2EAIK,OAAI;mBAAiB,gBAC7B,MAAK,IACL,OAAM,IACN,OAAM,aACN,QAAO,CAAC,EACR,MAAK,CAAC,EACN,MAAK,GAAG,EACR,OAAM,CAAA,CACN;;2DAIQ,CAAA,mDAIA,8DAIA,8DAIA;;;;;;;;;;AA2HZ"}