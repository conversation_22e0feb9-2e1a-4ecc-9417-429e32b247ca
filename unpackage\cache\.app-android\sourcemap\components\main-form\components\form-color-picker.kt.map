{"version": 3, "sources": ["components/main-form/components/form-color-picker.uvue", "components/main-form/components/form-input.uvue"], "sourcesContent": ["<template>\n\t<view class=\"form-color-picker-container\">\n\t\t<view class=\"form-color-picker-label\" :style=\"{ color: color }\">\n\t\t\t<text>{{ data.name }}</text>\n\t\t</view>\n\n\t\t<view class=\"form-color-picker-box\" :style=\"{ backgroundColor: bgColor }\" @click=\"showColorPicker\">\n\t\t\t<view class=\"form-color-preview\" :style=\"{ backgroundColor: selectedColor || '#ffffff' }\"></view>\n\t\t\t<text class=\"form-color-picker-text\" :class=\"{ 'placeholder': selectedColor == '' }\">\n\t\t\t\t{{ selectedColor || data.tip || '请选择颜色' }}\n\t\t\t</text>\n\t\t\t<text class=\"form-color-picker-icon\">🎨</text>\n\t\t</view>\n\n\t\t<!-- 使用main-color-picker组件 -->\n\t\t<main-color-picker\n\t\t\tv-if=\"showModal\"\n\t\t\t:show=\"showModal\"\n\t\t\t:color=\"tempColor\"\n\t\t\t@confirm=\"handleColorConfirm\"\n\t\t\t@cancel=\"hideColorPicker\"\n\t\t/>\n\t</view>\n</template>\n\n<script lang=\"uts\">\n\t// 表单项数据类型\n\ttype FormFieldData = {\n\t\tkey: string\n\t\tname: string\n\t\ttype: string\n\t\tvalue: any\n\t\ttip?: string\n\t\tisSave?: boolean\n\t}\n\n\t// 输入事件类型\n\ttype ColorInputEvent = {\n\t\tdetail: {\n\t\t\tvalue: string\n\t\t}\n\t}\n\n\texport default {\n\t\tname: \"FormColorPicker\",\n\t\tprops: {\n\t\t\tdata: {\n\t\t\t\ttype: Object as PropType<FormFieldData>,\n\t\t\t\tdefault: (): FormFieldData => ({\n\t\t\t\t\tkey: \"\",\n\t\t\t\t\tname: \"\",\n\t\t\t\t\ttype: \"color\",\n\t\t\t\t\tvalue: \"\"\n\t\t\t\t})\n\t\t\t},\n\t\t\tindex: {\n\t\t\t\ttype: Number,\n\t\t\t\tdefault: 0\n\t\t\t},\n\t\t\tcolor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#333333\"\n\t\t\t},\n\t\t\tbgColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#f8f9fa\"\n\t\t\t},\n\t\t\tkeyName: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"\"\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tselectedColor: \"\",\n\t\t\t\ttempColor: \"\",\n\t\t\t\tshowModal: false,\n\t\t\t\tcustomColorInput: \"\",\n\t\t\t\tcustomColorPreview: \"#ffffff\",\n\t\t\t\tsaveKey: \"\",\n\t\t\t\tpresetColors: [\n\t\t\t\t\t\"#FF0000\", \"#FF8000\", \"#FFFF00\", \"#80FF00\", \"#00FF00\", \"#00FF80\",\n\t\t\t\t\t\"#00FFFF\", \"#0080FF\", \"#0000FF\", \"#8000FF\", \"#FF00FF\", \"#FF0080\",\n\t\t\t\t\t\"#000000\", \"#404040\", \"#808080\", \"#C0C0C0\", \"#FFFFFF\", \"#FFE4E1\",\n\t\t\t\t\t\"#FFA500\", \"#FFFF99\", \"#90EE90\", \"#87CEEB\", \"#DDA0DD\", \"#F0E68C\"\n\t\t\t\t]\n\t\t\t}\n\t\t},\n\t\twatch: {\n\t\t\tdata: {\n\t\t\t\thandler(newValue: FormFieldData): void {\n\t\t\t\t\tthis.selectedColor = newValue.value || \"\"\n\t\t\t\t\tthis.tempColor = this.selectedColor\n\t\t\t\t},\n\t\t\t\tdeep: true,\n\t\t\t\timmediate: true\n\t\t\t}\n\t\t},\n\t\tcreated(): void {\n\t\t\tthis.initializeValue()\n\t\t},\n\t\tmethods: {\n\t\t\t// 初始化值\n\t\t\tinitializeValue(): void {\n\t\t\t\tif (this.data.isSave == true && this.keyName != \"\") {\n\t\t\t\t\tthis.saveKey = this.keyName + \"_\" + this.data.key\n\t\t\t\t\t\n\t\t\t\t\t// 从存储中获取值\n\t\t\t\t\tuni.getStorage({\n\t\t\t\t\t\tkey: this.saveKey,\n\t\t\t\t\t\tsuccess: (res): void => {\n\t\t\t\t\t\t\tthis.selectedColor = res.data || \"\"\n\t\t\t\t\t\t\tthis.tempColor = this.selectedColor\n\t\t\t\t\t\t\tthis.emitChange(res.data)\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: (): void => {\n\t\t\t\t\t\t\tthis.selectedColor = this.data.value || \"\"\n\t\t\t\t\t\t\tthis.tempColor = this.selectedColor\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t} else {\n\t\t\t\t\tthis.selectedColor = this.data.value || \"\"\n\t\t\t\t\tthis.tempColor = this.selectedColor\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 显示颜色选择器\n\t\t\tshowColorPicker(): void {\n\t\t\t\tthis.tempColor = this.selectedColor\n\t\t\t\tthis.customColorInput = this.selectedColor.replace(\"#\", \"\")\n\t\t\t\tthis.customColorPreview = this.selectedColor || \"#ffffff\"\n\t\t\t\tthis.showModal = true\n\t\t\t},\n\n\t\t\t// 隐藏颜色选择器\n\t\t\thideColorPicker(): void {\n\t\t\t\tthis.showModal = false\n\t\t\t},\n\n\t\t\t// 选择预设颜色\n\t\t\tselectPresetColor(color: string): void {\n\t\t\t\tthis.tempColor = color\n\t\t\t\tthis.customColorInput = color.replace(\"#\", \"\")\n\t\t\t\tthis.customColorPreview = color\n\t\t\t},\n\n\t\t\t// 处理自定义颜色输入\n\t\t\thandleCustomColorInput(event: ColorInputEvent): void {\n\t\t\t\tconst value = event.detail.value.toUpperCase()\n\t\t\t\tthis.customColorInput = value\n\t\t\t\t\n\t\t\t\t// 验证颜色格式\n\t\t\t\tif (/^[0-9A-F]{6}$/.test(value)) {\n\t\t\t\t\tconst color = \"#\" + value\n\t\t\t\t\tthis.customColorPreview = color\n\t\t\t\t\tthis.tempColor = color\n\t\t\t\t} else if (value == \"\") {\n\t\t\t\t\tthis.customColorPreview = \"#ffffff\"\n\t\t\t\t\tthis.tempColor = \"\"\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 确认颜色选择\n\t\t\tconfirmColor(): void {\n\t\t\t\tthis.selectedColor = this.tempColor\n\t\t\t\tthis.emitChange(this.selectedColor)\n\t\t\t\tthis.saveValue(this.selectedColor)\n\t\t\t\tthis.hideColorPicker()\n\t\t\t},\n\n\t\t\t// 发送变更事件\n\t\t\temitChange(value: string): void {\n\t\t\t\tthis.$emit(\"change\", {\n\t\t\t\t\tindex: this.index,\n\t\t\t\t\tvalue: value\n\t\t\t\t})\n\t\t\t},\n\n\t\t\t// 保存值到本地存储\n\t\t\tsaveValue(value: string): void {\n\t\t\t\tif (this.data.isSave == true && this.saveKey != \"\") {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tuni.setStorageSync(this.saveKey, value)\n\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\tconsole.error(\"保存数据失败:\", e)\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t.form-color-picker-container {\n\t\twidth: 100%;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tpadding: 0 20rpx;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.form-color-picker-label {\n\t\twidth: 100%;\n\t\tmargin-bottom: 10rpx;\n\t}\n\n\t.form-color-picker-label text {\n\t\tfont-size: 32rpx;\n\t}\n\n\t.form-color-picker-box {\n\t\twidth: 100%;\n\t\theight: 100rpx;\n\t\tborder: 1rpx solid #e0e0e0;\n\t\tbox-sizing: border-box;\n\t\tpadding: 0 20rpx;\n\t\tborder-radius: 20rpx;\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t}\n\n\t.form-color-preview {\n\t\twidth: 60rpx;\n\t\theight: 60rpx;\n\t\tborder-radius: 10rpx;\n\t\tborder: 1rpx solid #e0e0e0;\n\t\tmargin-right: 20rpx;\n\t}\n\n\t.form-color-picker-text {\n\t\tflex: 1;\n\t\tfont-size: 30rpx;\n\t\tcolor: #333333;\n\t}\n\n\t.form-color-picker-text.placeholder {\n\t\tcolor: #999999;\n\t}\n\n\t.form-color-picker-icon {\n\t\tfont-size: 32rpx;\n\t\tcolor: #666666;\n\t}\n\n\t.form-color-modal {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tbackground-color: rgba(0, 0, 0, 0.5);\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: flex-end;\n\t\tz-index: 1000;\n\t}\n\n\t.form-color-popup {\n\t\twidth: 100%;\n\t\tmax-height: 80%;\n\t\tbackground-color: #ffffff;\n\t\tborder-radius: 20rpx 20rpx 0 0;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t}\n\n\t.form-color-header {\n\t\theight: 100rpx;\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tpadding: 0 30rpx;\n\t\tborder-bottom: 1rpx solid #e0e0e0;\n\t}\n\n\t.form-color-cancel,\n\t.form-color-confirm {\n\t\tfont-size: 32rpx;\n\t\tcolor: #1F6ED4;\n\t}\n\n\t.form-color-title {\n\t\tfont-size: 34rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #333333;\n\t}\n\n\t.form-color-content {\n\t\tflex: 1;\n\t\tpadding: 30rpx;\n\t\toverflow-y: auto;\n\t}\n\n\t.form-color-section-title {\n\t\tfont-size: 30rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #333333;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.form-color-preset-grid {\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\tflex-wrap: wrap;\n\t\tgap: 20rpx;\n\t\tmargin-bottom: 40rpx;\n\t}\n\n\t.form-color-preset-item {\n\t\twidth: 80rpx;\n\t\theight: 80rpx;\n\t\tborder-radius: 10rpx;\n\t\tborder: 2rpx solid #e0e0e0;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tposition: relative;\n\t}\n\n\t.form-color-preset-item.selected {\n\t\tborder-color: #1F6ED4;\n\t\tborder-width: 4rpx;\n\t}\n\n\t.form-color-check {\n\t\tcolor: #ffffff;\n\t\tfont-weight: 600;\n\t\ttext-shadow: 1rpx 1rpx 2rpx rgba(0, 0, 0, 0.5);\n\t}\n\n\t.form-color-input-box {\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t\tborder: 1rpx solid #e0e0e0;\n\t\tborder-radius: 10rpx;\n\t\tpadding: 0 20rpx;\n\t\theight: 80rpx;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.form-color-hash {\n\t\tfont-size: 30rpx;\n\t\tcolor: #666666;\n\t\tmargin-right: 10rpx;\n\t}\n\n\t.form-color-input {\n\t\tflex: 1;\n\t\theight: 100%;\n\t\tfont-size: 30rpx;\n\t}\n\n\t.form-color-custom-preview {\n\t\twidth: 100%;\n\t\theight: 80rpx;\n\t\tborder-radius: 10rpx;\n\t\tborder: 1rpx solid #e0e0e0;\n\t}\n</style>\n", null], "names": [], "mappings": ";;;;;;;;;;;;;+BAwGY;+BAtGE;AAyCR;;kBAuDJ,OAAW,IAAG,CAAA;YACb,IAAI,CAAC,eAAe;QACrB;;;;;UAVE,IAAQ,UAAU,cAAa,GAAG,IAAG,CAAA;YACpC,IAAI,CAAC,aAAY,GAAI,SAAS,KAAI,IAAK;YACvC,IAAI,CAAC,SAAQ,GAAI,IAAI,CAAC,aAAY;QACnC;uBACA,OAAM,IAAI,EACV,YAAW,IAAG;;;;;;;eA9FjB,IAqBO,QAAA,IArBD,WAAM,gCAA6B;YACxC,IAEO,QAAA,IAFD,WAAM,2BAA2B,WAAK,IAAE,IAAA,WAAA,KAAA,KAAA;gBAC7C,IAA4B,QAAA,IAAA,EAAA,IAAnB,KAAA,IAAI,CAAC,IAAI,GAAA,CAAA;;YAGnB,IAMO,QAAA,IAND,WAAM,yBAAyB,WAAK,IAAE,IAAA,qBAAA,KAAA,OAAA,IAA+B,aAAO,KAAA,eAAe;gBAChG,IAAiG,QAAA,IAA3F,WAAM,sBAAsB,WAAK,IAAE,IAAA,sBAAA,KAAA,aAAA,IAAA;gBACzC,IAEO,QAAA,IAFD,WAAK,IAAA;oBAAC;oBAAiC,IAAA,kBAAA,KAAA,aAAA,IAAA;iBAAsC,QAC/E,KAAA,aAAa,IAAI,KAAA,IAAI,CAAC,GAAG,IAAA,UAAA,CAAA;gBAE7B,IAA8C,QAAA,IAAxC,WAAM,2BAAyB;;;;uBAK/B,KAAA,SAAS;gBADhB,IAME,8BAAA,gBAJA,UAAM,KAAA,SAAS,EACf,WAAO,KAAA,SAAS,EAChB,eAAS,KAAA,kBAAkB,EAC3B,cAAQ,KAAA,eAAe;;;;;;;;;;;mBA2BE;;;;;aA2BzB;aACA;aACA;aACA;aACA;aACA;aACA;;;mBANA,mBAAe,IACf,eAAW,IACX,eAAW,KAAK,EAChB,sBAAkB,IAClB,wBAAoB,WACpB,aAAS,IACT,kBAAc;YACb;YAAW;YAAW;YAAW;YAAW;YAAW;YACvD;YAAW;YAAW;YAAW;YAAW;YAAW;YACvD;YAAW;YAAW;YAAW;YAAW;YAAW;YACvD;YAAW;YAAW;YAAW;YAAW;YAAW;SACxD;;aAkBD;aAAA,0BAAmB,IAAG,CAAA;QACrB,IAAI,IAAI,CAAC,IAAI,CAAC,MAAK,IAAK,IAAG,IAAK,IAAI,CAAC,OAAM,IAAK,IAAI;YACnD,IAAI,CAAC,OAAM,GAAI,IAAI,CAAC,OAAM,GAAI,MAAM,IAAI,CAAC,IAAI,CAAC,GAAE;YAGhD,iCACC,MAAK,IAAI,CAAC,OAAO,EACjB,UAAS,IAAC,MAAM,IAAG,CAAG;gBACrB,IAAI,CAAC,aAAY,GAAI,IAAI,IAAG,IAAK;gBACjC,IAAI,CAAC,SAAQ,GAAI,IAAI,CAAC,aAAY;gBAClC,IAAI,CAAC,UAAU,CAAC,IAAI,IAAI;YACzB,GACA,OAAM,QAAI,IAAG,CAAG;gBACf,IAAI,CAAC,aAAY,GAAI,IAAI,CAAC,IAAI,CAAC,KAAI,IAAK;gBACxC,IAAI,CAAC,SAAQ,GAAI,IAAI,CAAC,aAAY;YACnC;eAEK;YACN,IAAI,CAAC,aAAY,GAAI,IAAI,CAAC,IAAI,CAAC,KAAI,IAAK;YACxC,IAAI,CAAC,SAAQ,GAAI,IAAI,CAAC,aAAY;;IAEpC;aAGA;aAAA,0BAAmB,IAAG,CAAA;QACrB,IAAI,CAAC,SAAQ,GAAI,IAAI,CAAC,aAAY;QAClC,IAAI,CAAC,gBAAe,GAAI,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,KAAK;QACxD,IAAI,CAAC,kBAAiB,GAAI,IAAI,CAAC,aAAY,IAAK;QAChD,IAAI,CAAC,SAAQ,GAAI,IAAG;IACrB;aAGA;aAAA,0BAAmB,IAAG,CAAA;QACrB,IAAI,CAAC,SAAQ,GAAI,KAAI;IACtB;aAGA;aAAA,yBAAkB,OAAO,MAAM,GAAG,IAAG,CAAA;QACpC,IAAI,CAAC,SAAQ,GAAI;QACjB,IAAI,CAAC,gBAAe,GAAI,MAAM,OAAO,CAAC,KAAK;QAC3C,IAAI,CAAC,kBAAiB,GAAI;IAC3B;aAGA;aAAA,8BAAuB,OAAO,eAAe,GAAG,IAAG,CAAA;QAClD,IAAM,QAAQ,MAAM,MAAM,CAAC,KAAK,CAAC,WAAW;QAC5C,IAAI,CAAC,gBAAe,GAAI;QAGxB,IAAI,gCAAgB,IAAI,CAAC,QAAQ;YAChC,IAAM,QAAQ,MAAM;YACpB,IAAI,CAAC,kBAAiB,GAAI;YAC1B,IAAI,CAAC,SAAQ,GAAI;eACX,IAAI,SAAS,IAAI;YACvB,IAAI,CAAC,kBAAiB,GAAI;YAC1B,IAAI,CAAC,SAAQ,GAAI;;IAEnB;aAGA;aAAA,uBAAgB,IAAG,CAAA;QAClB,IAAI,CAAC,aAAY,GAAI,IAAI,CAAC,SAAQ;QAClC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa;QAClC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa;QACjC,IAAI,CAAC,eAAe;IACrB;aAGA;aAAA,kBAAW,OAAO,MAAM,GAAG,IAAG,CAAA;QAC7B,IAAI,CAAC,OAAK,CAAC,UAAU,IACpB,WAAO,IAAI,CAAC,KAAK,EACjB,WAAO;IAET;aAGA;aAAA,iBAAU,OAAO,MAAM,GAAG,IAAG,CAAA;QAC5B,IAAI,IAAI,CAAC,IAAI,CAAC,MAAK,IAAK,IAAG,IAAK,IAAI,CAAC,OAAM,IAAK,IAAI;YACnD,IAAI;gBACH,mBAAmB,IAAI,CAAC,OAAO,EAAE;;aAChC,OAAO,cAAG;gBACX,QAAQ,KAAK,CAAC,WAAW,GAAC;;;IAG7B;;mBA/IK;;;;;;;;;;;;;2EAIK,OAAI;mBAAiB,gBAC7B,MAAK,IACL,OAAM,IACN,OAAM,SACN,QAAO,GACP;;2DAIQ,CAAA,mDAIA,8DAIA,8DAIA;;;;;;;;;;AAwHZ"}