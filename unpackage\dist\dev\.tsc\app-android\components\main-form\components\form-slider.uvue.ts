
	import { FormFieldData, FormChangeEvent } from '@/components/main-form/form_type.uts'
	import FormContainer from './form-container.uvue'

	const __sfc__ = defineComponent({
		name: "FormSlider",
		components: {
			FormContainer
		},
		props: {
			data: {
				type: null as any as PropType<FormFieldData>
			},
			index: {
				type: Number,
				default: 0
			},
			keyName: {
				type: String,
				default: ""
			},
			labelColor: {
				type: String,
				default: "#000"
			},
			backgroundColor: {
				type: String,
				default: "#f1f4f9"
			}
		},
		data() {
			return {
				fieldName: "",
				fieldValue: 0,
				isSave: false,
				save_key: "",
				tip: "",
				minValue: 0,
				maxValue: 100,
				stepValue: 1,
				sliderValue: 0,
				inputValue: "0",
				activeColor: "#3399FF",
				sliderBackgroundColor: "#000000",
				blockColor: "#8A6DE9",
				blockSize: 20,
				showError: false,
				errorMessage: ""
			}
		},
		computed: {

		},
		watch: {
			data: {
				handler(obj: FormFieldData) {
					// 只处理value的变化，当外部传入的value与当前fieldValue不同时，才更新fieldValue
					const newValue = obj.value as number
					if (newValue !== this.fieldValue) {
						this.fieldValue = newValue
						this.updateSliderAndInput()
					}
				},
				deep: true
			}
		},
		created(): void {
			// 初始化时调用一次即可
			const fieldObj = this.$props["data"] as FormFieldData
			this.initFieldData(fieldObj)
		},
		methods: {
			// 初始化字段数据（仅在首次加载时调用）
			initFieldData(fieldObj: FormFieldData): void {
				const fieldKey = fieldObj.key
				const fieldValue = fieldObj.value as number

				// 设置基本信息
				this.fieldName = fieldObj.name
				this.fieldValue = fieldValue
				this.isSave = fieldObj.isSave ?? false
				this.save_key = this.keyName + "_" + fieldKey

				// 解析配置信息
				const extalJson = fieldObj.extra as UTSJSONObject
				this.minValue = extalJson.getNumber("min") ?? 0
				this.maxValue = extalJson.getNumber("max") ?? 100
				this.stepValue = extalJson.getNumber("step") ?? 1
				this.tip = extalJson.getString("tip") ?? ""
				this.activeColor = extalJson.getString("activeColor") ?? "#3399FF"
				this.sliderBackgroundColor = extalJson.getString("sliderBackgroundColor") ?? "#000000"
				this.blockColor = extalJson.getString("blockColor") ?? "#8A6DE9"
				this.blockSize = extalJson.getNumber("blockSize") ?? 20

				// 更新滑块和输入框的值
				this.updateSliderAndInput()

				// 获取缓存
				setTimeout(() => {
					this.getCache()
				}, 500)
			},

			// 更新滑块和输入框的值
			updateSliderAndInput(): void {
				this.sliderValue = this.fieldValue
				this.inputValue = this.fieldValue.toString()
			},

			// 验证值是否在有效范围内
			validateValue(value: number): number {
				if (value < this.minValue) {
					return this.minValue
				}
				if (value > this.maxValue) {
					return this.maxValue
				}

				// 处理步长
				if (this.stepValue % 1 == 0) {
					// 整数步长
					return Math.round(value)
				} else {
					// 小数步长，保留一位小数
					return Number.from(value.toFixed(1))
				}
			},

			getCache(): void {
				if (this.isSave) {
					const that = this
					uni.getStorage({
						key: this.save_key,
						success: (res: GetStorageSuccess) => {
							const save_value = res.data as number
							const validatedValue = that.validateValue(save_value)
							that.fieldValue = validatedValue
							that.updateSliderAndInput()
							const result: FormChangeEvent = {
								index: this.index,
								value: validatedValue
							}
							this.change(result)
						}
					})
				}
			},

			setCache(): void {
				if (this.isSave) {
					uni.setStorage({
						key: this.save_key,
						data: this.fieldValue
					})
				}
			},

			validate(): boolean {
				// 滑块组件通常不需要额外验证，因为值已经被限制在min-max范围内
				this.showError = false
				this.errorMessage = ""
				return true
			},

			change(event: FormChangeEvent): void {
				// 更新字段值
				this.fieldValue = event.value as number
				// 保存缓存
				this.setCache()
				// 触发父组件事件
				this.$emit('change', event)
			},

			onSliderChange(event: UniSliderChangeEvent): void {
				const rawValue = event.detail.value as number
				const validatedValue = this.validateValue(rawValue)

				this.inputValue = validatedValue.toString()

				const result: FormChangeEvent = {
					index: this.index,
					value: validatedValue
				}
				this.change(result)
			},

			onInputChange(event: UniInputEvent): void {
				const inputStr = event.detail.value as string
				const inputNum = parseFloat(inputStr)

				if (!isNaN(inputNum)) {
					const validatedValue = this.validateValue(inputNum)
					this.sliderValue = validatedValue

					const result: FormChangeEvent = {
						index: this.index,
						value: validatedValue
					}
					this.change(result)
				}
			},

			onInputBlur(): void {
				// 在失去焦点时进行验证和格式化
				const inputNum = parseFloat(this.inputValue)
				if (isNaN(inputNum)) {
					// 如果输入无效，恢复到当前字段值
					this.inputValue = this.fieldValue.toString()
				} else {
					// 验证并格式化输入值
					const validatedValue = this.validateValue(inputNum)
					this.inputValue = validatedValue.toString()
					this.sliderValue = validatedValue

					if (validatedValue !== this.fieldValue) {
						const result: FormChangeEvent = {
							index: this.index,
							value: validatedValue
						}
						this.change(result)
					}
				}
				this.validate()
			}
		}
	})

export default __sfc__
function GenComponentsMainFormComponentsFormSliderRender(this: InstanceType<typeof __sfc__>): any | null {
const _ctx = this
const _cache = this.$.renderCache
const _component_slider = resolveComponent("slider")
const _component_form_container = resolveComponent("form-container")

  return _cV(_component_form_container, _uM({
    label: _ctx.fieldName,
    "show-error": _ctx.showError,
    tip: _ctx.tip,
    "error-message": _ctx.errorMessage,
    "label-color": _ctx.labelColor,
    "background-color": _ctx.backgroundColor
  }), _uM({
    "input-content": withSlotCtx((): any[] => [
      _cE("view", _uM({ class: "slider-container" }), [
        _cE("view", _uM({ class: "slider-wrapper" }), [
          _cV(_component_slider, _uM({
            value: _ctx.sliderValue,
            min: _ctx.minValue,
            max: _ctx.maxValue,
            step: _ctx.stepValue,
            onChange: _ctx.onSliderChange,
            "show-value": false,
            activeColor: _ctx.activeColor,
            backgroundColor: _ctx.sliderBackgroundColor,
            "block-color": _ctx.blockColor,
            "block-size": _ctx.blockSize
          }), null, 8 /* PROPS */, ["value", "min", "max", "step", "onChange", "activeColor", "backgroundColor", "block-color", "block-size"])
        ]),
        _cE("view", _uM({ class: "input-wrapper" }), [
          _cE("input", _uM({
            class: "slider-input",
            type: "number",
            modelValue: _ctx.inputValue,
            onInput: [($event: UniInputEvent) => {(_ctx.inputValue) = $event.detail.value}, _ctx.onInputChange],
            onBlur: _ctx.onInputBlur
          }), null, 40 /* PROPS, NEED_HYDRATION */, ["modelValue", "onInput", "onBlur"])
        ])
      ])
    ]),
    _: 1 /* STABLE */
  }), 8 /* PROPS */, ["label", "show-error", "tip", "error-message", "label-color", "background-color"])
}
const GenComponentsMainFormComponentsFormSliderStyles = [_uM([["slider-container", _pS(_uM([["width", "100%"], ["display", "flex"], ["flexDirection", "row"], ["alignItems", "center"]]))], ["slider-wrapper", _pS(_uM([["flex", 1], ["marginRight", "20rpx"]]))], ["input-wrapper", _pS(_uM([["width", "120rpx"]]))], ["slider-input", _pS(_uM([["width", "100%"], ["height", "60rpx"], ["textAlign", "center"], ["borderTopWidth", "1rpx"], ["borderRightWidth", "1rpx"], ["borderBottomWidth", "1rpx"], ["borderLeftWidth", "1rpx"], ["borderTopStyle", "solid"], ["borderRightStyle", "solid"], ["borderBottomStyle", "solid"], ["borderLeftStyle", "solid"], ["borderTopColor", "#dddddd"], ["borderRightColor", "#dddddd"], ["borderBottomColor", "#dddddd"], ["borderLeftColor", "#dddddd"], ["borderTopLeftRadius", "10rpx"], ["borderTopRightRadius", "10rpx"], ["borderBottomRightRadius", "10rpx"], ["borderBottomLeftRadius", "10rpx"], ["paddingTop", 0], ["paddingRight", "10rpx"], ["paddingBottom", 0], ["paddingLeft", "10rpx"], ["boxSizing", "border-box"]]))]])]
