{"version": 3, "sources": ["components/main-form/components/form-numberbox.uvue"], "names": [], "mappings": "AA6BC,OAAO,EAAE,aAAa,EAAE,eAAc,EAAE,MAAO,sCAAqC,CAAA;AACpF,OAAO,aAAY,MAAO,uBAAsB,CAAA;AAEhD,MAAK,OAAQ,GAAE,eAAA,CAAA;IACd,IAAI,EAAE,eAAe;IACrB,KAAK,EAAE,CAAC,QAAQ,CAAC;IACjB,UAAU,EAAE;QACX,aAAY;KACZ;IACD,KAAK,EAAE;QACN,IAAI,EAAE;YACL,IAAI,EAAE,IAAG,IAAK,GAAE,IAAK,QAAQ,CAAC,aAAa,CAAA;SAC3C;QACD,KAAK,EAAE;YACN,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,CAAA;SACT;QACD,OAAO,EAAE;YACR,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,EAAC;SACV;QACD,UAAU,EAAE;YACX,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,MAAK;SACd;QACD,eAAe,EAAE;YAChB,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,SAAQ;SAClB;KACA;IACD,IAAI;QACH,OAAO;YACN,SAAS,EAAE,EAAE;YACb,UAAU,EAAE,CAAC;YACb,MAAM,EAAE,KAAK;YACb,QAAQ,EAAE,EAAE;YACZ,GAAG,EAAE,EAAE;YACP,QAAQ,EAAE,CAAC;YACX,QAAQ,EAAE,GAAG;YACb,SAAS,EAAE,CAAC;YACZ,UAAU,EAAE,GAAG;YACf,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,EAAC;SAChB,CAAA;IACD,CAAC;IACD,QAAQ,EAAE,EAET;IACD,KAAK,EAAE;QACN,IAAI,EAAE;YACL,OAAO,CAAC,GAAG,EAAE,aAAa;gBACzB,wDAAuD;gBACvD,MAAM,QAAO,GAAI,GAAG,CAAC,KAAI,IAAK,MAAK,CAAA;gBACnC,IAAI,QAAO,KAAM,IAAI,CAAC,UAAU,EAAE;oBACjC,IAAI,CAAC,UAAS,GAAI,QAAO,CAAA;oBACzB,IAAI,CAAC,gBAAgB,EAAC,CAAA;iBACvB;YACD,CAAC;YACD,IAAI,EAAE,IAAG;SACV;KACA;IACD,OAAO,IAAI,IAAG;QACb,aAAY;QACZ,MAAM,QAAO,GAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAA,IAAK,aAAY,CAAA;QACpD,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAA,CAAA;IAC5B,CAAC;IACD,OAAO,EAAE;QACR,qBAAoB;QACpB,aAAa,CAAC,QAAQ,EAAE,aAAa,GAAG,IAAG;YAC1C,MAAM,QAAO,GAAI,QAAQ,CAAC,GAAE,CAAA;YAC5B,MAAM,UAAS,GAAI,QAAQ,CAAC,KAAI,IAAK,MAAK,CAAA;YAE1C,SAAQ;YACR,IAAI,CAAC,SAAQ,GAAI,QAAQ,CAAC,IAAG,CAAA;YAC7B,IAAI,CAAC,UAAS,GAAI,UAAS,CAAA;YAC3B,IAAI,CAAC,MAAK,GAAI,QAAQ,CAAC,MAAK,IAAK,KAAI,CAAA;YACrC,IAAI,CAAC,QAAO,GAAI,IAAI,CAAC,OAAM,GAAI,GAAE,GAAI,QAAO,CAAA;YAE5C,SAAQ;YACR,MAAM,SAAQ,GAAI,QAAQ,CAAC,KAAI,IAAK,aAAY,CAAA;YAChD,IAAI,CAAC,QAAO,GAAI,SAAS,CAAC,SAAS,CAAC,KAAK,CAAA,IAAK,CAAA,CAAA;YAC9C,IAAI,CAAC,QAAO,GAAI,SAAS,CAAC,SAAS,CAAC,KAAK,CAAA,IAAK,GAAE,CAAA;YAChD,IAAI,CAAC,SAAQ,GAAI,SAAS,CAAC,SAAS,CAAC,MAAM,CAAA,IAAK,CAAA,CAAA;YAChD,IAAI,CAAC,GAAE,GAAI,SAAS,CAAC,SAAS,CAAC,KAAK,CAAA,IAAK,EAAC,CAAA;YAC1C,IAAI,CAAC,QAAO,GAAI,SAAS,CAAC,SAAS,CAAC,MAAM,CAAA,IAAK,EAAC,CAAA;YAEhD,UAAS;YACT,IAAI,CAAC,gBAAgB,EAAC,CAAA;YAEtB,OAAM;YACN,UAAU,CAAC,GAAG,EAAC;gBACd,IAAI,CAAC,QAAQ,EAAC,CAAA;YACf,CAAC,EAAE,GAAG,CAAA,CAAA;QACP,CAAC;QAED,UAAS;QACT,gBAAgB,IAAI,IAAG;YACtB,IAAI,CAAC,UAAS,GAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,IAAC,CAAA;QAC5C,CAAC;QAED,cAAa;QACb,aAAa,CAAC,KAAK,EAAE,MAAM,GAAG,MAAK;YAClC,IAAI,KAAI,GAAI,IAAI,CAAC,QAAQ,EAAE;gBAC1B,OAAO,IAAI,CAAC,QAAO,CAAA;aACpB;YACA,IAAI,KAAI,GAAI,IAAI,CAAC,QAAQ,EAAE;gBAC1B,OAAO,IAAI,CAAC,QAAO,CAAA;aACpB;YAEA,OAAM;YACN,IAAI,IAAI,CAAC,SAAQ,GAAI,CAAA,IAAK,CAAC,EAAE;gBAC5B,OAAM;gBACN,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAA,CAAA;aACxB;iBAAO;gBACN,cAAa;gBACb,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA,CAAA;aACpC;QACD,CAAC;QAED,QAAQ,IAAI,IAAG;YACd,IAAI,IAAI,CAAC,MAAM,EAAE;gBAChB,MAAM,IAAG,GAAI,IAAG,CAAA;gBAChB,GAAG,CAAC,UAAU,CAAC;oBACd,GAAG,EAAE,IAAI,CAAC,QAAQ;oBAClB,OAAO,EAAE,CAAC,GAAG,EAAE,iBAAiB,EAAE,EAAC;wBAClC,MAAM,UAAS,GAAI,GAAG,CAAC,IAAG,IAAK,MAAK,CAAA;wBACpC,MAAM,cAAa,GAAI,IAAI,CAAC,aAAa,CAAC,UAAU,CAAA,CAAA;wBACpD,IAAI,CAAC,UAAS,GAAI,cAAa,CAAA;wBAC/B,IAAI,CAAC,gBAAgB,EAAC,CAAA;wBACtB,MAAM,MAAM,EAAE,eAAc,GAAI;4BAC/B,KAAK,EAAE,IAAI,CAAC,KAAK;4BACjB,KAAK,EAAE,cAAa;yBACrB,CAAA;wBACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAA,CAAA;oBACnB,CAAA;iBACA,CAAA,CAAA;aACF;QACD,CAAC;QAED,QAAQ,IAAI,IAAG;YACd,IAAI,IAAI,CAAC,MAAM,EAAE;gBAChB,GAAG,CAAC,UAAU,CAAC;oBACd,GAAG,EAAE,IAAI,CAAC,QAAQ;oBAClB,IAAI,EAAE,IAAI,CAAC,UAAS;iBACpB,CAAA,CAAA;aACF;QACD,CAAC;QAED,QAAQ,IAAI,OAAM;YACjB,uCAAsC;YACtC,IAAI,CAAC,SAAQ,GAAI,KAAI,CAAA;YACrB,IAAI,CAAC,YAAW,GAAI,EAAC,CAAA;YACrB,OAAO,IAAG,CAAA;QACX,CAAC;QAED,MAAM,CAAC,KAAK,EAAE,eAAe,GAAG,IAAG;YAClC,QAAO;YACP,IAAI,CAAC,UAAS,GAAI,KAAK,CAAC,KAAI,IAAK,MAAK,CAAA;YACtC,OAAM;YACN,IAAI,CAAC,QAAQ,EAAC,CAAA;YACd,UAAS;YACT,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAA,CAAA;QAC3B,CAAC;QAED,SAAQ;QACR,SAAS,CAAC,IAAI,EAAE,MAAM,GAAG,IAAG;YAC3B,IAAI,KAAK,EAAE,MAAK,CAAA;YAChB,IAAI,GAAG,EAAE,MAAK,GAAI,IAAI,CAAC,UAAS,CAAA;YAEhC,IAAI,IAAG,IAAK,GAAG,EAAE;gBAChB,GAAE,GAAI,GAAE,GAAI,IAAI,CAAC,SAAQ,CAAA;aAC1B;iBAAO;gBACN,GAAE,GAAI,GAAE,GAAI,IAAI,CAAC,SAAQ,CAAA;aAC1B;YAEA,SAAQ;YACR,KAAI,GAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAA,CAAA;YAE9B,UAAS;YACT,IAAI,CAAC,UAAS,GAAI,KAAK,CAAC,QAAQ,IAAC,CAAA;YAEjC,MAAM,MAAM,EAAE,eAAc,GAAI;gBAC/B,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,KAAK,EAAE,KAAI;aACZ,CAAA;YACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAA,CAAA;QACnB,CAAC;QAED,aAAa,CAAC,KAAK,EAAE,aAAa,GAAG,IAAG;YACvC,MAAM,QAAO,GAAI,KAAK,CAAC,MAAM,CAAC,KAAI,IAAK,MAAK,CAAA;YAC5C,MAAM,QAAO,GAAI,UAAU,CAAC,QAAQ,CAAA,CAAA;YAEpC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE;gBACrB,MAAM,cAAa,GAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAA,CAAA;gBAElD,MAAM,MAAM,EAAE,eAAc,GAAI;oBAC/B,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,KAAK,EAAE,cAAa;iBACrB,CAAA;gBACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAA,CAAA;aACnB;QACD,CAAC;QAED,WAAW,IAAI,IAAG;YACjB,iBAAgB;YAChB,MAAM,QAAO,GAAI,UAAU,CAAC,IAAI,CAAC,UAAU,CAAA,CAAA;YAC3C,IAAI,KAAK,CAAC,QAAQ,CAAC,EAAE;gBACpB,kBAAiB;gBACjB,IAAI,CAAC,UAAS,GAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,IAAC,CAAA;aAC5C;iBAAO;gBACN,YAAW;gBACX,MAAM,cAAa,GAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAA,CAAA;gBAClD,IAAI,CAAC,UAAS,GAAI,cAAc,CAAC,QAAQ,IAAC,CAAA;gBAE1C,IAAI,cAAa,KAAM,IAAI,CAAC,UAAU,EAAE;oBACvC,MAAM,MAAM,EAAE,eAAc,GAAI;wBAC/B,KAAK,EAAE,IAAI,CAAC,KAAK;wBACjB,KAAK,EAAE,cAAa;qBACrB,CAAA;oBACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAA,CAAA;iBACnB;aACD;YACA,IAAI,CAAC,QAAQ,EAAC,CAAA;QACf,CAAA;KACD;CACD,CAAA,CAAA;;;;;;WA9PA,GAAA,CAwBiB,yBAAA,EAAA,GAAA,CAAA;QAxBA,KAAK,EAAE,IAAA,CAAA,SAAS;QAAG,YAAU,EAAE,IAAA,CAAA,SAAS;QAAG,GAAG,EAAE,IAAA,CAAA,GAAG;QAAG,eAAa,EAAE,IAAA,CAAA,YAAY;QAAG,aAAW,EAAE,IAAA,CAAA,UAAU;QAC1H,kBAAgB,EAAE,IAAA,CAAA,eAAe;;QACvB,eAAa,EAAA,WAAA,CACvB,IAmBO,GAAA,EAAA,CAAA,EAAA,CAAA;YAnBP,GAAA,CAmBO,MAAA,EAAA,GAAA,CAAA,EAnBD,KAAK,EAAC,qBAAqB,EAAA,CAAA,EAAA;gBAChC,GAAA,CAEO,MAAA,EAAA,GAAA,CAAA;oBAFD,KAAK,EAAC,wBAAwB;oBAAE,OAAK,EAAA,GAAA,EAAA,GAAE,IAAA,CAAA,SAAS,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA;;oBACrD,GAAA,CAAyC,MAAA,EAAA,GAAA,CAAA,EAAnC,KAAK,EAAC,oBAAoB,EAAA,CAAA,EAAC,GAAC,CAAA;;gBAEnC,GAAA,CAQO,MAAA,EAAA,GAAA,CAAA,EARD,KAAK,EAAC,yBAAyB,EAAA,CAAA,EAAA;oBACpC,GAAA,CAME,OAAA,EAAA,GAAA,CAAA;wBALD,KAAK,EAAC,iBAAiB;wBACvB,IAAI,EAAC,QAAQ;oCACJ,IAAA,CAAA,UAAU;gEAAV,IAAA,CAAA,UAAU,CAAA,GAAA,MAAA,CAAA,MAAA,CAAA,KAAA,CAAA,CAAA,CAAA,EACX,IAAA,CAAA,aAAa,CAAA;wBACpB,MAAI,EAAE,IAAA,CAAA,WAAW;;;gBAGpB,GAAA,CAEO,MAAA,EAAA,GAAA,CAAA;oBAFD,KAAK,EAAC,wBAAwB;oBAAE,OAAK,EAAA,GAAA,EAAA,GAAE,IAAA,CAAA,SAAS,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA;;oBACrD,GAAA,CAAyC,MAAA,EAAA,GAAA,CAAA,EAAnC,KAAK,EAAC,oBAAoB,EAAA,CAAA,EAAC,GAAC,CAAA;;uBAEA,IAAA,CAAA,QAAQ,CAAA;sBAA3C,GAAA,CAEO,MAAA,EAAA,GAAA,CAAA;;wBAFD,KAAK,EAAC,gBAAgB;;wBAC3B,GAAA,CAAuD,MAAA,EAAA,GAAA,CAAA,EAAjD,KAAK,EAAC,qBAAqB,EAAA,CAAA,EAAA,GAAA,CAAI,IAAA,CAAA,QAAQ,CAAA,EAAA,CAAA,CAAA,UAAA,CAAA", "file": "components/main-form/components/form-numberbox.uvue", "sourcesContent": ["<template>\n\t<form-container :label=\"fieldName\" :show-error=\"showError\" :tip=\"tip\" :error-message=\"errorMessage\" :label-color=\"labelColor\"\n\t\t:background-color=\"backgroundColor\">\n\t\t<template #input-content>\n\t\t\t<view class=\"numberbox-container\">\n\t\t\t\t<view class=\"numberbox-btn qShadow1\" @click=\"numberFun('-')\">\n\t\t\t\t\t<text class=\"numberbox-btn-text\">-</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"numberbox-input-wrapper\">\n\t\t\t\t\t<input\n\t\t\t\t\t\tclass=\"numberbox-input\"\n\t\t\t\t\t\ttype=\"number\"\n\t\t\t\t\t\tv-model=\"inputValue\"\n\t\t\t\t\t\t@input=\"onInputChange\"\n\t\t\t\t\t\t@blur=\"onInputBlur\"\n\t\t\t\t\t/>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"numberbox-btn qShadow1\" @click=\"numberFun('+')\">\n\t\t\t\t\t<text class=\"numberbox-btn-text\">+</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"numberbox-unit\" v-if=\"unitText\">\n\t\t\t\t\t<text class=\"numberbox-unit-text\">{{ unitText }}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</template>\n\t</form-container>\n</template>\n\n<script lang=\"uts\">\n\timport { FormFieldData, FormChangeEvent } from '@/components/main-form/form_type.uts'\n\timport FormContainer from './form-container.uvue'\n\n\texport default {\n\t\tname: \"FormNumberbox\",\n\t\temits: ['change'],\n\t\tcomponents: {\n\t\t\tFormContainer\n\t\t},\n\t\tprops: {\n\t\t\tdata: {\n\t\t\t\ttype: null as any as PropType<FormFieldData>\n\t\t\t},\n\t\t\tindex: {\n\t\t\t\ttype: Number,\n\t\t\t\tdefault: 0\n\t\t\t},\n\t\t\tkeyName: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"\"\n\t\t\t},\n\t\t\tlabelColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#000\"\n\t\t\t},\n\t\t\tbackgroundColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#f1f4f9\"\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tfieldName: \"\",\n\t\t\t\tfieldValue: 0,\n\t\t\t\tisSave: false,\n\t\t\t\tsave_key: \"\",\n\t\t\t\ttip: \"\",\n\t\t\t\tminValue: 0,\n\t\t\t\tmaxValue: 100,\n\t\t\t\tstepValue: 1,\n\t\t\t\tinputValue: \"0\",\n\t\t\t\tunitText: \"\",\n\t\t\t\tshowError: false,\n\t\t\t\terrorMessage: \"\"\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\n\t\t},\n\t\twatch: {\n\t\t\tdata: {\n\t\t\t\thandler(obj: FormFieldData) {\n\t\t\t\t\t// 只处理value的变化，当外部传入的value与当前fieldValue不同时，才更新fieldValue\n\t\t\t\t\tconst newValue = obj.value as number\n\t\t\t\t\tif (newValue !== this.fieldValue) {\n\t\t\t\t\t\tthis.fieldValue = newValue\n\t\t\t\t\t\tthis.updateInputValue()\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tdeep: true\n\t\t\t}\n\t\t},\n\t\tcreated(): void {\n\t\t\t// 初始化时调用一次即可\n\t\t\tconst fieldObj = this.$props[\"data\"] as FormFieldData\n\t\t\tthis.initFieldData(fieldObj)\n\t\t},\n\t\tmethods: {\n\t\t\t// 初始化字段数据（仅在首次加载时调用）\n\t\t\tinitFieldData(fieldObj: FormFieldData): void {\n\t\t\t\tconst fieldKey = fieldObj.key\n\t\t\t\tconst fieldValue = fieldObj.value as number\n\n\t\t\t\t// 设置基本信息\n\t\t\t\tthis.fieldName = fieldObj.name\n\t\t\t\tthis.fieldValue = fieldValue\n\t\t\t\tthis.isSave = fieldObj.isSave ?? false\n\t\t\t\tthis.save_key = this.keyName + \"_\" + fieldKey\n\n\t\t\t\t// 解析配置信息\n\t\t\t\tconst extalJson = fieldObj.extra as UTSJSONObject\n\t\t\t\tthis.minValue = extalJson.getNumber(\"min\") ?? 0\n\t\t\t\tthis.maxValue = extalJson.getNumber(\"max\") ?? 100\n\t\t\t\tthis.stepValue = extalJson.getNumber(\"step\") ?? 1\n\t\t\t\tthis.tip = extalJson.getString(\"tip\") ?? \"\"\n\t\t\t\tthis.unitText = extalJson.getString(\"unit\") ?? \"\"\n\n\t\t\t\t// 更新输入框的值\n\t\t\t\tthis.updateInputValue()\n\n\t\t\t\t// 获取缓存\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.getCache()\n\t\t\t\t}, 500)\n\t\t\t},\n\n\t\t\t// 更新输入框的值\n\t\t\tupdateInputValue(): void {\n\t\t\t\tthis.inputValue = this.fieldValue.toString()\n\t\t\t},\n\n\t\t\t// 验证值是否在有效范围内\n\t\t\tvalidateValue(value: number): number {\n\t\t\t\tif (value < this.minValue) {\n\t\t\t\t\treturn this.minValue\n\t\t\t\t}\n\t\t\t\tif (value > this.maxValue) {\n\t\t\t\t\treturn this.maxValue\n\t\t\t\t}\n\n\t\t\t\t// 处理步长\n\t\t\t\tif (this.stepValue % 1 == 0) {\n\t\t\t\t\t// 整数步长\n\t\t\t\t\treturn Math.round(value)\n\t\t\t\t} else {\n\t\t\t\t\t// 小数步长，保留一位小数\n\t\t\t\t\treturn Number.from(value.toFixed(1))\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tgetCache(): void {\n\t\t\t\tif (this.isSave) {\n\t\t\t\t\tconst that = this\n\t\t\t\t\tuni.getStorage({\n\t\t\t\t\t\tkey: this.save_key,\n\t\t\t\t\t\tsuccess: (res: GetStorageSuccess) => {\n\t\t\t\t\t\t\tconst save_value = res.data as number\n\t\t\t\t\t\t\tconst validatedValue = that.validateValue(save_value)\n\t\t\t\t\t\t\tthat.fieldValue = validatedValue\n\t\t\t\t\t\t\tthat.updateInputValue()\n\t\t\t\t\t\t\tconst result: FormChangeEvent = {\n\t\t\t\t\t\t\t\tindex: this.index,\n\t\t\t\t\t\t\t\tvalue: validatedValue\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tthis.change(result)\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tsetCache(): void {\n\t\t\t\tif (this.isSave) {\n\t\t\t\t\tuni.setStorage({\n\t\t\t\t\t\tkey: this.save_key,\n\t\t\t\t\t\tdata: this.fieldValue\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tvalidate(): boolean {\n\t\t\t\t// 数字输入框组件通常不需要额外验证，因为值已经被限制在min-max范围内\n\t\t\t\tthis.showError = false\n\t\t\t\tthis.errorMessage = \"\"\n\t\t\t\treturn true\n\t\t\t},\n\n\t\t\tchange(event: FormChangeEvent): void {\n\t\t\t\t// 更新字段值\n\t\t\t\tthis.fieldValue = event.value as number\n\t\t\t\t// 保存缓存\n\t\t\t\tthis.setCache()\n\t\t\t\t// 触发父组件事件\n\t\t\t\tthis.$emit('change', event)\n\t\t\t},\n\n\t\t\t// 按钮点击事件\n\t\t\tnumberFun(type: string): void {\n\t\t\t\tlet value: number\n\t\t\t\tlet num: number = this.fieldValue\n\n\t\t\t\tif (type == \"+\") {\n\t\t\t\t\tnum = num + this.stepValue\n\t\t\t\t} else {\n\t\t\t\t\tnum = num - this.stepValue\n\t\t\t\t}\n\n\t\t\t\t// 验证并限制值\n\t\t\t\tvalue = this.validateValue(num)\n\n\t\t\t\t// 更新输入框显示\n\t\t\t\tthis.inputValue = value.toString()\n\n\t\t\t\tconst result: FormChangeEvent = {\n\t\t\t\t\tindex: this.index,\n\t\t\t\t\tvalue: value\n\t\t\t\t}\n\t\t\t\tthis.change(result)\n\t\t\t},\n\n\t\t\tonInputChange(event: UniInputEvent): void {\n\t\t\t\tconst inputStr = event.detail.value as string\n\t\t\t\tconst inputNum = parseFloat(inputStr)\n\n\t\t\t\tif (!isNaN(inputNum)) {\n\t\t\t\t\tconst validatedValue = this.validateValue(inputNum)\n\n\t\t\t\t\tconst result: FormChangeEvent = {\n\t\t\t\t\t\tindex: this.index,\n\t\t\t\t\t\tvalue: validatedValue\n\t\t\t\t\t}\n\t\t\t\t\tthis.change(result)\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tonInputBlur(): void {\n\t\t\t\t// 在失去焦点时进行验证和格式化\n\t\t\t\tconst inputNum = parseFloat(this.inputValue)\n\t\t\t\tif (isNaN(inputNum)) {\n\t\t\t\t\t// 如果输入无效，恢复到当前字段值\n\t\t\t\t\tthis.inputValue = this.fieldValue.toString()\n\t\t\t\t} else {\n\t\t\t\t\t// 验证并格式化输入值\n\t\t\t\t\tconst validatedValue = this.validateValue(inputNum)\n\t\t\t\t\tthis.inputValue = validatedValue.toString()\n\n\t\t\t\t\tif (validatedValue !== this.fieldValue) {\n\t\t\t\t\t\tconst result: FormChangeEvent = {\n\t\t\t\t\t\t\tindex: this.index,\n\t\t\t\t\t\t\tvalue: validatedValue\n\t\t\t\t\t\t}\n\t\t\t\t\t\tthis.change(result)\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthis.validate()\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t.numberbox-container {\n\t\twidth: 100%;\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t}\n\n\t.numberbox-btn {\n\t\twidth: 80rpx;\n\t\theight: 80rpx;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tbackground-color: #e1e1e1;\n\t\tborder-radius: 10rpx;\n\t}\n\n\t.numberbox-btn-text {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t}\n\n\t.numberbox-input-wrapper {\n\t\tflex: 1;\n\t\theight: 80rpx;\n\t\tbackground-color: #f2f4f8;\n\t\tmargin: 0 20rpx;\n\t\tborder-radius: 10rpx;\n\t}\n\n\t.numberbox-input {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\ttext-align: center;\n\t\tfont-size: 32rpx;\n\t\tborder: none;\n\t\tbackground-color: transparent;\n\t}\n\n\t.numberbox-unit {\n\t\tpadding: 0 10rpx;\n\t\theight: 80rpx;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tbackground-color: #333;\n\t\tborder-radius: 10rpx;\n\t\tmargin-left: 20rpx;\n\t}\n\n\t.numberbox-unit-text {\n\t\tcolor: #fff;\n\t\tfont-size: 28rpx;\n\t}\n\n\t/* 阴影样式 */\n\t.qShadow1 {\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n\t}\n</style>"]}