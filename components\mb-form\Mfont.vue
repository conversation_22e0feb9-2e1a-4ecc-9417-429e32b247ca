<template>
	<view class="Mselect">
		<view class="Mselect-name" :style="{color:color}">
			{{data.name}}
		</view>

		<view class="Mselect-box qShadow1" :style="{backgroundColor:bgColor}" @click="selectFun">
			<view class="mb-selectText" :style="{fontFamily:data.fontFamily}">
				{{mText}}
			</view>

			<view class="mb-selectIcon">
				<image src="@/static/main/menu-down.png" mode=""></image>
			</view>
		</view>

	</view>



</template>

<script>
	import FontTool from "@/common/font.js"
	export default {
		name: "Mfont",
		props: {
			data: {
				type: Object,
				default: {}
			},
			keyName: {
				type: String,
				default: ""

			},
			index: {
				type: Number,
				default: 0
			},
			color: {
				type: String,
				default: '#000'
			},
			bgColor: {
				type: String,
				default: "#f1f4f9"

			}
		},
		created() {

			this.initData()

		},
		data() {
			return {
				selectData: [],
				fontList: [],
				mText: ""
			};
		},
		watch: {


		},
		methods: {
			getCurrentFont() {
				return new Promise((resolve) => {

					if (this.data.isSave) {
						this.saveKey = this.keyName + "_" + this.data.key
						uni.getStorage({
							key: this.saveKey,
							success: (font) => {
								if (font.data != "") {

									const found = this.fontList.find(obj => obj.value === font.data);

									if (found !== undefined) {
										resolve(found)

									} else {
										resolve({
											text: "无",
											value: ""
										})


									}
								} else {
									if (this.data.value != undefined && this.data.value != "") {
										resolve(this.fontList[0])
									}
									else{
										resolve({
											text: "无",
											value: ""
										})
									}
									
									
									
								}

							},
							fail: () => {

								if (this.data.value != undefined && this.data.value != "") {

									const found = this.fontList.find(obj => obj.value === this.data
										.value);


									if (found !== undefined) {
										resolve(found)

									} else {
										resolve(this.fontList[0])


									}


								} else {
									resolve({
										text: "无",
										value: ""
									})
								}
							}
						});

					} else {

						if (this.data.value != undefined && this.data.value != "") {

							const found = this.fontList.find(obj => obj.value === this.data.value);


							if (found !== undefined) {
								resolve(found)

							} else {
								resolve(this.fontList[0])


							}


						} else {
							resolve({
								text: "无",
								value: ""
							})
						}





					}



				})
			},
			initData() {


				FontTool.getFontList(this.data.fontType).then(res => {


					if (res) {
						this.fontList = res

						this.getCurrentFont().then(res => {

							console.log(res)
							if (res) {


								this.mText = res["text"]

								let mvalue = res["value"]
								this.$emit("change", {
									index: this.index,
									value: mvalue
								})

							}



						})


					}




				})



			},
			setFont(name) {

				FontTool.setfont(name, this.data.fontFamily).then(res => {


					console.log("设置字体", res)

				})

			},
			selectFun() {
				const selectList = this.fontList.map(obj => obj.text);


				if (selectList.length > 0) {

					uni.showActionSheet({
						itemList: selectList,
						success: (res) => {
							this.mText = this.fontList[res.tapIndex]["text"]
							let mvalue = this.fontList[res.tapIndex]["value"]
							this.$emit("change", {
								index: this.index,
								value: mvalue
							})

							if (this.data.isSave) {
								try {
									uni.setStorageSync(this.saveKey, mvalue);
								} catch (e) {
									// error
								}

							}

						},
						fail: function(res) {
							console.log(res.errMsg);
						}
					});

				}

			}
		}
	}
</script>

<style>
	.Mselect {
		width: 100%;
		display: flex;
		flex-direction: column;
		padding: 0 20rpx;
		margin-bottom: 20rpx;

	}

	.Mselect-name {
		width: 100%;
		font-size: 32rpx;
		margin-bottom: 10rpx;
	}

	.Mselect-box {
		width: 100%;
		height: 100rpx;
		border: 1rpx solid #fff;
		box-sizing: border-box;
		padding: 0 10rpx;
		border-radius: 20rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
	}

	.mb-selectText {
		flex: 1;
		padding-left: 20rpx;
	}

	.mb-selectIcon {
		width: 60rpx;
		height: 60rpx;
	}

	.mb-selectIcon image {
		width: 100%;
		height: 100%;
	}
</style>