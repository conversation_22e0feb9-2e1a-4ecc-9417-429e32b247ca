<template>
	<view class="Mselect">
		<view class="Mselect-name" :style="{color:color}">
			{{data.name}}
		</view>
	
		<view class="Mselect-box qShadow1" :style="{backgroundColor:bgColor}" @click="selectFun">
			<view class="mb-selectText">
				{{mText}}
			</view>
			
			<view class="mb-selectIcon">
				<image src="@/static/main/menu-down.png" mode=""></image>
			</view>
		</view>
	
	</view>
	
	
	
</template>

<script>
	export default {
		name: "Mselect",
		props: {
			data: {
				type: Object,
				default: {}
			},
			keyName: {
				type: String,
				default: ""
			
			},
			index: {
				type: Number,
				default: 0
			},
			color: {
				type: String,
				default: '#000'
			},
			bgColor: {
				type: String,
				default: "#f1f4f9"
			
			}
		},
		created() {
			
			this.initData()
		
		},
		data() {
			return {
				selectData:[],
				mText:"",
				saveKey:""
			};
		},
		watch:{
			data: {
				
				handler(newValue, oldValue) {
					
					this.initData() 
					
				},
				immediate: true,
				deep: true
			}
			
		},
		methods:{
			initData(){
				this.selectData=this.data.data
				const selectData = this.data.data.reduce((acc, obj) => {
				  acc[String(obj.value)] = obj.text;
				  return acc;
				}, {});
				
				if(this.data.isSave){
					
					this.saveKey=this.keyName+"_"+this.data.key
					
					
					
					uni.getStorage({
						key: this.saveKey,
						success:  (res)=> {
							
							if(selectData[String(res.data)]){
								this.mText=selectData[String(res.data)]
								let mvalue=res.data
								if(this.data.varType=="int"  ||this.data.varType=="float"){
									 mvalue=Number(mvalue)
								}else{
									 mvalue=String(mvalue)
								}
								this.$emit("change",{index:this.index,value:mvalue})
							}else{
								
								if(this.data.value==""){
									this.mText=this.selectData[0]["value"]
								}else{
									
									if(selectData[String(this.data.value)]){
										this.mText=selectData[String(this.data.value)]
									}else{
										this.mText=this.selectData[0]["value"]
									}
									
								}
								
								
							}
							
							
							
							
							
						},
						fail: () => {
							
							if(this.data.value===""){
								
								this.mText=this.selectData[0]["text"]
							}else{
								
								if(selectData[String(this.data.value)]){
									console.log("有值")
									this.mText=selectData[String(this.data.value)]
								}else{
									
									console.log("没有值")
									
									
									this.mText=this.selectData[0]["text"]
								}
								
							}
						}
					});
					
					
				}else{
				
					if(this.data.value==""){
						this.mText=this.selectData[0]["value"]
					}else{
						
						if(selectData[String(this.data.value)]){
							this.mText=selectData[String(this.data.value)]
						}else{
							this.mText=this.selectData[0]["value"]
						}
						
					}
				}
			},
			selectFun(){
				const selectList = this.selectData.map(obj => obj.text);
				
				
				if(selectList.length>0){
					
					uni.showActionSheet({
						itemList: selectList,
						success:  (res)=> {
							
							
							this.mText=this.selectData[res.tapIndex]["text"]
							
							let mvalue=this.selectData[res.tapIndex]["value"]
							if(this.data.varType=="int"||this.data.varType=="float"){
								 mvalue=Number(mvalue)
							}else{
								 mvalue=String(mvalue)
							}
							
							
							
							this.$emit("change",{index:this.index,value:mvalue})
							
							if(this.data.isSave){
								try {
									uni.setStorageSync(this.saveKey, mvalue);
								} catch (e) {
									// error
								}
								
							}
							
						},
						fail: function (res) {
							console.log(res.errMsg);
						}
					});
					
				}
				
			}
		}
	}
</script>

<style>
.Mselect {
		width: 100%;
		display: flex;
		flex-direction: column;
		padding: 0 20rpx;
		margin-bottom: 20rpx;
	
	}
	
	.Mselect-name {
		width: 100%;
		font-size: 32rpx;
		margin-bottom: 10rpx;
	}
	
	.Mselect-box {
		width: 100%;
		height: 100rpx;
		border: 1rpx solid #fff;
		box-sizing: border-box;
		padding: 0 10rpx;
		border-radius: 20rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
	}
	
	.mb-selectText {
		flex: 1;
		padding-left: 20rpx;
	}
	
	.mb-selectIcon {
		width: 60rpx;
		height: 60rpx;
	}
	.mb-selectIcon image{
		width: 100%;
		height: 100%;
	}
</style>