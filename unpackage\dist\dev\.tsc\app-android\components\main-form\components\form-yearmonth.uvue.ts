
	import { FormFieldData, FormChangeEvent } from '@/components/main-form/form_type.uts'
	import FormContainer from './form-container.uvue'
	import MainYearmonthPicker from './../tools/main-yearmonth-picker.uvue'

	// 年月数据类型
	type YearMonthData = { __$originalPosition?: UTSSourceMapPosition<"YearMonthData", "components/main-form/components/form-yearmonth.uvue", 24, 7>;
		year: number,
		month: number
	}

	const __sfc__ = defineComponent({
		name: "FormYearmonth",
		emits: ['change'],
		components: {
			FormContainer,
			MainYearmonthPicker
		},
		props: {
			data: {
				type: null as any as PropType<FormFieldData>
			},
			index: {
				type: Number,
				default: 0
			},
			keyName: {
				type: String,
				default: ""
			},
			labelColor: {
				type: String,
				default: "#000"
			},
			backgroundColor: {
				type: String,
				default: "#f1f4f9"
			}
		},
		data() {
			return {
				fieldName: "",
				fieldValue: "" as string,
				isSave: false,
				save_key: "",
				tip: "",
				displayText: "请选择年月",
				showError: false,
				errorMessage: ""
			}
		},
		computed: {

		},
		watch: {
			data: {
				handler(obj: FormFieldData) {
					// 只处理value的变化，当外部传入的value与当前fieldValue不同时，才更新fieldValue
					// 这避免了用户输入时的循环更新问题
					const newValue = obj.value as string
					if (newValue !== this.fieldValue) {
						this.fieldValue = newValue
						this.updateDisplayText()
					}
				},
				deep: true
			}
		},
		created(): void {
			// 初始化时调用一次即可
			const fieldObj = this.$props["data"] as FormFieldData
			this.initFieldData(fieldObj)
		},
		methods: {
			// 初始化字段数据（仅在首次加载时调用）
			initFieldData(fieldObj: FormFieldData): void {
				const fieldKey = fieldObj.key
				const fieldValue = fieldObj.value as string

				// 设置基本信息
				this.fieldName = fieldObj.name
				this.fieldValue = fieldValue
				this.isSave = fieldObj.isSave ?? false
				this.save_key = this.keyName + "_" + fieldKey

				// 解析配置信息
				const extalJson = fieldObj.extra as UTSJSONObject
				this.tip = extalJson.getString("tip") ?? ""

				// 更新显示文本
				this.updateDisplayText()

				// 获取缓存
				this.getCache()
			},

			// 更新显示文本
			updateDisplayText(): void {
				if (this.fieldValue != "" ) {
					// 验证格式是否为 YYYY-MM
					const yearMonthPattern = /^\d{4}-\d{2}$/
					if (yearMonthPattern.test(this.fieldValue)) {
						const parts = this.fieldValue.split("-")
						const year = parts[0]
						const month = parts[1]
						this.displayText = `${year}年${month}月`
					} else {
						this.displayText = this.fieldValue
					}
				} else {
					this.displayText = "请选择年月"
				}
			},

			getCache(): void {
				if (this.isSave) {
					const that = this
					uni.getStorage({
						key: this.save_key,
						success: (res: GetStorageSuccess) => {
							const save_value = res.data
							if(typeof save_value === 'string'){
								that.fieldValue = save_value as string
								that.updateDisplayText()
								const result: FormChangeEvent = {
									index: this.index,
									value: save_value
								}
								this.change(result)
							}

						}
					})
				}
			},

			setCache(): void {
				if (this.isSave && typeof this.fieldValue ==="string") {
					uni.setStorage({
						key: this.save_key,
						data: this.fieldValue
					})
				}
			},

			validate(): boolean {
				// 年月值验证
				if (this.fieldValue == "") {
					this.showError = true
					this.errorMessage = "请选择年月"
					return false
				}

				// 验证年月格式 YYYY-MM
				const yearMonthPattern = /^\d{4}-\d{2}$/
				if (!yearMonthPattern.test(this.fieldValue)) {
					this.showError = true
					this.errorMessage = "年月格式不正确"
					return false
				}

				// 验证月份范围
				const parts = this.fieldValue.split("-")
				const month = parseInt(parts[1])
				if (month < 1 || month > 12) {
					this.showError = true
					this.errorMessage = "月份必须在1-12之间"
					return false
				}

				this.showError = false
				this.errorMessage = ""
				return true
			},

			change(event: FormChangeEvent): void {
				// 更新字段值
				this.fieldValue = event.value as string
				// 更新显示文本
				this.updateDisplayText()
				// 保存缓存
				this.setCache()
				// 触发父组件事件
				this.$emit('change', event)
			},

			// 打开年月选择器
			openYearMonthPicker(): void {
				const yearmonthPicker = this.$refs["yearmonthPicker"] as ComponentPublicInstance
				yearmonthPicker.$callMethod("open")
			},

			// 年月选择确认
			onYearMonthConfirm(yearMonthData: UTSJSONObject): void {
				console.log(yearMonthData, " at components/main-form/components/form-yearmonth.uvue:212")
				// 格式化为 YYYY-MM 格式
				const year = yearMonthData.getNumber("year")
				const month = yearMonthData.getNumber("month")
				let yearValue :string=new Date().getFullYear().toString()
				let monthValue :string=new Date().getMonth().toString()

				if(year!=null){
					yearValue=year.toString()
				}
				if(month!=null){
					monthValue=month.toString().padStart(2, '0')
				}
				
				const selectedValue = `${yearValue}-${monthValue}`

				const result: FormChangeEvent = {
					index: this.index,
					value: selectedValue
				}
				this.change(result)
			},

			// 年月选择取消
			onYearMonthCancel(): void {
				// 取消选择，不做任何操作
			}
		}
	})

export default __sfc__
function GenComponentsMainFormComponentsFormYearmonthRender(this: InstanceType<typeof __sfc__>): any | null {
const _ctx = this
const _cache = this.$.renderCache
const _component_form_container = resolveComponent("form-container")
const _component_main_yearmonth_picker = resolveComponent("main-yearmonth-picker")

  return _cE(Fragment, null, [
    _cV(_component_form_container, _uM({
      label: _ctx.fieldName,
      "show-error": _ctx.showError,
      tip: _ctx.tip,
      "error-message": _ctx.errorMessage,
      "label-color": _ctx.labelColor,
      "background-color": _ctx.backgroundColor
    }), _uM({
      "input-content": withSlotCtx((): any[] => [
        _cE("view", _uM({
          class: "yearmonth-display-container",
          onClick: _ctx.openYearMonthPicker
        }), [
          _cE("view", _uM({ class: "yearmonth-icon" }), [
            _cE("text", _uM({ class: "yearmonth-icon-text" }), "📅")
          ]),
          _cE("text", _uM({ class: "yearmonth-text" }), _tD(_ctx.displayText), 1 /* TEXT */)
        ], 8 /* PROPS */, ["onClick"])
      ]),
      _: 1 /* STABLE */
    }), 8 /* PROPS */, ["label", "show-error", "tip", "error-message", "label-color", "background-color"]),
    _cV(_component_main_yearmonth_picker, _uM({
      ref: "yearmonthPicker",
      onConfirm: _ctx.onYearMonthConfirm,
      onCancel: _ctx.onYearMonthCancel
    }), null, 8 /* PROPS */, ["onConfirm", "onCancel"])
  ], 64 /* STABLE_FRAGMENT */)
}
const GenComponentsMainFormComponentsFormYearmonthStyles = [_uM([["yearmonth-display-container", _pS(_uM([["flex", 1], ["display", "flex"], ["flexDirection", "row"], ["alignItems", "center"], ["minHeight", "60rpx"], ["paddingTop", "10rpx"], ["paddingRight", "10rpx"], ["paddingBottom", "10rpx"], ["paddingLeft", "10rpx"], ["borderTopLeftRadius", "10rpx"], ["borderTopRightRadius", "10rpx"], ["borderBottomRightRadius", "10rpx"], ["borderBottomLeftRadius", "10rpx"], ["backgroundColor", "rgba(255,255,255,0.8)"]]))], ["yearmonth-icon", _pS(_uM([["width", "60rpx"], ["height", "40rpx"], ["display", "flex"], ["justifyContent", "center"], ["alignItems", "center"], ["marginRight", "20rpx"]]))], ["yearmonth-icon-text", _pS(_uM([["fontSize", "32rpx"]]))], ["yearmonth-text", _pS(_uM([["flex", 1], ["fontSize", "28rpx"], ["color", "#333333"]]))]])]
