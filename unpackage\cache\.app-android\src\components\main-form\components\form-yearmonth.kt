@file:Suppress("UNCHECKED_CAST", "USELESS_CAST", "INAPPLICABLE_JVM_NAME", "UNUSED_ANONYMOUS_PARAMETER", "NAME_SHADOWING", "UNNECESSARY_NOT_NULL_ASSERTION")
package uni.UNIC178CB1
import io.dcloud.uniapp.*
import io.dcloud.uniapp.extapi.*
import io.dcloud.uniapp.framework.*
import io.dcloud.uniapp.runtime.*
import io.dcloud.uniapp.vue.*
import io.dcloud.uniapp.vue.shared.*
import io.dcloud.unicloud.*
import io.dcloud.uts.*
import io.dcloud.uts.Map
import io.dcloud.uts.Set
import io.dcloud.uts.UTSAndroid
import io.dcloud.uniapp.extapi.getStorage as uni_getStorage
import io.dcloud.uniapp.extapi.setStorage as uni_setStorage
open class GenComponentsMainFormComponentsFormYearmonth : VueComponent {
    constructor(__ins: ComponentInternalInstance) : super(__ins) {
        onCreated(fun(): Unit {
            val fieldObj = this.`$props`["data"] as FormFieldData
            this.initFieldData(fieldObj)
        }
        , __ins)
        this.`$watch`(fun(): Any? {
            return this.data
        }
        , fun(obj: FormFieldData) {
            val newValue = obj.value as String
            if (newValue !== this.fieldValue) {
                this.fieldValue = newValue
                this.updateDisplayText()
            }
        }
        , WatchOptions(deep = true))
    }
    @Suppress("UNUSED_PARAMETER", "UNUSED_VARIABLE")
    override fun `$render`(): Any? {
        val _ctx = this
        val _cache = this.`$`.renderCache
        val _component_form_container = resolveComponent("form-container")
        val _component_main_yearmonth_picker = resolveComponent("main-yearmonth-picker")
        return _cE(Fragment, null, _uA(
            _cV(_component_form_container, _uM("label" to _ctx.fieldName, "show-error" to _ctx.showError, "tip" to _ctx.tip, "error-message" to _ctx.errorMessage, "label-color" to _ctx.labelColor, "background-color" to _ctx.backgroundColor), _uM("input-content" to withSlotCtx(fun(): UTSArray<Any> {
                return _uA(
                    _cE("view", _uM("class" to "yearmonth-display-container", "onClick" to _ctx.openYearMonthPicker), _uA(
                        _cE("view", _uM("class" to "yearmonth-icon"), _uA(
                            _cE("text", _uM("class" to "yearmonth-icon-text"), "📅")
                        )),
                        _cE("text", _uM("class" to "yearmonth-text"), _tD(_ctx.displayText), 1)
                    ), 8, _uA(
                        "onClick"
                    ))
                )
            }
            ), "_" to 1), 8, _uA(
                "label",
                "show-error",
                "tip",
                "error-message",
                "label-color",
                "background-color"
            )),
            _cV(_component_main_yearmonth_picker, _uM("ref" to "yearmonthPicker", "onConfirm" to _ctx.onYearMonthConfirm, "onCancel" to _ctx.onYearMonthCancel), null, 8, _uA(
                "onConfirm",
                "onCancel"
            ))
        ), 64)
    }
    open var data: Any? by `$props`
    open var index: Number by `$props`
    open var keyName: String by `$props`
    open var labelColor: String by `$props`
    open var backgroundColor: String by `$props`
    open var fieldName: String by `$data`
    open var fieldValue: String by `$data`
    open var isSave: Boolean by `$data`
    open var save_key: String by `$data`
    open var tip: String by `$data`
    open var displayText: String by `$data`
    open var showError: Boolean by `$data`
    open var errorMessage: String by `$data`
    @Suppress("USELESS_CAST")
    override fun data(): Map<String, Any?> {
        return _uM("fieldName" to "", "fieldValue" to "" as String, "isSave" to false, "save_key" to "", "tip" to "", "displayText" to "请选择年月", "showError" to false, "errorMessage" to "")
    }
    open var initFieldData = ::gen_initFieldData_fn
    open fun gen_initFieldData_fn(fieldObj: FormFieldData): Unit {
        val fieldKey = fieldObj.key
        val fieldValue = fieldObj.value as String
        this.fieldName = fieldObj.name
        this.fieldValue = fieldValue
        this.isSave = fieldObj.isSave ?: false
        this.save_key = this.keyName + "_" + fieldKey
        val extalJson = fieldObj.extra as UTSJSONObject
        this.tip = extalJson.getString("tip") ?: ""
        this.updateDisplayText()
        this.getCache()
    }
    open var updateDisplayText = ::gen_updateDisplayText_fn
    open fun gen_updateDisplayText_fn(): Unit {
        if (this.fieldValue != "") {
            val yearMonthPattern = UTSRegExp("^\\d{4}-\\d{2}\$", "")
            if (yearMonthPattern.test(this.fieldValue)) {
                val parts = this.fieldValue.split("-")
                val year = parts[0]
                val month = parts[1]
                this.displayText = "" + year + "\u5E74" + month + "\u6708"
            } else {
                this.displayText = this.fieldValue
            }
        } else {
            this.displayText = "请选择年月"
        }
    }
    open var getCache = ::gen_getCache_fn
    open fun gen_getCache_fn(): Unit {
        if (this.isSave) {
            val that = this
            uni_getStorage(GetStorageOptions(key = this.save_key, success = fun(res: GetStorageSuccess){
                val save_value = res.data
                if (UTSAndroid.`typeof`(save_value) === "string") {
                    that.fieldValue = save_value as String
                    that.updateDisplayText()
                    val result = FormChangeEvent(index = this.index, value = save_value)
                    this.change(result)
                }
            }
            ))
        }
    }
    open var setCache = ::gen_setCache_fn
    open fun gen_setCache_fn(): Unit {
        if (this.isSave && UTSAndroid.`typeof`(this.fieldValue) === "string") {
            uni_setStorage(SetStorageOptions(key = this.save_key, data = this.fieldValue))
        }
    }
    open var validate = ::gen_validate_fn
    open fun gen_validate_fn(): Boolean {
        if (this.fieldValue == "") {
            this.showError = true
            this.errorMessage = "请选择年月"
            return false
        }
        val yearMonthPattern = UTSRegExp("^\\d{4}-\\d{2}\$", "")
        if (!yearMonthPattern.test(this.fieldValue)) {
            this.showError = true
            this.errorMessage = "年月格式不正确"
            return false
        }
        val parts = this.fieldValue.split("-")
        val month = parseInt(parts[1])
        if (month < 1 || month > 12) {
            this.showError = true
            this.errorMessage = "月份必须在1-12之间"
            return false
        }
        this.showError = false
        this.errorMessage = ""
        return true
    }
    open var change = ::gen_change_fn
    open fun gen_change_fn(event: FormChangeEvent): Unit {
        this.fieldValue = event.value as String
        this.updateDisplayText()
        this.setCache()
        this.`$emit`("change", event)
    }
    open var openYearMonthPicker = ::gen_openYearMonthPicker_fn
    open fun gen_openYearMonthPicker_fn(): Unit {
        val yearmonthPicker = this.`$refs`["yearmonthPicker"] as ComponentPublicInstance
        yearmonthPicker.`$callMethod`("open")
    }
    open var onYearMonthConfirm = ::gen_onYearMonthConfirm_fn
    open fun gen_onYearMonthConfirm_fn(yearMonthData: UTSJSONObject): Unit {
        console.log(yearMonthData, " at components/main-form/components/form-yearmonth.uvue:212")
        val year = yearMonthData.getNumber("year")
        val month = yearMonthData.getNumber("month")
        var yearValue: String = Date().getFullYear().toString(10)
        var monthValue: String = Date().getMonth().toString(10)
        if (year != null) {
            yearValue = year.toString(10)
        }
        if (month != null) {
            monthValue = month.toString(10).padStart(2, "0")
        }
        val selectedValue = "" + yearValue + "-" + monthValue
        val result = FormChangeEvent(index = this.index, value = selectedValue)
        this.change(result)
    }
    open var onYearMonthCancel = ::gen_onYearMonthCancel_fn
    open fun gen_onYearMonthCancel_fn(): Unit {}
    companion object {
        var name = "FormYearmonth"
        val styles: Map<String, Map<String, Map<String, Any>>> by lazy {
            _nCS(_uA(
                styles0
            ))
        }
        val styles0: Map<String, Map<String, Map<String, Any>>>
            get() {
                return _uM("yearmonth-display-container" to _pS(_uM("flex" to 1, "display" to "flex", "flexDirection" to "row", "alignItems" to "center", "minHeight" to "60rpx", "paddingTop" to "10rpx", "paddingRight" to "10rpx", "paddingBottom" to "10rpx", "paddingLeft" to "10rpx", "borderTopLeftRadius" to "10rpx", "borderTopRightRadius" to "10rpx", "borderBottomRightRadius" to "10rpx", "borderBottomLeftRadius" to "10rpx", "backgroundColor" to "rgba(255,255,255,0.8)")), "yearmonth-icon" to _pS(_uM("width" to "60rpx", "height" to "40rpx", "display" to "flex", "justifyContent" to "center", "alignItems" to "center", "marginRight" to "20rpx")), "yearmonth-icon-text" to _pS(_uM("fontSize" to "32rpx")), "yearmonth-text" to _pS(_uM("flex" to 1, "fontSize" to "28rpx", "color" to "#333333")))
            }
        var inheritAttrs = true
        var inject: Map<String, Map<String, Any?>> = _uM()
        var emits: Map<String, Any?> = _uM("change" to null)
        var props = _nP(_uM("data" to _uM(), "index" to _uM("type" to "Number", "default" to 0), "keyName" to _uM("type" to "String", "default" to ""), "labelColor" to _uM("type" to "String", "default" to "#000"), "backgroundColor" to _uM("type" to "String", "default" to "#f1f4f9")))
        var propsNeedCastKeys = _uA(
            "index",
            "keyName",
            "labelColor",
            "backgroundColor"
        )
        var components: Map<String, CreateVueComponent> = _uM("FormContainer" to GenComponentsMainFormComponentsFormContainerClass, "MainYearmonthPicker" to GenComponentsMainFormToolsMainYearmonthPickerClass)
    }
}
