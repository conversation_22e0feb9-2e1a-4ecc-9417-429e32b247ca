<template>
	<!-- 弹窗遮罩层 -->
	<view v-if="visible" class="picker-overlay" @click="onOverlayClick">
		<view class="picker-modal" @click.stop="">
			<view class="datetime-picker-container">
				<!-- 导航栏 -->
				<view class="navbar">
					<text class="nav-btn cancel-btn" @click="onCancel">取消</text>
					<text class="nav-title">{{ displayTitle }}</text>
					<view class="confirm-btn-container">
						<text class="nav-btn confirm-btn" @click="onConfirm">确定</text>
					</view>
				</view>

				<!-- 快捷选项 -->
				<view v-if="quickOptions.length > 0" class="quick-options">
					<text v-for="(option, index) in quickOptions" :key="index" class="quick-item"
						:class="{ 'quick-item-active': currentQuickIndex == index }" @click="onQuickSelect(option, index)">
						{{ option.label }}
					</text>
				</view>

				<!-- 区间选择标签 -->
				<view v-if="isRange" class="range-tabs">
					<text class="range-tab" :class="{ 'range-tab-active': rangeIndex == 0 }" @click="onRangeChange(0)">
						开始时间
					</text>
					<text class="range-tab" :class="{ 'range-tab-active': rangeIndex == 1 }" @click="onRangeChange(1)">
						结束时间
					</text>
				</view>

				<!-- picker-view 选择器 -->
				<view class="picker-body">
					<picker-view :value="pickerValue" @change="onPickerChange" class="picker-view"
						:indicator-style="indicatorStyle" :mask-style="maskStyle">
						<picker-view-column v-if="showYear">
							<view class="picker-item" v-for="year in years" :key="year">
								<text class="picker-text">{{ year }}年</text>
							</view>
						</picker-view-column>
						<picker-view-column v-if="showMonth">
							<view class="picker-item" v-for="month in months" :key="month">
								<text class="picker-text">{{ month }}月</text>
							</view>
						</picker-view-column>
						<picker-view-column v-if="showDay">
							<view class="picker-item" v-for="day in days" :key="day">
								<text class="picker-text">{{ day }}日</text>
							</view>
						</picker-view-column>
						<picker-view-column v-if="showHour">
							<view class="picker-item" v-for="hour in hours" :key="hour">
								<text class="picker-text">{{ hour }}时</text>
							</view>
						</picker-view-column>
						<picker-view-column v-if="showMinute">
							<view class="picker-item" v-for="minute in minutes" :key="minute">
								<text class="picker-text">{{ minute }}分</text>
							</view>
						</picker-view-column>
						<picker-view-column v-if="showSecond">
							<view class="picker-item" v-for="second in seconds" :key="second">
								<text class="picker-text">{{ second }}秒</text>
							</view>
						</picker-view-column>
					</picker-view>
				</view>

				<!-- 当前选择显示区域 -->
				<view class="current-selection">
					<text class="selection-label">当前选择：</text>
					<text class="selection-value">{{ currentDisplayValue }}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	// 定义快捷选项类型
	type QuickOption = {
		label: string,
		value: Date | Date[],
		autoConfirm?: boolean
	}

	// 定义模式类型
	type DateTimeMode = 'datetime' | 'date' | 'time' | 'year' | 'year-month' | 'month' | 'day' | 'hour-minute' | 'hour-minute-second' | 'datetime-range' | 'date-range' | 'time-range'

	export default {
		name: "main-datetime-picker",
		emits: ['cancel', 'confirm', 'change'],
		props: {
			// 选择模式
			mode: {
				type: String as PropType<DateTimeMode>,
				default: 'datetime' as DateTimeMode
			},
			// 标题
			title: {
				type: String,
				default: '选择时间'
			},
			// 是否显示秒
			showSeconds: {
				type: Boolean,
				default: false
			},
			// 开始年份
			startYear: {
				type: Number,
				default: () => new Date().getFullYear() - 5
			},
			// 结束年份
			endYear: {
				type: Number,
				default: () => new Date().getFullYear() + 5
			},
			// 快捷选项
			quickOptions: {
				type: Array as PropType<QuickOption[]>,
				default: () => [] as QuickOption[]
			},
			// 选择器高度
			height: {
				type: Number,
				default: 264
			}
		},
		data() {
			const now: Date = new Date()
			return {
				// 控制弹窗显示
				visible: false as boolean,
				// 当前日期
				currentDate: now as Date,
				// 区间值
				rangeValues: [now, now] as Date[],
				// 当前区间索引
				rangeIndex: 0 as number,
				// picker-view 的值
				pickerValue: [] as number[],
				// 当前快捷选项索引
				currentQuickIndex: -1 as number,
				// 年份列表
				years: [] as string[],
				// 月份列表
				months: [] as string[],
				// 日期列表
				days: [] as string[],
				// 小时列表
				hours: [] as string[],
				// 分钟列表
				minutes: [] as string[],
				// 秒列表
				seconds: [] as string[],
				// 是否已初始化
				isInitialized: false as boolean
			}
		},
		computed: {
			// 是否为区间模式
			isRange(): boolean {
				return this.mode.includes('range')
			},
			// 是否显示年份
			showYear(): boolean {
				return !['time', 'hour-minute', 'hour-minute-second'].includes(this.mode)
			},
			// 是否显示月份
			showMonth(): boolean {
				return ['datetime', 'date', 'year-month', 'month'].includes(this.mode) ||
					   ['datetime-range', 'date-range'].includes(this.mode)
			},
			// 是否显示日期
			showDay(): boolean {
				return ['datetime', 'date'].includes(this.mode) ||
					   ['datetime-range', 'date-range'].includes(this.mode)
			},
			// 是否显示小时
			showHour(): boolean {
				return ['datetime', 'time', 'hour-minute', 'hour-minute-second'].includes(this.mode) ||
					   ['datetime-range', 'time-range'].includes(this.mode)
			},
			// 是否显示分钟
			showMinute(): boolean {
				return ['datetime', 'time', 'hour-minute', 'hour-minute-second'].includes(this.mode) ||
					   ['datetime-range', 'time-range'].includes(this.mode)
			},
			// 是否显示秒
			showSecond(): boolean {
				return (this.showSeconds && ['datetime', 'time', 'hour-minute-second'].includes(this.mode)) ||
					   (this.showSeconds && ['datetime-range', 'time-range'].includes(this.mode))
			},
			// 指示器样式
			indicatorStyle(): string {
				return 'height: 44px; border-top: 1px solid #eee; border-bottom: 1px solid #eee;'
			},
			// 遮罩样式
			maskStyle(): string {
				return 'background-image: linear-gradient(180deg, rgba(255,255,255,0.95), rgba(255,255,255,0.6)), linear-gradient(0deg, rgba(255,255,255,0.95), rgba(255,255,255,0.6));'
			},
			// 显示标题
			displayTitle(): string {
				if (this.title != '选择时间') return this.title

				const modeMap: UTSJSONObject = {
					'datetime': '选择日期时间',
					'date': '选择日期',
					'time': '选择时间',
					'year': '选择年份',
					'year-month': '选择年月',
					'month': '选择月份',
					'day': '选择日期',
					'hour-minute': '选择时间',
					'hour-minute-second': '选择时间',
					'datetime-range': '选择日期时间范围',
					'date-range': '选择日期范围',
					'time-range': '选择时间范围'
				}

				const result = modeMap[this.mode] as string | null
				return result != null ? result : '选择时间'
			},
			// 当前显示值
			currentDisplayValue(): string {
				if (this.isRange) {
					const startFormatted: string = this.formatDate(this.rangeValues[0], this.mode.replace('-range', '') as DateTimeMode)
					const endFormatted: string = this.formatDate(this.rangeValues[1], this.mode.replace('-range', '') as DateTimeMode)
					return startFormatted + ' 至 ' + endFormatted
				} else {
					return this.formatDate(this.currentDate, this.mode)
				}
			}
		},
		created() {
			this.initData()
		},
		methods: {
			// 格式化日期
			formatDate(date: Date | null, type: DateTimeMode): string {
				if (date == null) return ''

				try {
					// 确保是 Date 对象
					const d: Date = date
					if (!this.validateDate(d)) return ''

					const year: number = d.getFullYear()
					const month: string = (d.getMonth() + 1).toString().padStart(2, '0')
					const day: string = d.getDate().toString().padStart(2, '0')
					const hour: string = d.getHours().toString().padStart(2, '0')
					const minute: string = d.getMinutes().toString().padStart(2, '0')
					const second: string = d.getSeconds().toString().padStart(2, '0')

					switch (type) {
						case 'datetime':
							return `${year}-${month}-${day} ${hour}:${minute}${this.showSeconds ? ':' + second : ''}`
						case 'date':
							return `${year}-${month}-${day}`
						case 'time':
							return `${hour}:${minute}${this.showSeconds ? ':' + second : ''}`
						case 'year':
							return `${year}`
						case 'year-month':
							return `${year}-${month}`
						case 'month':
							return month
						case 'day':
							return day
						case 'hour-minute':
							return `${hour}:${minute}`
						case 'hour-minute-second':
							return `${hour}:${minute}:${second}`
						default:
							return `${year}-${month}-${day} ${hour}:${minute}${this.showSeconds ? ':' + second : ''}`
					}
				} catch (error) {
					console.error('Format date error:', error, date)
					return ''
				}
			},

			// 解析日期
			parseDate(value: Date | string | number | null): Date {
				if (value == null) return new Date()

				try {
					let date: Date | null = null
					if (value instanceof Date) {
						// 如果已经是 Date 对象，创建一个新的副本
						date = new Date(value.getTime())
					} else if (typeof value == 'number' && !isNaN(value)) {
						// 数字类型，作为时间戳处理
						date = new Date(value as number)
					} else if (typeof value == 'string') {
						// 字符串类型，需要解析
						if (value.includes('T')) {
							// ISO 格式字符串
							date = new Date(value as string)
						} else if (value.includes('-') || value.includes('/')) {
							// 自定义格式字符串，手动解析
							const parts: number[] = value.split(/[-\s:/]/).map(p => parseInt(p))
							if (parts.length >= 3) {
								date = new Date(
									parts[0], // year
									parts[1] - 1, // month
									parts[2], // day
									parts.length > 3 ? parts[3] : 0, // hour
									parts.length > 4 ? parts[4] : 0, // minute
									parts.length > 5 ? parts[5] : 0 // second
								)
							}
						} else {
							// 尝试作为时间戳字符串解析
							const timestamp: number = parseInt(value)
							if (!isNaN(timestamp)) {
								date = new Date(timestamp as number)
							}
						}
					}

					return date != null && !isNaN(date.getTime()) ? date : new Date()
				} catch (error) {
					console.error('Parse date error:', error)
					return new Date()
				}
			},

			// 验证日期
			validateDate(date: Date): boolean {
				if (!(date instanceof Date) || isNaN(date.getTime())) {
					console.warn('Invalid date:', date)
					return false
				}

				const year: number = date.getFullYear()
				if (year < this.startYear || year > this.endYear) {
					console.warn('Date out of range:', date)
					return false
				}

				return true
			},

			// 显示选择器
			show(value?: Date | Date[] | string | number) {
				this.visible = true
				this.currentQuickIndex = -1
				this.rangeIndex = 0

				try {
					if (this.isRange) {
						// 处理区间值
						if (Array.isArray(value) && value.length == 2) {
							this.rangeValues = value.map(v => this.parseDate(v))
						} else if (typeof value == 'string') {
							// 尝试解析字符串格式的日期
							const date: Date = this.parseDate(value)
							this.rangeValues = [date, date]
						} else {
							const now: Date = new Date()
							this.rangeValues = [now, now]
						}
					} else {
						// 处理单个值
						this.currentDate = this.parseDate(value)
					}

					this.$nextTick(() => {
						this.initData()
						this.updateCurrentValue()
					})
				} catch (error) {
					console.error('Show picker error:', error)
					const now: Date = new Date()
					if (this.isRange) {
						this.rangeValues = [now, now]
					} else {
						this.currentDate = now
					}
				}
			},

			// 隐藏选择器
			hide() {
				this.visible = false
			},

			// 点击遮罩层关闭弹窗
			onOverlayClick() {
				this.hide()
				this.$emit('cancel')
			},

			// 取消按钮点击事件
			onCancel() {
				this.hide()
				this.$emit('cancel')
			},

			// 确定按钮点击事件
			onConfirm() {
				try {
					if (this.isRange) {
						if (!this.validateDate(this.rangeValues[0]) || !this.validateDate(this.rangeValues[1])) {
							uni.showToast({
								title: '日期格式无效',
								icon: 'none'
							})
							return
						}

						if (this.rangeValues[1] < this.rangeValues[0]) {
							uni.showToast({
								title: '结束时间不能早于开始时间',
								icon: 'none'
							})
							return
						}

						const value: Date[] = this.rangeValues.map(date => new Date(date.getTime()))
						const formatted: string = value.map(date => this.formatDate(date, this.mode.replace('-range', '') as DateTimeMode)).join(' 至 ')

						this.$emit('confirm', {
							value,
							formatted
						})
					} else {
						if (!this.validateDate(this.currentDate)) {
							uni.showToast({
								title: '日期格式无效',
								icon: 'none'
							})
							return
						}

						const value: Date = new Date(this.currentDate.getTime())
						const formatted: string = this.formatDate(value, this.mode)

						this.$emit('confirm', {
							value,
							formatted
						})
					}

					this.hide()
				} catch (error) {
					console.error('Confirm error:', error)
					uni.showToast({
						title: '操作失败',
						icon: 'none'
					})
				}
			},

			// 快捷选项选择
			onQuickSelect(option: QuickOption, index: number) {
				if (option == null || option.value == null) {
					console.warn('Invalid quick option:', option)
					return
				}

				this.currentQuickIndex = index
				this.rangeIndex = 0

				try {
					if (this.isRange) {
						// 处理区间值
						let rangeValue: Date[] = option.value as Date[]
						if (!Array.isArray(rangeValue)) {
							// 如果不是数组，尝试转换
							const date: Date = this.parseDate(rangeValue)
							rangeValue = [date, date]
						}

						if (rangeValue.length != 2) {
							console.warn('Quick option value should have 2 items for range mode:', option)
							return
						}

						this.rangeValues = rangeValue.map(v => this.parseDate(v))
					} else {
						// 处理单个值
						this.currentDate = this.parseDate(option.value as Date)
					}

					this.$nextTick(() => {
						this.initData()
						this.updateCurrentValue()
					})

					if (option.autoConfirm == true) {
						this.onConfirm()
					}
				} catch (error) {
					console.error('Quick select error:', error)
					uni.showToast({
						title: '快捷选项格式无效',
						icon: 'none'
					})
				}
			},

			// picker-view 变化事件
			onPickerChange(e: UniPickerViewChangeEvent) {
				this.pickerValue = e.detail.value
				this.currentQuickIndex = -1
				this.updateDateFromValue()
			},

			// 区间选择切换
			onRangeChange(index: number) {
				if (this.rangeIndex == index) return
				this.rangeIndex = index
				this.$nextTick(() => {
					this.updateCurrentValue()
				})
			},

			// 初始化数据
			initData() {
				if (!this.isInitialized) {
					// 年
					this.years = []
					for (let i: number = this.startYear; i <= this.endYear; i++) {
						this.years.push(i.toString())
					}

					// 月
					this.months = []
					for (let i: number = 1; i <= 12; i++) {
						this.months.push(i.toString().padStart(2, '0'))
					}

					// 时
					this.hours = []
					for (let i: number = 0; i <= 23; i++) {
						this.hours.push(i.toString().padStart(2, '0'))
					}

					// 分
					this.minutes = []
					for (let i: number = 0; i <= 59; i++) {
						this.minutes.push(i.toString().padStart(2, '0'))
					}

					// 秒
					this.seconds = []
					for (let i: number = 0; i <= 59; i++) {
						this.seconds.push(i.toString().padStart(2, '0'))
					}

					this.isInitialized = true
				}

				// 日，需要根据年月动态计算
				const date: Date = this.isRange ? this.rangeValues[this.rangeIndex] : this.currentDate
				const year: number = date.getFullYear()
				const month: number = date.getMonth() + 1
				const daysInMonth: number = new Date(year, month, 0).getDate()

				this.days = []
				for (let i: number = 1; i <= daysInMonth; i++) {
					this.days.push(i.toString().padStart(2, '0'))
				}
			},

			// 更新当前值
			updateCurrentValue() {
				const date: Date = this.isRange ? this.rangeValues[this.rangeIndex] : this.currentDate
				if (date == null || isNaN(date.getTime())) {
					console.warn('Invalid date in updateCurrentValue:', date)
					return
				}

				const values: number[] = []

				if (this.showYear) {
					const yearIndex: number = this.years.findIndex(y => parseInt(y) == date.getFullYear())
					values.push(yearIndex >= 0 ? yearIndex : 0)
				}

				if (this.showMonth) {
					const monthStr: string = (date.getMonth() + 1).toString().padStart(2, '0')
					const monthIndex: number = this.months.findIndex(m => m == monthStr)
					values.push(monthIndex >= 0 ? monthIndex : 0)
				}

				if (this.showDay) {
					const dayStr: string = date.getDate().toString().padStart(2, '0')
					const dayIndex: number = this.days.findIndex(d => d == dayStr)
					values.push(dayIndex >= 0 ? dayIndex : 0)
				}

				if (this.showHour) {
					const hourStr: string = date.getHours().toString().padStart(2, '0')
					const hourIndex: number = this.hours.findIndex(h => h == hourStr)
					values.push(hourIndex >= 0 ? hourIndex : 0)
				}

				if (this.showMinute) {
					const minuteStr: string = date.getMinutes().toString().padStart(2, '0')
					const minuteIndex: number = this.minutes.findIndex(m => m == minuteStr)
					values.push(minuteIndex >= 0 ? minuteIndex : 0)
				}

				if (this.showSecond) {
					const secondStr: string = date.getSeconds().toString().padStart(2, '0')
					const secondIndex: number = this.seconds.findIndex(s => s == secondStr)
					values.push(secondIndex >= 0 ? secondIndex : 0)
				}

				this.pickerValue = [...values]
			},

			// 从picker值更新日期
			updateDateFromValue() {
				if (!Array.isArray(this.pickerValue)) return

				let index: number = 0
				let year: number = this.currentDate.getFullYear()
				let month: number = this.currentDate.getMonth()
				let day: number = this.currentDate.getDate()
				let hour: number = this.currentDate.getHours()
				let minute: number = this.currentDate.getMinutes()
				let second: number = this.currentDate.getSeconds()

				if (this.showYear && this.pickerValue[index] != null) {
					year = parseInt(this.years[this.pickerValue[index]])
					index++
				}

				if (this.showMonth && this.pickerValue[index] != null) {
					month = parseInt(this.months[this.pickerValue[index]]) - 1
					index++
				}

				if (this.showDay && this.pickerValue[index] != null) {
					day = parseInt(this.days[this.pickerValue[index]])
					index++
				}

				if (this.showHour && this.pickerValue[index] != null) {
					hour = parseInt(this.hours[this.pickerValue[index]])
					index++
				}

				if (this.showMinute && this.pickerValue[index] != null) {
					minute = parseInt(this.minutes[this.pickerValue[index]])
					index++
				}

				if (this.showSecond && this.pickerValue[index] != null) {
					second = parseInt(this.seconds[this.pickerValue[index]])
				}

				const newDate: Date = new Date(year, month, day, hour, minute, second)

				if (this.isRange) {
					this.rangeValues[this.rangeIndex] = newDate
					if (this.rangeIndex == 0 && this.rangeValues[1] < newDate) {
						this.rangeValues[1] = new Date(newDate.getTime())
					}
				} else {
					this.currentDate = newDate
				}

				this.initData()
			}
		}
	}
	</script>

	<style>
		/* 弹窗遮罩层 */
		.picker-overlay {
			position: fixed;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background-color: rgba(0, 0, 0, 0.5);
			display: flex;
			align-items: center;
			justify-content: center;
			z-index: 1000;
		}

		.picker-modal {
			width: 90%;
			max-width: 600rpx;
			background-color: #ffffff;
			border-radius: 20rpx;
			overflow: hidden;
			box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
		}

		.datetime-picker-container {
			width: 100%;
			background-color: #ffffff;
			display: flex;
			flex-direction: column;
		}

		/* 导航栏样式 */
		.navbar {
			height: 44px;
			background-color: #f8f8f8;
			border-bottom: 1px solid #e5e5e5;
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: space-between;
			padding: 0 10px;
		}

		.nav-btn {
			font-size: 16px;
			color: #007aff;
			padding: 8px 12px;
		}

		.cancel-btn {
			color: #999999;
		}

		.confirm-btn-container {
			height: 30px;
			background-color: #007aff;
			border-radius: 8rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);
		}

		.confirm-btn {
			color: #ffffff;
			font-weight: bold;
		}

		.nav-title {
			font-size: 17px;
			color: #333333;
		}

		/* 快捷选项样式 */
		.quick-options {
			display: flex;
			flex-wrap: wrap;
			padding: 10rpx 20rpx;
			border-bottom: 1rpx solid #eee;
		}

		.quick-item {
			padding: 6rpx 20rpx;
			margin: 6rpx;
			font-size: 24rpx;
			color: #666;
			background-color: #f5f5f5;
			border-radius: 6rpx;
		}

		.quick-item-active {
			color: #ffffff;
			background-color: #007AFF;
		}

		/* 区间选择标签样式 */
		.range-tabs {
			display: flex;
			padding: 20rpx;
			border-bottom: 1rpx solid #eee;
		}

		.range-tab {
			flex: 1;
			text-align: center;
			font-size: 28rpx;
			color: #666;
			padding: 10rpx 0;
		}

		.range-tab-active {
			color: #007AFF;
			position: relative;
		}

		/* picker-view 样式 */
		.picker-body {
			position: relative;
		}

		.picker-view {
			width: 100%;
			height: 264px;
		}

		.picker-item {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 44px;
			overflow: hidden;
		}

		.picker-text {
			font-size: 16px;
			color: #333;
		}

		/* 当前选择显示区域 */
		.current-selection {
			padding: 20rpx;
			background-color: #f8f9fa;
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: center;
		}

		.selection-label {
			font-size: 28rpx;
			color: #666666;
			margin-right: 10rpx;
		}

		.selection-value {
			font-size: 32rpx;
			color: #007aff;
			font-weight: bold;
		}
	</style>