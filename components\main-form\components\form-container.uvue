<template>
	<view class="form-container-body">
		<view class="form-container">
			<view class="form-container-label" v-if="label">
				<view class="form-container-name" >
					
					<text class="form-container-name-text" :style="{color: labelColor}">{{ label }}</text>
					<view class="form-container-tip-icon" v-if="tip!=''" @click="showTipModal">
						<text class="form-container-tip-text">!</text>
					</view>
				</view>
			</view>

			<view class="form-container-box" :style="{
				backgroundColor: backgroundColor,
				borderColor: showError ? 'red' : '#fff'
			}">
				<slot name="input-content"></slot>
			</view> 
		</view>

		<view class="form-container-tip" v-if="showError">
			<text class="form-container-error">{{ errorMessage }}</text>
		</view>
	</view>
</template>

<script lang="uts">
	export default {
		name: "FormContainer",
		props: {
			label: {
				type: String,
				default: ""
			},
			showError: {
				type: Boolean,
				default: false
			},
			errorMessage: {
				type: String,
				default: ""
			},
			tip: {
				type: String,
				default: ""
			},
			labelColor: {
				type: String,
				default: "#000"
			},
			backgroundColor: {
				type: String,
				default: "#f1f4f9"
			}
		},
		methods: {
			showTipModal() {
				if (this.tip!="") {
					uni.showModal({
						title: '提示',
						content: this.tip,
						showCancel: false,
						confirmText: '知道了'
					})
				}
			}
		}
	}
</script>

<style>
	.form-container-body {
		width: 100%;
		display: flex;
		flex-direction: column;
		padding: 0 20rpx;
		margin-bottom: 20rpx;
	}

	.form-container {
		width: 100%;
		display: flex;
		flex-direction: column;
	}

	.form-container-label {
		width: 100%;
		margin-bottom: 10rpx;
	}

	.form-container-name {
		
		display: flex;
		flex-direction: row;
		align-items: center;
	}
	.form-container-name-text{
		font-size: 30rpx;
	}
	.form-container-tip-icon {
		width: 40rpx;
		height: 40rpx;
		background-color: rgba(0, 0, 0, 0.05);
		border-radius: 20rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		margin-left: 10rpx;
	}

	.form-container-tip-text {
		font-size: 24rpx;
		color: #666;
		font-weight: bold;
	}

	.form-container-box {
		width: 100%;
		border: 1rpx solid #fff;
		box-sizing: border-box;
		padding: 20rpx;
		border-radius: 20rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
	}

	.form-container-tip {
		width: 100%;
		
		margin-top: 10rpx;
	}

	.form-container-error {
		color: red;
		font-size: 28rpx;
		text-align: right;
	}

	/* 通用输入元素样式类 */
	.form-input-element {
		flex: 1;
		min-height: 60rpx;
	}

	/* 阴影样式 */
	.qShadow1 {
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	}
</style>
