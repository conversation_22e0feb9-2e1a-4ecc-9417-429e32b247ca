@file:Suppress("UNCHECKED_CAST", "USELESS_CAST", "INAPPLICABLE_JVM_NAME", "UNUSED_ANONYMOUS_PARAMETER", "NAME_SHADOWING", "UNNECESSARY_NOT_NULL_ASSERTION")
package uni.UNIC178CB1
import io.dcloud.uniapp.*
import io.dcloud.uniapp.extapi.*
import io.dcloud.uniapp.framework.*
import io.dcloud.uniapp.runtime.*
import io.dcloud.uniapp.vue.*
import io.dcloud.uniapp.vue.shared.*
import io.dcloud.unicloud.*
import io.dcloud.uts.*
import io.dcloud.uts.Map
import io.dcloud.uts.Set
import io.dcloud.uts.UTSAndroid
import io.dcloud.uniapp.extapi.getStorage as uni_getStorage
import io.dcloud.uniapp.extapi.setStorageSync as uni_setStorageSync
import io.dcloud.uniapp.extapi.showModal as uni_showModal
open class GenComponentsMainFormComponentsFormTimePicker : VueComponent {
    constructor(__ins: ComponentInternalInstance) : super(__ins) {
        onCreated(fun(): Unit {
            this.initializeValue()
        }
        , __ins)
        this.`$watch`(fun(): Any? {
            return this.data
        }
        , fun(newValue: FormFieldData7): Unit {
            this.selectedTime = newValue.value || ""
        }
        , WatchOptions(deep = true, immediate = true))
    }
    @Suppress("UNUSED_PARAMETER", "UNUSED_VARIABLE")
    override fun `$render`(): Any? {
        val _ctx = this
        val _cache = this.`$`.renderCache
        return _cE("view", _uM("class" to "form-time-picker-container"), _uA(
            _cE("view", _uM("class" to "form-time-picker-label", "style" to _nS(_uM("color" to _ctx.color))), _uA(
                _cE("text", null, _tD(_ctx.data.name), 1)
            ), 4),
            _cE("view", _uM("class" to "form-time-picker-box", "style" to _nS(_uM("backgroundColor" to _ctx.bgColor)), "onClick" to _ctx.showTimePicker), _uA(
                _cE("text", _uM("class" to _nC(_uA(
                    "form-time-picker-text",
                    _uM("placeholder" to (_ctx.selectedTime == ""))
                ))), _tD(_ctx.selectedTime || _ctx.data.tip || "请选择时间"), 3),
                _cE("text", _uM("class" to "form-time-picker-icon"), "🕐")
            ), 12, _uA(
                "onClick"
            ))
        ))
    }
    open var data: FormFieldData7 by `$props`
    open var index: Number by `$props`
    open var color: String by `$props`
    open var bgColor: String by `$props`
    open var keyName: String by `$props`
    open var selectedTime: String by `$data`
    open var saveKey: String by `$data`
    @Suppress("USELESS_CAST")
    override fun data(): Map<String, Any?> {
        return _uM("selectedTime" to "", "saveKey" to "")
    }
    open var initializeValue = ::gen_initializeValue_fn
    open fun gen_initializeValue_fn(): Unit {
        if (this.data.isSave == true && this.keyName != "") {
            this.saveKey = this.keyName + "_" + this.data.key
            uni_getStorage(GetStorageOptions(key = this.saveKey, success = fun(res): Unit {
                this.selectedTime = res.data || ""
                this.emitChange(res.data)
            }, fail = fun(_): Unit {
                this.selectedTime = this.data.value || ""
            }))
        } else {
            this.selectedTime = this.data.value || ""
        }
    }
    open var getCurrentTime = ::gen_getCurrentTime_fn
    open fun gen_getCurrentTime_fn(): String {
        val now = Date()
        val hours = String(now.getHours()).padStart(2, "0")
        val minutes = String(now.getMinutes()).padStart(2, "0")
        return "" + hours + ":" + minutes
    }
    open var showTimePicker = ::gen_showTimePicker_fn
    open fun gen_showTimePicker_fn(): Unit {
        uni_showModal(ShowModalOptions(title = this.data.name, content = "请在系统时间选择器中选择时间", showCancel = true, success = fun(res): Unit {
            if (res.confirm) {
                val currentTime = this.getCurrentTime()
                this.handleTimeSelection(currentTime)
            }
        }
        ))
    }
    open var handleTimeSelection = ::gen_handleTimeSelection_fn
    open fun gen_handleTimeSelection_fn(time: String): Unit {
        this.selectedTime = time
        this.emitChange(time)
        this.saveValue(time)
    }
    open var emitChange = ::gen_emitChange_fn
    open fun gen_emitChange_fn(value: String): Unit {
        this.`$emit`("change", _uO("index" to this.index, "value" to value))
    }
    open var saveValue = ::gen_saveValue_fn
    open fun gen_saveValue_fn(value: String): Unit {
        if (this.data.isSave == true && this.saveKey != "") {
            try {
                uni_setStorageSync(this.saveKey, value)
            }
             catch (e: Throwable) {
                console.error("保存数据失败:", e, " at components/main-form/components/form-time-picker.uvue:143")
            }
        }
    }
    companion object {
        var name = "FormTimePicker"
        val styles: Map<String, Map<String, Map<String, Any>>> by lazy {
            _nCS(_uA(
                styles0
            ))
        }
        val styles0: Map<String, Map<String, Map<String, Any>>>
            get() {
                return _uM("form-time-picker-container" to _pS(_uM("width" to "100%", "display" to "flex", "flexDirection" to "column", "paddingTop" to 0, "paddingRight" to "20rpx", "paddingBottom" to 0, "paddingLeft" to "20rpx", "marginBottom" to "20rpx")), "form-time-picker-label" to _pS(_uM("width" to "100%", "marginBottom" to "10rpx", "fontSize" to "32rpx")), "form-time-picker-box" to _pS(_uM("width" to "100%", "height" to "100rpx", "borderTopWidth" to "1rpx", "borderRightWidth" to "1rpx", "borderBottomWidth" to "1rpx", "borderLeftWidth" to "1rpx", "borderTopStyle" to "solid", "borderRightStyle" to "solid", "borderBottomStyle" to "solid", "borderLeftStyle" to "solid", "borderTopColor" to "#e0e0e0", "borderRightColor" to "#e0e0e0", "borderBottomColor" to "#e0e0e0", "borderLeftColor" to "#e0e0e0", "boxSizing" to "border-box", "paddingTop" to 0, "paddingRight" to "20rpx", "paddingBottom" to 0, "paddingLeft" to "20rpx", "borderTopLeftRadius" to "20rpx", "borderTopRightRadius" to "20rpx", "borderBottomRightRadius" to "20rpx", "borderBottomLeftRadius" to "20rpx", "display" to "flex", "flexDirection" to "row", "alignItems" to "center", "justifyContent" to "space-between")), "form-time-picker-text" to _uM("" to _uM("flex" to 1, "fontSize" to "30rpx", "color" to "#333333"), ".placeholder" to _uM("color" to "#999999")), "form-time-picker-icon" to _pS(_uM("fontSize" to "32rpx", "color" to "#666666")))
            }
        var inheritAttrs = true
        var inject: Map<String, Map<String, Any?>> = _uM()
        var emits: Map<String, Any?> = _uM()
        var props = _nP(_uM("data" to _uM("type" to "Object", "default" to fun(): FormFieldData7 {
            return (FormFieldData7(key = "", name = "", type = "time", value = ""))
        }
        ), "index" to _uM("type" to "Number", "default" to 0), "color" to _uM("type" to "String", "default" to "#333333"), "bgColor" to _uM("type" to "String", "default" to "#f8f9fa"), "keyName" to _uM("type" to "String", "default" to "")))
        var propsNeedCastKeys = _uA(
            "data",
            "index",
            "color",
            "bgColor",
            "keyName"
        )
        var components: Map<String, CreateVueComponent> = _uM()
    }
}
