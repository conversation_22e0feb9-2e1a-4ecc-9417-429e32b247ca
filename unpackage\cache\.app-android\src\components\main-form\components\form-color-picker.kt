@file:Suppress("UNCHECKED_CAST", "USELESS_CAST", "INAPPLICABLE_JVM_NAME", "UNUSED_ANONYMOUS_PARAMETER", "NAME_SHADOWING", "UNNECESSARY_NOT_NULL_ASSERTION")
package uni.UNIC178CB1
import io.dcloud.uniapp.*
import io.dcloud.uniapp.extapi.*
import io.dcloud.uniapp.framework.*
import io.dcloud.uniapp.runtime.*
import io.dcloud.uniapp.vue.*
import io.dcloud.uniapp.vue.shared.*
import io.dcloud.unicloud.*
import io.dcloud.uts.*
import io.dcloud.uts.Map
import io.dcloud.uts.Set
import io.dcloud.uts.UTSAndroid
import io.dcloud.uniapp.extapi.getStorage as uni_getStorage
import io.dcloud.uniapp.extapi.setStorageSync as uni_setStorageSync
open class GenComponentsMainFormComponentsFormColorPicker : VueComponent {
    constructor(__ins: ComponentInternalInstance) : super(__ins) {
        onCreated(fun(): Unit {
            this.initializeValue()
        }
        , __ins)
        this.`$watch`(fun(): Any? {
            return this.data
        }
        , fun(newValue: FormFieldData8): Unit {
            this.selectedColor = newValue.value || ""
            this.tempColor = this.selectedColor
        }
        , WatchOptions(deep = true, immediate = true))
    }
    @Suppress("UNUSED_PARAMETER", "UNUSED_VARIABLE")
    override fun `$render`(): Any? {
        val _ctx = this
        val _cache = this.`$`.renderCache
        val _component_main_color_picker = resolveEasyComponent("main-color-picker", GenComponentsMainColorPickerMainColorPickerClass)
        return _cE("view", _uM("class" to "form-color-picker-container"), _uA(
            _cE("view", _uM("class" to "form-color-picker-label", "style" to _nS(_uM("color" to _ctx.color))), _uA(
                _cE("text", null, _tD(_ctx.data.name), 1)
            ), 4),
            _cE("view", _uM("class" to "form-color-picker-box", "style" to _nS(_uM("backgroundColor" to _ctx.bgColor)), "onClick" to _ctx.showColorPicker), _uA(
                _cE("view", _uM("class" to "form-color-preview", "style" to _nS(_uM("backgroundColor" to (_ctx.selectedColor || "#ffffff")))), null, 4),
                _cE("text", _uM("class" to _nC(_uA(
                    "form-color-picker-text",
                    _uM("placeholder" to (_ctx.selectedColor == ""))
                ))), _tD(_ctx.selectedColor || _ctx.data.tip || "请选择颜色"), 3),
                _cE("text", _uM("class" to "form-color-picker-icon"), "🎨")
            ), 12, _uA(
                "onClick"
            )),
            if (isTrue(_ctx.showModal)) {
                _cV(_component_main_color_picker, _uM("key" to 0, "show" to _ctx.showModal, "color" to _ctx.tempColor, "onConfirm" to _ctx.handleColorConfirm, "onCancel" to _ctx.hideColorPicker), null, 8, _uA(
                    "show",
                    "color",
                    "onConfirm",
                    "onCancel"
                ))
            } else {
                _cC("v-if", true)
            }
        ))
    }
    open var data: FormFieldData8 by `$props`
    open var index: Number by `$props`
    open var color: String by `$props`
    open var bgColor: String by `$props`
    open var keyName: String by `$props`
    open var selectedColor: String by `$data`
    open var tempColor: String by `$data`
    open var showModal: Boolean by `$data`
    open var customColorInput: String by `$data`
    open var customColorPreview: String by `$data`
    open var saveKey: String by `$data`
    open var presetColors: UTSArray<String> by `$data`
    @Suppress("USELESS_CAST")
    override fun data(): Map<String, Any?> {
        return _uM("selectedColor" to "", "tempColor" to "", "showModal" to false, "customColorInput" to "", "customColorPreview" to "#ffffff", "saveKey" to "", "presetColors" to _uA(
            "#FF0000",
            "#FF8000",
            "#FFFF00",
            "#80FF00",
            "#00FF00",
            "#00FF80",
            "#00FFFF",
            "#0080FF",
            "#0000FF",
            "#8000FF",
            "#FF00FF",
            "#FF0080",
            "#000000",
            "#404040",
            "#808080",
            "#C0C0C0",
            "#FFFFFF",
            "#FFE4E1",
            "#FFA500",
            "#FFFF99",
            "#90EE90",
            "#87CEEB",
            "#DDA0DD",
            "#F0E68C"
        ))
    }
    open var initializeValue = ::gen_initializeValue_fn
    open fun gen_initializeValue_fn(): Unit {
        if (this.data.isSave == true && this.keyName != "") {
            this.saveKey = this.keyName + "_" + this.data.key
            uni_getStorage(GetStorageOptions(key = this.saveKey, success = fun(res): Unit {
                this.selectedColor = res.data || ""
                this.tempColor = this.selectedColor
                this.emitChange(res.data)
            }, fail = fun(_): Unit {
                this.selectedColor = this.data.value || ""
                this.tempColor = this.selectedColor
            }))
        } else {
            this.selectedColor = this.data.value || ""
            this.tempColor = this.selectedColor
        }
    }
    open var showColorPicker = ::gen_showColorPicker_fn
    open fun gen_showColorPicker_fn(): Unit {
        this.tempColor = this.selectedColor
        this.customColorInput = this.selectedColor.replace("#", "")
        this.customColorPreview = this.selectedColor || "#ffffff"
        this.showModal = true
    }
    open var hideColorPicker = ::gen_hideColorPicker_fn
    open fun gen_hideColorPicker_fn(): Unit {
        this.showModal = false
    }
    open var selectPresetColor = ::gen_selectPresetColor_fn
    open fun gen_selectPresetColor_fn(color: String): Unit {
        this.tempColor = color
        this.customColorInput = color.replace("#", "")
        this.customColorPreview = color
    }
    open var handleCustomColorInput = ::gen_handleCustomColorInput_fn
    open fun gen_handleCustomColorInput_fn(event: ColorInputEvent): Unit {
        val value = event.detail.value.toUpperCase()
        this.customColorInput = value
        if (UTSRegExp("^[0-9A-F]{6}\$", "").test(value)) {
            val color = "#" + value
            this.customColorPreview = color
            this.tempColor = color
        } else if (value == "") {
            this.customColorPreview = "#ffffff"
            this.tempColor = ""
        }
    }
    open var confirmColor = ::gen_confirmColor_fn
    open fun gen_confirmColor_fn(): Unit {
        this.selectedColor = this.tempColor
        this.emitChange(this.selectedColor)
        this.saveValue(this.selectedColor)
        this.hideColorPicker()
    }
    open var emitChange = ::gen_emitChange_fn
    open fun gen_emitChange_fn(value: String): Unit {
        this.`$emit`("change", _uO("index" to this.index, "value" to value))
    }
    open var saveValue = ::gen_saveValue_fn
    open fun gen_saveValue_fn(value: String): Unit {
        if (this.data.isSave == true && this.saveKey != "") {
            try {
                uni_setStorageSync(this.saveKey, value)
            }
             catch (e: Throwable) {
                console.error("保存数据失败:", e, " at components/main-form/components/form-color-picker.uvue:185")
            }
        }
    }
    companion object {
        var name = "FormColorPicker"
        val styles: Map<String, Map<String, Map<String, Any>>> by lazy {
            _nCS(_uA(
                styles0
            ))
        }
        val styles0: Map<String, Map<String, Map<String, Any>>>
            get() {
                return _uM("form-color-picker-container" to _pS(_uM("width" to "100%", "display" to "flex", "flexDirection" to "column", "paddingTop" to 0, "paddingRight" to "20rpx", "paddingBottom" to 0, "paddingLeft" to "20rpx", "marginBottom" to "20rpx")), "form-color-picker-label" to _pS(_uM("width" to "100%", "marginBottom" to "10rpx")), "form-color-picker-box" to _pS(_uM("width" to "100%", "height" to "100rpx", "borderTopWidth" to "1rpx", "borderRightWidth" to "1rpx", "borderBottomWidth" to "1rpx", "borderLeftWidth" to "1rpx", "borderTopStyle" to "solid", "borderRightStyle" to "solid", "borderBottomStyle" to "solid", "borderLeftStyle" to "solid", "borderTopColor" to "#e0e0e0", "borderRightColor" to "#e0e0e0", "borderBottomColor" to "#e0e0e0", "borderLeftColor" to "#e0e0e0", "boxSizing" to "border-box", "paddingTop" to 0, "paddingRight" to "20rpx", "paddingBottom" to 0, "paddingLeft" to "20rpx", "borderTopLeftRadius" to "20rpx", "borderTopRightRadius" to "20rpx", "borderBottomRightRadius" to "20rpx", "borderBottomLeftRadius" to "20rpx", "display" to "flex", "flexDirection" to "row", "alignItems" to "center")), "form-color-preview" to _pS(_uM("width" to "60rpx", "height" to "60rpx", "borderTopLeftRadius" to "10rpx", "borderTopRightRadius" to "10rpx", "borderBottomRightRadius" to "10rpx", "borderBottomLeftRadius" to "10rpx", "borderTopWidth" to "1rpx", "borderRightWidth" to "1rpx", "borderBottomWidth" to "1rpx", "borderLeftWidth" to "1rpx", "borderTopStyle" to "solid", "borderRightStyle" to "solid", "borderBottomStyle" to "solid", "borderLeftStyle" to "solid", "borderTopColor" to "#e0e0e0", "borderRightColor" to "#e0e0e0", "borderBottomColor" to "#e0e0e0", "borderLeftColor" to "#e0e0e0", "marginRight" to "20rpx")), "form-color-picker-text" to _uM("" to _uM("flex" to 1, "fontSize" to "30rpx", "color" to "#333333"), ".placeholder" to _uM("color" to "#999999")), "form-color-picker-icon" to _pS(_uM("fontSize" to "32rpx", "color" to "#666666")), "form-color-modal" to _pS(_uM("position" to "fixed", "top" to 0, "left" to 0, "right" to 0, "bottom" to 0, "backgroundColor" to "rgba(0,0,0,0.5)", "display" to "flex", "justifyContent" to "center", "alignItems" to "flex-end", "zIndex" to 1000)), "form-color-popup" to _pS(_uM("width" to "100%", "backgroundColor" to "#ffffff", "borderTopLeftRadius" to "20rpx", "borderTopRightRadius" to "20rpx", "borderBottomRightRadius" to 0, "borderBottomLeftRadius" to 0, "display" to "flex", "flexDirection" to "column")), "form-color-header" to _pS(_uM("height" to "100rpx", "display" to "flex", "flexDirection" to "row", "alignItems" to "center", "justifyContent" to "space-between", "paddingTop" to 0, "paddingRight" to "30rpx", "paddingBottom" to 0, "paddingLeft" to "30rpx", "borderBottomWidth" to "1rpx", "borderBottomStyle" to "solid", "borderBottomColor" to "#e0e0e0")), "form-color-cancel" to _pS(_uM("fontSize" to "32rpx", "color" to "#1F6ED4")), "form-color-confirm" to _pS(_uM("fontSize" to "32rpx", "color" to "#1F6ED4")), "form-color-title" to _pS(_uM("fontSize" to "34rpx", "color" to "#333333")), "form-color-content" to _pS(_uM("flex" to 1, "paddingTop" to "30rpx", "paddingRight" to "30rpx", "paddingBottom" to "30rpx", "paddingLeft" to "30rpx", "overflowY" to "auto")), "form-color-section-title" to _pS(_uM("fontSize" to "30rpx", "color" to "#333333", "marginBottom" to "20rpx")), "form-color-preset-grid" to _pS(_uM("display" to "flex", "flexDirection" to "row", "flexWrap" to "wrap", "gap" to "20rpx", "marginBottom" to "40rpx")), "form-color-preset-item" to _uM("" to _uM("width" to "80rpx", "height" to "80rpx", "borderTopLeftRadius" to "10rpx", "borderTopRightRadius" to "10rpx", "borderBottomRightRadius" to "10rpx", "borderBottomLeftRadius" to "10rpx", "borderTopWidth" to "2rpx", "borderRightWidth" to "2rpx", "borderBottomWidth" to "2rpx", "borderLeftWidth" to "2rpx", "borderTopStyle" to "solid", "borderRightStyle" to "solid", "borderBottomStyle" to "solid", "borderLeftStyle" to "solid", "borderTopColor" to "#e0e0e0", "borderRightColor" to "#e0e0e0", "borderBottomColor" to "#e0e0e0", "borderLeftColor" to "#e0e0e0", "display" to "flex", "justifyContent" to "center", "alignItems" to "center", "position" to "relative"), ".selected" to _uM("borderTopColor" to "#1F6ED4", "borderRightColor" to "#1F6ED4", "borderBottomColor" to "#1F6ED4", "borderLeftColor" to "#1F6ED4", "borderTopWidth" to "4rpx", "borderRightWidth" to "4rpx", "borderBottomWidth" to "4rpx", "borderLeftWidth" to "4rpx")), "form-color-check" to _pS(_uM("color" to "#ffffff", "textShadow" to "1rpx 1rpx 2rpx rgba(0, 0, 0, 0.5)")), "form-color-input-box" to _pS(_uM("display" to "flex", "flexDirection" to "row", "alignItems" to "center", "borderTopWidth" to "1rpx", "borderRightWidth" to "1rpx", "borderBottomWidth" to "1rpx", "borderLeftWidth" to "1rpx", "borderTopStyle" to "solid", "borderRightStyle" to "solid", "borderBottomStyle" to "solid", "borderLeftStyle" to "solid", "borderTopColor" to "#e0e0e0", "borderRightColor" to "#e0e0e0", "borderBottomColor" to "#e0e0e0", "borderLeftColor" to "#e0e0e0", "borderTopLeftRadius" to "10rpx", "borderTopRightRadius" to "10rpx", "borderBottomRightRadius" to "10rpx", "borderBottomLeftRadius" to "10rpx", "paddingTop" to 0, "paddingRight" to "20rpx", "paddingBottom" to 0, "paddingLeft" to "20rpx", "height" to "80rpx", "marginBottom" to "20rpx")), "form-color-hash" to _pS(_uM("fontSize" to "30rpx", "color" to "#666666", "marginRight" to "10rpx")), "form-color-input" to _pS(_uM("flex" to 1, "height" to "100%", "fontSize" to "30rpx")), "form-color-custom-preview" to _pS(_uM("width" to "100%", "height" to "80rpx", "borderTopLeftRadius" to "10rpx", "borderTopRightRadius" to "10rpx", "borderBottomRightRadius" to "10rpx", "borderBottomLeftRadius" to "10rpx", "borderTopWidth" to "1rpx", "borderRightWidth" to "1rpx", "borderBottomWidth" to "1rpx", "borderLeftWidth" to "1rpx", "borderTopStyle" to "solid", "borderRightStyle" to "solid", "borderBottomStyle" to "solid", "borderLeftStyle" to "solid", "borderTopColor" to "#e0e0e0", "borderRightColor" to "#e0e0e0", "borderBottomColor" to "#e0e0e0", "borderLeftColor" to "#e0e0e0")))
            }
        var inheritAttrs = true
        var inject: Map<String, Map<String, Any?>> = _uM()
        var emits: Map<String, Any?> = _uM()
        var props = _nP(_uM("data" to _uM("type" to "Object", "default" to fun(): FormFieldData8 {
            return (FormFieldData8(key = "", name = "", type = "color", value = ""))
        }
        ), "index" to _uM("type" to "Number", "default" to 0), "color" to _uM("type" to "String", "default" to "#333333"), "bgColor" to _uM("type" to "String", "default" to "#f8f9fa"), "keyName" to _uM("type" to "String", "default" to "")))
        var propsNeedCastKeys = _uA(
            "data",
            "index",
            "color",
            "bgColor",
            "keyName"
        )
        var components: Map<String, CreateVueComponent> = _uM()
    }
}
