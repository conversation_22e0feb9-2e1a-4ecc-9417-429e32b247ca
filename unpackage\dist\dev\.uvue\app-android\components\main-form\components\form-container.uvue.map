{"version": 3, "sources": ["components/main-form/components/form-container.uvue"], "names": [], "mappings": "AA4BC,MAAK,OAAQ,GAAE,eAAA,CAAA;IACd,IAAI,EAAE,eAAe;IACrB,KAAK,EAAE;QACN,KAAK,EAAE;YACN,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,EAAC;SACV;QACD,SAAS,EAAE;YACV,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,KAAI;SACb;QACD,YAAY,EAAE;YACb,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,EAAC;SACV;QACD,GAAG,EAAE;YACJ,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,EAAC;SACV;QACD,UAAU,EAAE;YACX,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,MAAK;SACd;QACD,eAAe,EAAE;YAChB,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,SAAQ;SAClB;KACA;IACD,OAAO,EAAE;QACR,YAAY;YACX,IAAI,IAAI,CAAC,GAAG,IAAE,EAAE,EAAE;gBACjB,GAAG,CAAC,SAAS,CAAC;oBACb,KAAK,EAAE,IAAI;oBACX,OAAO,EAAE,IAAI,CAAC,GAAG;oBACjB,UAAU,EAAE,KAAK;oBACjB,WAAW,EAAE,KAAI;iBACjB,CAAA,CAAA;aACF;QACD,CAAA;KACD;CACD,CAAA,CAAA;;;;;WAnEA,GAAA,CAuBO,MAAA,EAAA,GAAA,CAAA,EAvBD,KAAK,EAAC,qBAAqB,EAAA,CAAA,EAAA;QAChC,GAAA,CAiBO,MAAA,EAAA,GAAA,CAAA,EAjBD,KAAK,EAAC,gBAAgB,EAAA,CAAA,EAAA;mBACc,IAAA,CAAA,KAAK,CAAA;kBAA9C,GAAA,CAQO,MAAA,EAAA,GAAA,CAAA;;oBARD,KAAK,EAAC,sBAAsB;;oBACjC,GAAA,CAMO,MAAA,EAAA,GAAA,CAAA,EAND,KAAK,EAAC,qBAAqB,EAAA,CAAA,EAAA;wBAEhC,GAAA,CAAsF,MAAA,EAAA,GAAA,CAAA;4BAAhF,KAAK,EAAC,0BAA0B;4BAAE,KAAK,EAAA,GAAA,CAAE,GAAA,CAAA,EAAA,KAAA,EAAA,IAAA,CAAA,UAAA,EAAA,CAAmB,CAAA;gCAAK,IAAA,CAAA,KAAK,CAAA,EAAA,CAAA,CAAA,iBAAA,CAAA;wBAChC,IAAA,CAAA,GAAG,IAAA,EAAA;8BAA/C,GAAA,CAEO,MAAA,EAAA,GAAA,CAAA;;gCAFD,KAAK,EAAC,yBAAyB;gCAAiB,OAAK,EAAE,IAAA,CAAA,YAAY;;gCACxE,GAAA,CAA8C,MAAA,EAAA,GAAA,CAAA,EAAxC,KAAK,EAAC,yBAAyB,EAAA,CAAA,EAAC,GAAC,CAAA;;;;;;YAK1C,GAAA,CAKO,MAAA,EAAA,GAAA,CAAA;gBALD,KAAK,EAAC,oBAAoB;gBAAE,KAAK,EAAA,GAAA,CAAE,GAAA,CAAA;;;kBAGxC,CAAA;;gBACA,UAAA,CAAkC,IAAA,CAAA,MAAA,EAAA,eAAA,CAAA;;;eAIG,IAAA,CAAA,SAAS,CAAA;cAAhD,GAAA,CAEO,MAAA,EAAA,GAAA,CAAA;;gBAFD,KAAK,EAAC,oBAAoB;;gBAC/B,GAAA,CAA4D,MAAA,EAAA,GAAA,CAAA,EAAtD,KAAK,EAAC,sBAAsB,EAAA,CAAA,EAAA,GAAA,CAAI,IAAA,CAAA,YAAY,CAAA,EAAA,CAAA,CAAA,UAAA,CAAA", "file": "components/main-form/components/form-container.uvue", "sourcesContent": ["<template>\n\t<view class=\"form-container-body\">\n\t\t<view class=\"form-container\">\n\t\t\t<view class=\"form-container-label\" v-if=\"label\">\n\t\t\t\t<view class=\"form-container-name\" >\r\n\t\t\t\t\t\n\t\t\t\t\t<text class=\"form-container-name-text\" :style=\"{color: labelColor}\">{{ label }}</text>\n\t\t\t\t\t<view class=\"form-container-tip-icon\" v-if=\"tip!=''\" @click=\"showTipModal\">\r\n\t\t\t\t\t\t<text class=\"form-container-tip-text\">!</text>\r\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<view class=\"form-container-box\" :style=\"{\n\t\t\t\tbackgroundColor: backgroundColor,\n\t\t\t\tborderColor: showError ? 'red' : '#fff'\n\t\t\t}\">\n\t\t\t\t<slot name=\"input-content\"></slot>\n\t\t\t</view> \n\t\t</view>\n\n\t\t<view class=\"form-container-tip\" v-if=\"showError\">\n\t\t\t<text class=\"form-container-error\">{{ errorMessage }}</text>\n\t\t</view>\n\t</view>\n</template>\n\n<script lang=\"uts\">\n\texport default {\n\t\tname: \"FormContainer\",\n\t\tprops: {\n\t\t\tlabel: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"\"\n\t\t\t},\n\t\t\tshowError: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: false\n\t\t\t},\n\t\t\terrorMessage: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"\"\n\t\t\t},\n\t\t\ttip: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"\"\n\t\t\t},\n\t\t\tlabelColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#000\"\n\t\t\t},\n\t\t\tbackgroundColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#f1f4f9\"\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\tshowTipModal() {\n\t\t\t\tif (this.tip!=\"\") {\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\tcontent: this.tip,\n\t\t\t\t\t\tshowCancel: false,\n\t\t\t\t\t\tconfirmText: '知道了'\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t.form-container-body {\n\t\twidth: 100%;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tpadding: 0 20rpx;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.form-container {\n\t\twidth: 100%;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t}\n\n\t.form-container-label {\n\t\twidth: 100%;\n\t\tmargin-bottom: 10rpx;\n\t}\n\n\t.form-container-name {\n\t\t\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t}\n\t.form-container-name-text{\r\n\t\tfont-size: 30rpx;\r\n\t}\n\t.form-container-tip-icon {\n\t\twidth: 40rpx;\n\t\theight: 40rpx;\n\t\tbackground-color: rgba(0, 0, 0, 0.05);\n\t\tborder-radius: 20rpx;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tmargin-left: 10rpx;\n\t}\n\n\t.form-container-tip-text {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666;\n\t\tfont-weight: bold;\n\t}\n\n\t.form-container-box {\n\t\twidth: 100%;\n\t\tborder: 1rpx solid #fff;\n\t\tbox-sizing: border-box;\n\t\tpadding: 20rpx;\n\t\tborder-radius: 20rpx;\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t}\n\n\t.form-container-tip {\n\t\twidth: 100%;\n\t\t\n\t\tmargin-top: 10rpx;\n\t}\n\n\t.form-container-error {\n\t\tcolor: red;\n\t\tfont-size: 28rpx;\n\t\ttext-align: right;\n\t}\n\n\t/* 通用输入元素样式类 */\n\t.form-input-element {\n\t\tflex: 1;\n\t\tmin-height: 60rpx;\n\t}\n\n\t/* 阴影样式 */\n\t.qShadow1 {\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n\t}\n</style>\n"]}