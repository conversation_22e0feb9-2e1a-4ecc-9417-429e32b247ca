const __sfc__ = defineComponent({
    name: "FormContainer",
    props: {
        label: {
            type: String,
            default: ""
        },
        showError: {
            type: Boolean,
            default: false
        },
        errorMessage: {
            type: String,
            default: ""
        },
        tip: {
            type: String,
            default: ""
        },
        labelColor: {
            type: String,
            default: "#000"
        },
        backgroundColor: {
            type: String,
            default: "#f1f4f9"
        }
    },
    methods: {
        showTipModal() {
            if (this.tip != "") {
                uni.showModal({
                    title: '提示',
                    content: this.tip,
                    showCancel: false,
                    confirmText: '知道了'
                });
            }
        }
    }
});
export default __sfc__;
function GenComponentsMainFormComponentsFormContainerRender(this: InstanceType<typeof __sfc__>): any | null {
    const _ctx = this;
    const _cache = this.$.renderCache;
    return _cE("view", _uM({ class: "form-container-body" }), [
        _cE("view", _uM({ class: "form-container" }), [
            isTrue(_ctx.label)
                ? _cE("view", _uM({
                    key: 0,
                    class: "form-container-label"
                }), [
                    _cE("view", _uM({ class: "form-container-name" }), [
                        _cE("text", _uM({
                            class: "form-container-name-text",
                            style: _nS(_uM({ color: _ctx.labelColor }))
                        }), _tD(_ctx.label), 5 /* TEXT, STYLE */),
                        _ctx.tip != ''
                            ? _cE("view", _uM({
                                key: 0,
                                class: "form-container-tip-icon",
                                onClick: _ctx.showTipModal
                            }), [
                                _cE("text", _uM({ class: "form-container-tip-text" }), "!")
                            ], 8 /* PROPS */, ["onClick"])
                            : _cC("v-if", true)
                    ])
                ])
                : _cC("v-if", true),
            _cE("view", _uM({
                class: "form-container-box",
                style: _nS(_uM({
                    backgroundColor: _ctx.backgroundColor,
                    borderColor: _ctx.showError ? 'red' : '#fff'
                }))
            }), [
                renderSlot(_ctx.$slots, "input-content")
            ], 4 /* STYLE */)
        ]),
        isTrue(_ctx.showError)
            ? _cE("view", _uM({
                key: 0,
                class: "form-container-tip"
            }), [
                _cE("text", _uM({ class: "form-container-error" }), _tD(_ctx.errorMessage), 1 /* TEXT */)
            ])
            : _cC("v-if", true)
    ]);
}
const GenComponentsMainFormComponentsFormContainerStyles = [_uM([["form-container-body", _pS(_uM([["width", "100%"], ["display", "flex"], ["flexDirection", "column"], ["paddingTop", 0], ["paddingRight", "20rpx"], ["paddingBottom", 0], ["paddingLeft", "20rpx"], ["marginBottom", "20rpx"]]))], ["form-container", _pS(_uM([["width", "100%"], ["display", "flex"], ["flexDirection", "column"]]))], ["form-container-label", _pS(_uM([["width", "100%"], ["marginBottom", "10rpx"]]))], ["form-container-name", _pS(_uM([["display", "flex"], ["flexDirection", "row"], ["alignItems", "center"]]))], ["form-container-name-text", _pS(_uM([["fontSize", "30rpx"]]))], ["form-container-tip-icon", _pS(_uM([["width", "40rpx"], ["height", "40rpx"], ["backgroundColor", "rgba(0,0,0,0.05)"], ["borderTopLeftRadius", "20rpx"], ["borderTopRightRadius", "20rpx"], ["borderBottomRightRadius", "20rpx"], ["borderBottomLeftRadius", "20rpx"], ["display", "flex"], ["justifyContent", "center"], ["alignItems", "center"], ["marginLeft", "10rpx"]]))], ["form-container-tip-text", _pS(_uM([["fontSize", "24rpx"], ["color", "#666666"], ["fontWeight", "bold"]]))], ["form-container-box", _pS(_uM([["width", "100%"], ["borderTopWidth", "1rpx"], ["borderRightWidth", "1rpx"], ["borderBottomWidth", "1rpx"], ["borderLeftWidth", "1rpx"], ["borderTopStyle", "solid"], ["borderRightStyle", "solid"], ["borderBottomStyle", "solid"], ["borderLeftStyle", "solid"], ["borderTopColor", "#ffffff"], ["borderRightColor", "#ffffff"], ["borderBottomColor", "#ffffff"], ["borderLeftColor", "#ffffff"], ["boxSizing", "border-box"], ["paddingTop", "20rpx"], ["paddingRight", "20rpx"], ["paddingBottom", "20rpx"], ["paddingLeft", "20rpx"], ["borderTopLeftRadius", "20rpx"], ["borderTopRightRadius", "20rpx"], ["borderBottomRightRadius", "20rpx"], ["borderBottomLeftRadius", "20rpx"], ["display", "flex"], ["flexDirection", "row"], ["alignItems", "center"]]))], ["form-container-tip", _pS(_uM([["width", "100%"], ["marginTop", "10rpx"]]))], ["form-container-error", _pS(_uM([["color", "#FF0000"], ["fontSize", "28rpx"], ["textAlign", "right"]]))], ["form-input-element", _pS(_uM([["flex", 1], ["minHeight", "60rpx"]]))], ["qShadow1", _pS(_uM([["boxShadow", "0 2rpx 8rpx rgba(0, 0, 0, 0.1)"]]))]])];
//# sourceMappingURL=form-container.uvue.map