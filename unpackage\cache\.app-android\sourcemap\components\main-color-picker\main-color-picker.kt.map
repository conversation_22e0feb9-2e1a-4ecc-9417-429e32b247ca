{"version": 3, "sources": ["components/main-color-picker/main-color-picker.uvue"], "sourcesContent": ["<template>\r\n\t<!-- 弹窗遮罩层 -->\r\n\t<view v-if=\"visible\" class=\"picker-overlay\" @click=\"onOverlayClick\">\r\n\t\t<view class=\"picker-modal\" @click.stop=\"\">\r\n\t\t\t<view class=\"color-picker-container\">\r\n\t\t\t\t<!-- 导航栏 -->\r\n\t\t\t\t<view class=\"navbar\">\r\n\t\t\t\t\t<text class=\"nav-btn cancel-btn\" @click=\"onCancel\">取消</text>\r\n\t\t\t\t\t<text class=\"nav-title\">颜色选择</text>\r\n\t\t\t\t\t<view class=\"confirm-btn-container\">\r\n\t\t\t\t\t\t<text class=\"nav-btn confirm-btn\" @click=\"onConfirm\">确定</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 颜色系列选择按钮 -->\r\n\t\t\t\t<view class=\"color-series-section\">\r\n\t\t\t\t\t<view class=\"color-series-buttons\">\r\n\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\tv-for=\"(series, index) in colorSeriesList\"\r\n\t\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\t\tclass=\"series-button\"\r\n\t\t\t\t\t\t\t:class=\"{\r\n\t\t\t\t\t\t\t\t'active': selectedSeriesIndex == index,\r\n\t\t\t\t\t\t\t\t'random-button': index == 0,\r\n\t\t\t\t\t\t\t\t'normal-button': index != 0\r\n\t\t\t\t\t\t\t}\"\r\n\t\t\t\t\t\t\t:style=\"{ backgroundColor: series.color }\"\r\n\t\t\t\t\t\t\t@click=\"onSeriesSelect(index)\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<text class=\"series-text\">{{ series.name }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 颜色方块列表 -->\r\n\t\t\t\t<view class=\"color-grid-section\">\r\n\t\t\t\t\t<view class=\"color-grid\">\r\n\t\t\t\t\t\t<view v-for=\"(color, index) in colorList\" :key=\"index\" class=\"color-item\"\r\n\t\t\t\t\t\t\t:class=\"{ 'selected': selectedColorIndex == index }\" :style=\"{ backgroundColor: color }\"\r\n\t\t\t\t\t\t\t@click=\"onColorSelect(index)\">\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 预览和透明度选择区域 -->\r\n\t\t\t\t<view class=\"preview-opacity-section\">\r\n\t\t\t\t\t<view class=\"preview-area\" @click=\"showRGBPicker\">\r\n\t\t\t\t\t\t<view class=\"preview-color\" :style=\"{ backgroundColor: finalColor }\"></view>\r\n\t\t\t\t\t\t<text class=\"rgba-text\">{{ finalColor }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"opacity-area\">\r\n\t\t\t\t\t\t<text class=\"opacity-label\">透明度</text>\r\n\t\t\t\t\t\t<view class=\"opacity-button\" @click=\"showOpacityPicker\">\r\n\t\t\t\t\t\t\t<text class=\"opacity-value\">{{ Math.round(opacity * 100) }}%</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- RGB设置弹窗 -->\r\n\t\t\t\t<view v-if=\"showRGBModal\" class=\"rgb-modal-overlay\" @click=\"closeRGBPicker\">\r\n\t\t\t\t\t<view class=\"rgb-modal\" @click=\"onRGBModalClick\">\r\n\t\t\t\t\t\t<view class=\"rgb-modal-header\">\r\n\t\t\t\t\t\t\t<text class=\"rgb-modal-title\">RGB颜色设置</text>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"rgb-preview-section\">\r\n\t\t\t\t\t\t\t<view class=\"rgb-preview-color\" :style=\"{ backgroundColor: tempRGBColor }\"></view>\r\n\t\t\t\t\t\t\t<text class=\"rgb-preview-text\">{{ tempRGBColor }}</text>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"rgb-controls\">\r\n\t\t\t\t\t\t\t<!-- R值控制 -->\r\n\t\t\t\t\t\t\t<view class=\"rgb-control-item\">\r\n\t\t\t\t\t\t\t\t<text class=\"rgb-label\">R</text>\r\n\t\t\t\t\t\t\t\t<slider class=\"rgb-slider\" :min=\"0\" :max=\"255\" :step=\"1\" :value=\"tempR\"\r\n\t\t\t\t\t\t\t\t\t@change=\"onTempRChange\" />\r\n\t\t\t\t\t\t\t\t<input class=\"rgb-input\" type=\"number\" :value=\"tempR.toString()\"\r\n\t\t\t\t\t\t\t\t\t@input=\"onTempRInput\" placeholder=\"0-255\" />\r\n\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t\t<!-- G值控制 -->\r\n\t\t\t\t\t\t\t<view class=\"rgb-control-item\">\r\n\t\t\t\t\t\t\t\t<text class=\"rgb-label\">G</text>\r\n\t\t\t\t\t\t\t\t<slider class=\"rgb-slider\" :min=\"0\" :max=\"255\" :step=\"1\" :value=\"tempG\"\r\n\t\t\t\t\t\t\t\t\t@change=\"onTempGChange\" />\r\n\t\t\t\t\t\t\t\t<input class=\"rgb-input\" type=\"number\" :value=\"tempG.toString()\"\r\n\t\t\t\t\t\t\t\t\t@input=\"onTempGInput\" placeholder=\"0-255\" />\r\n\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t\t<!-- B值控制 -->\r\n\t\t\t\t\t\t\t<view class=\"rgb-control-item\">\r\n\t\t\t\t\t\t\t\t<text class=\"rgb-label\">B</text>\r\n\t\t\t\t\t\t\t\t<slider class=\"rgb-slider\" :min=\"0\" :max=\"255\" :step=\"1\" :value=\"tempB\"\r\n\t\t\t\t\t\t\t\t\t@change=\"onTempBChange\" />\r\n\t\t\t\t\t\t\t\t<input class=\"rgb-input\" type=\"number\" :value=\"tempB.toString()\"\r\n\t\t\t\t\t\t\t\t\t@input=\"onTempBInput\" placeholder=\"0-255\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"rgb-modal-buttons\">\r\n\t\t\t\t\t\t\t<view class=\"rgb-button rgb-cancel\" @click=\"closeRGBPicker\">\r\n\t\t\t\t\t\t\t\t<text class=\"rgb-button-text\">取消</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"rgb-button rgb-confirm\" @click=\"confirmRGBPicker\">\r\n\t\t\t\t\t\t\t\t<text class=\"rgb-button-text\">确定</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t// 定义颜色类型\r\n\ttype ColorInfo = {\r\n\t\tr : number,\r\n\t\tg : number,\r\n\t\tb : number\r\n\t}\r\n\ttype RGBAValues = {\r\n\t  r: number,\r\n\t  g: number,\r\n\t  b: number,\r\n\t  a: number\r\n\t}\r\n\ttype RGBValues = {\r\n\t  r: number,\r\n\t  g: number,\r\n\t  b: number\r\n\t}\r\n\ttype ColorSeries = {\r\n\t  name: string,\r\n\t  color: string\r\n\t}\r\n\texport default {\r\n\t\tname: \"main-color-picker\",\r\n\t\temits: ['cancel', 'confirm'],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t// 控制弹窗显示\r\n\t\t\t\tvisible: false as boolean,\r\n\t\t\t\t// 当前选中的颜色系列索引\r\n\t\t\t\tselectedSeriesIndex: 0 as number,\r\n\t\t\t\t// 透明度，范围0-1\r\n\t\t\t\topacity: 1.0 as number,\r\n\t\t\t\t// 当前选中的颜色索引\r\n\t\t\t\tselectedColorIndex: 0 as number,\r\n\t\t\t\t// 基础颜色（可以根据需要修改）\r\n\t\t\t\tbaseColor: { r: 255.0, g: 0.0, b: 0.0 } as ColorInfo,\r\n\t\t\t\t// 随机色种子，用于重新生成随机色\r\n\t\t\t\trandomSeed: 0 as number,\r\n\t\t\t\t// RGB设置弹窗相关\r\n\t\t\t\tshowRGBModal: false as boolean,\r\n\t\t\t\ttempR: 255 as number,\r\n\t\t\t\ttempG: 0 as number,\r\n\t\t\t\ttempB: 0 as number,\r\n\t\t\t\t// 自定义颜色\r\n\t\t\t\tcustomColor: \"\" as string\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t// 颜色系列列表\r\n\t\t\tcolorSeriesList(): ColorSeries[] {\r\n\t\t\t\treturn [\r\n\t\t\t\t\t{ name: \"随机色\", color: \"#FF6B35\" },\r\n\t\t\t\t\t{ name: \"黑白灰\", color: \"#808080\" },\r\n\t\t\t\t\t{ name: \"红色\", color: \"#FF4444\" },\r\n\t\t\t\t\t{ name: \"橙色\", color: \"#FF8844\" },\r\n\t\t\t\t\t{ name: \"黄色\", color: \"#FFDD44\" },\r\n\t\t\t\t\t{ name: \"绿色\", color: \"#44FF44\" },\r\n\t\t\t\t\t{ name: \"青色\", color: \"#44FFFF\" },\r\n\t\t\t\t\t{ name: \"蓝色\", color: \"#4444FF\" },\r\n\t\t\t\t\t{ name: \"紫色\", color: \"#AA44FF\" },\r\n\t\t\t\t\t{ name: \"粉色\", color: \"#FF88CC\" },\r\n\t\t\t\t\t{ name: \"棕色\", color: \"#AA6644\" }\r\n\t\t\t\t]\r\n\t\t\t},\r\n\r\n\t\t\t// 根据选中的系列生成120个颜色（10行12列）\r\n\t\t\tcolorList() : string[] {\r\n\t\t\t\tconst colors : string[] = []\r\n\r\n\t\t\t\tfor (let i = 0; i < 120; i++) {\r\n\t\t\t\t\tconst row = Math.floor(i / 12) // 当前行（0-9）\r\n\t\t\t\t\tconst col = i % 12 // 当前列（0-11）\r\n\r\n\t\t\t\t\t// 计算位置因子\r\n\t\t\t\t\tconst rowFactor = row / 9.0 // 行因子 0-1\r\n\t\t\t\t\tconst colFactor = col / 11.0 // 列因子 0-1\r\n\r\n\t\t\t\t\t// 基于选中的系列索引确定颜色系列\r\n\t\t\t\t\tlet r: number, g: number, b: number\r\n\r\n\t\t\t\t\tif (this.selectedSeriesIndex == 0) {\r\n\t\t\t\t\t\t// 随机色系列 - 每个方块完全随机的RGB值\r\n\t\t\t\t\t\tconst seed1 = (row * 12 + col + this.randomSeed) * 0.1\r\n\t\t\t\t\t\tconst seed2 = (row * 12 + col + this.randomSeed + 100) * 0.13\r\n\t\t\t\t\t\tconst seed3 = (row * 12 + col + this.randomSeed + 200) * 0.17\r\n\t\t\t\t\t\tr = Math.round((Math.sin(seed1) * 0.5 + 0.5) * 255)\r\n\t\t\t\t\t\tg = Math.round((Math.sin(seed2) * 0.5 + 0.5) * 255)\r\n\t\t\t\t\t\tb = Math.round((Math.sin(seed3) * 0.5 + 0.5) * 255)\r\n\t\t\t\t\t} else if (this.selectedSeriesIndex == 1) {\r\n\t\t\t\t\t\t// 黑白灰系列 - 更细腻的灰度变化\r\n\t\t\t\t\t\tconst totalFactor = (row * 12 + col) / 119.0 // 0到1的完整渐变\r\n\t\t\t\t\t\tconst grayValue = Math.round(totalFactor * 255)\r\n\t\t\t\t\t\tr = grayValue\r\n\t\t\t\t\t\tg = grayValue\r\n\t\t\t\t\t\tb = grayValue\r\n\t\t\t\t\t} else if (this.selectedSeriesIndex == 2) {\r\n\t\t\t\t\t\t// 红色系列 - 更丰富的红色变化\r\n\t\t\t\t\t\tconst totalFactor = (row * 12 + col) / 119.0 // 0到1\r\n\t\t\t\t\t\tconst brightness = 0.2 + totalFactor * 0.8 // 0.2-1.0的亮度范围\r\n\t\t\t\t\t\tconst saturation = 0.3 + (1 - Math.abs(totalFactor - 0.5) * 2) * 0.7 // 中间饱和度高\r\n\t\t\t\t\t\tr = Math.round(brightness * 255)\r\n\t\t\t\t\t\tg = Math.round(brightness * (1 - saturation) * 255)\r\n\t\t\t\t\t\tb = Math.round(brightness * (1 - saturation) * 255)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 其他颜色系列 - 确保包含纯色且避免黑色\r\n\t\t\t\t\t\tconst totalFactor = (row * 12 + col) / 119.0 // 0到1\r\n\r\n\t\t\t\t\t\t// 根据系列索引确定基础色相\r\n\t\t\t\t\t\tlet baseHue: number\r\n\t\t\t\t\t\tif (this.selectedSeriesIndex == 3) baseHue = 30      // 橙色\r\n\t\t\t\t\t\telse if (this.selectedSeriesIndex == 4) baseHue = 60 // 黄色\r\n\t\t\t\t\t\telse if (this.selectedSeriesIndex == 5) baseHue = 120 // 绿色\r\n\t\t\t\t\t\telse if (this.selectedSeriesIndex == 6) baseHue = 180 // 青色\r\n\t\t\t\t\t\telse if (this.selectedSeriesIndex == 7) baseHue = 240 // 蓝色\r\n\t\t\t\t\t\telse if (this.selectedSeriesIndex == 8) baseHue = 300 // 紫色\r\n\t\t\t\t\t\telse if (this.selectedSeriesIndex == 9) baseHue = 330 // 粉色\r\n\t\t\t\t\t\telse baseHue = 25 // 棕色\r\n\r\n\t\t\t\t\t\t// 色相微调：在基础色相±10度范围内变化\r\n\t\t\t\t\t\tconst hue = baseHue + (colFactor - 0.5) * 20\r\n\r\n\t\t\t\t\t\t// 创造三种类型的颜色变化\r\n\t\t\t\t\t\tif (totalFactor < 0.4) {\r\n\t\t\t\t\t\t\t// 前40%：深色调 - 高饱和度，中低明度（避免太暗）\r\n\t\t\t\t\t\t\tconst localFactor = totalFactor / 0.4\r\n\t\t\t\t\t\t\tconst saturation = 0.8 + localFactor * 0.2 // 0.8-1.0\r\n\t\t\t\t\t\t\tconst value = 0.4 + localFactor * 0.3 // 0.4-0.7（避免太暗）\r\n\t\t\t\t\t\t\tconst rgb = this.hsvToRgb(hue, saturation, value)\r\n\t\t\t\t\t\t\tr = rgb.r\r\n\t\t\t\t\t\t\tg = rgb.g\r\n\t\t\t\t\t\t\tb = rgb.b\r\n\t\t\t\t\t\t} else if (totalFactor < 0.6) {\r\n\t\t\t\t\t\t\t// 中20%：纯色调 - 最高饱和度，最佳明度\r\n\t\t\t\t\t\t\tconst localFactor = (totalFactor - 0.4) / 0.2\r\n\t\t\t\t\t\t\tconst saturation = 1.0 // 最高饱和度\r\n\t\t\t\t\t\t\tconst value = 0.8 + localFactor * 0.2 // 0.8-1.0（确保亮度足够）\r\n\t\t\t\t\t\t\tconst rgb = this.hsvToRgb(hue, saturation, value)\r\n\t\t\t\t\t\t\tr = rgb.r\r\n\t\t\t\t\t\t\tg = rgb.g\r\n\t\t\t\t\t\t\tb = rgb.b\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t// 后40%：浅色调 - 降低饱和度，保持高明度\r\n\t\t\t\t\t\t\tconst localFactor = (totalFactor - 0.6) / 0.4\r\n\t\t\t\t\t\t\tconst saturation = 0.8 - localFactor * 0.6 // 0.8-0.2（逐渐降低饱和度）\r\n\t\t\t\t\t\t\tconst value = 0.9 + localFactor * 0.1 // 0.9-1.0（保持高明度）\r\n\t\t\t\t\t\t\tconst rgb = this.hsvToRgb(hue, saturation, value)\r\n\t\t\t\t\t\t\tr = rgb.r\r\n\t\t\t\t\t\t\tg = rgb.g\r\n\t\t\t\t\t\t\tb = rgb.b\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 确保RGB值在0-255范围内\r\n\t\t\t\t\tr = Math.max(0, Math.min(255, r))\r\n\t\t\t\t\tg = Math.max(0, Math.min(255, g))\r\n\t\t\t\t\tb = Math.max(0, Math.min(255, b))\r\n\r\n\t\t\t\t\tcolors.push(`rgb(${r}, ${g}, ${b})`)\r\n\t\t\t\t}\r\n\r\n\t\t\t\treturn colors\r\n\t\t\t},\r\n\r\n\r\n\r\n\t\t\t// 最终的RGBA颜色值\r\n\t\t\tfinalColor() : string {\r\n\t\t\t\t// 优先使用自定义颜色\r\n\t\t\t\tlet colorToUse = \"\"\r\n\t\t\t\tif (this.customColor != \"\") {\r\n\t\t\t\t\tcolorToUse = this.customColor\r\n\t\t\t\t} else if (this.colorList.length > this.selectedColorIndex) {\r\n\t\t\t\t\tcolorToUse = this.colorList[this.selectedColorIndex]\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (colorToUse != \"\") {\r\n\t\t\t\t\t// 提取RGB值并添加透明度\r\n\t\t\t\t\tconst rgbMatch = colorToUse.match(/rgb\\((\\d+),\\s*(\\d+),\\s*(\\d+)\\)/)\r\n\t\t\t\t\tif (rgbMatch != null) {\r\n\t\t\t\t\t\tconst r = parseInt(rgbMatch[1] as string)\r\n\t\t\t\t\t\tconst g = parseInt(rgbMatch[2] as string)\r\n\t\t\t\t\t\tconst b = parseInt(rgbMatch[3] as string)\r\n\t\t\t\t\t\treturn `rgba(${r}, ${g}, ${b}, ${this.opacity})`\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\treturn `rgba(255, 0, 0, ${this.opacity})`\r\n\t\t\t},\r\n\r\n\t\t\t// 临时RGB颜色预览\r\n\t\t\ttempRGBColor() : string {\r\n\t\t\t\treturn `rgb(${this.tempR}, ${this.tempG}, ${this.tempB})`\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 颜色系列选择事件\r\n\t\t\tonSeriesSelect(index: number) {\r\n\t\t\t\tthis.selectedSeriesIndex = index\r\n\t\t\t\tthis.selectedColorIndex = 0 // 重置选中的颜色\r\n\t\t\t\tthis.customColor = \"\" // 清除自定义颜色\r\n\r\n\t\t\t\t// 如果选择的是随机色系列，生成新的随机种子\r\n\t\t\t\tif (index == 0) { // 随机色现在是第1个按钮，索引为0\r\n\t\t\t\t\tthis.randomSeed = Math.floor(Math.random() * 1000)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 显示透明度选择器\r\n\t\t\tshowOpacityPicker() {\r\n\t\t\t\tconst opacityOptions = [\r\n\t\t\t\t\t'100%', '95%', '90%', '85%', '80%', '75%', '70%', '65%', '60%', '55%',\r\n\t\t\t\t\t'50%', '45%', '40%', '35%', '30%', '25%', '20%', '15%', '10%', '5%'\r\n\t\t\t\t]\r\n\r\n\t\t\t\tuni.showActionSheet({\r\n\t\t\t\t\titemList: opacityOptions,\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tconst selectedOpacity = (100 - res.tapIndex * 5) / 100\r\n\t\t\t\t\t\tthis.opacity = selectedOpacity\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\r\n\t\t\t// 颜色选择事件\r\n\t\t\tonColorSelect(index : number) {\r\n\t\t\t\tthis.selectedColorIndex = index\r\n\t\t\t\t// 清除自定义颜色，使用新选中的颜色\r\n\t\t\t\tthis.customColor = \"\"\r\n\t\t\t},\r\n\r\n\t\t\t// 显示RGB设置弹窗\r\n\t\t\tshowRGBPicker() {\r\n\t\t\t\t// 获取当前颜色的RGB值（优先使用自定义颜色）\r\n\t\t\t\tlet colorToUse = \"\"\r\n\t\t\t\tif (this.customColor != \"\") {\r\n\t\t\t\t\tcolorToUse = this.customColor\r\n\t\t\t\t} else if (this.colorList.length > this.selectedColorIndex) {\r\n\t\t\t\t\tcolorToUse = this.colorList[this.selectedColorIndex]\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (colorToUse != \"\") {\r\n\t\t\t\t\tconst rgbMatch = colorToUse.match(/rgb\\((\\d+),\\s*(\\d+),\\s*(\\d+)\\)/)\r\n\t\t\t\t\tif (rgbMatch != null) {\r\n\t\t\t\t\t\tthis.tempR = parseInt(rgbMatch[1] as string)\r\n\t\t\t\t\t\tthis.tempG = parseInt(rgbMatch[2] as string)\r\n\t\t\t\t\t\tthis.tempB = parseInt(rgbMatch[3] as string)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.tempR = 255\r\n\t\t\t\t\t\tthis.tempG = 0\r\n\t\t\t\t\t\tthis.tempB = 0\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.tempR = 255\r\n\t\t\t\t\tthis.tempG = 0\r\n\t\t\t\t\tthis.tempB = 0\r\n\t\t\t\t}\r\n\t\t\t\tthis.showRGBModal = true\r\n\t\t\t},\r\n\r\n\t\t\t// 关闭RGB设置弹窗\r\n\t\t\tcloseRGBPicker() {\r\n\t\t\t\tthis.showRGBModal = false\r\n\t\t\t},\r\n\r\n\t\t\t// RGB弹窗点击事件（阻止冒泡）\r\n\t\t\tonRGBModalClick() {\r\n\t\t\t\t// 空方法，用于阻止事件冒泡\r\n\t\t\t},\r\n\r\n\t\t\t// 确认RGB设置\r\n\t\t\tconfirmRGBPicker() {\r\n\t\t\t\t// 设置自定义颜色\r\n\t\t\t\tthis.customColor = `rgb(${this.tempR}, ${this.tempG}, ${this.tempB})`\r\n\t\t\t\tthis.showRGBModal = false\r\n\t\t\t},\r\n\r\n\t\t\t// R值滑块变化\r\n\t\t\tonTempRChange(event: UniSliderChangeEvent) {\r\n\t\t\t\tthis.tempR = event.detail.value as number\r\n\t\t\t},\r\n\r\n\t\t\t// G值滑块变化\r\n\t\t\tonTempGChange(event: UniSliderChangeEvent) {\r\n\t\t\t\tthis.tempG = event.detail.value as number\r\n\t\t\t},\r\n\r\n\t\t\t// B值滑块变化\r\n\t\t\tonTempBChange(event: UniSliderChangeEvent) {\r\n\t\t\t\tthis.tempB = event.detail.value as number\r\n\t\t\t},\r\n\r\n\t\t\t// R值输入框变化\r\n\t\t\tonTempRInput(event: UniInputEvent) {\r\n\t\t\t\tconst value = parseInt(event.detail.value)\r\n\t\t\t\tif (!isNaN(value)) {\r\n\t\t\t\t\tthis.tempR = Math.max(0, Math.min(255, value))\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// G值输入框变化\r\n\t\t\tonTempGInput(event: UniInputEvent) {\r\n\t\t\t\tconst value = parseInt(event.detail.value)\r\n\t\t\t\tif (!isNaN(value)) {\r\n\t\t\t\t\tthis.tempG = Math.max(0, Math.min(255, value))\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// B值输入框变化\r\n\t\t\tonTempBInput(event: UniInputEvent) {\r\n\t\t\t\tconst value = parseInt(event.detail.value)\r\n\t\t\t\tif (!isNaN(value)) {\r\n\t\t\t\t\tthis.tempB = Math.max(0, Math.min(255, value))\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 打开弹窗\r\n\t\t\topen() {\r\n\t\t\t\tthis.visible = true\r\n\t\t\t},\r\n\r\n\t\t\t// 关闭弹窗\r\n\t\t\tclose() {\r\n\t\t\t\tthis.visible = false\r\n\t\t\t},\r\n\r\n\t\t\t// 点击遮罩层关闭弹窗\r\n\t\t\tonOverlayClick() {\r\n\t\t\t\tthis.close()\r\n\t\t\t\tthis.$emit('cancel')\r\n\t\t\t},\r\n\r\n\t\t\t// 取消按钮点击事件\r\n\t\t\tonCancel() {\r\n\t\t\t\tthis.close()\r\n\t\t\t\tthis.$emit('cancel')\r\n\t\t\t},\r\n\r\n\t\t\t// 确定按钮点击事件\r\n\t\t\tonConfirm() {\r\n\t\t\t\tthis.close()\r\n\t\t\t\tconst rgbaValues = this.getRGBAValues()\r\n\t\t\t\tthis.$emit('confirm', {\r\n\t\t\t\t\tcolor: this.finalColor,\r\n\t\t\t\t\trgba: rgbaValues,\r\n\t\t\t\t\thex: this.rgbToHex(rgbaValues.r, rgbaValues.g, rgbaValues.b)\r\n\t\t\t\t})\r\n\t\t\t},\r\n \r\n\t\t\t// 获取RGBA数值\r\n\t\t\tgetRGBAValues() : RGBAValues {\r\n\t\t\t\tconst rgbaMatch = this.finalColor.match(/rgba\\((\\d+),\\s*(\\d+),\\s*(\\d+),\\s*([\\d.]+)\\)/)\r\n\t\t\t\tif (rgbaMatch != null) {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tr: parseInt(rgbaMatch[1] as string),\r\n\t\t\t\t\t\tg: parseInt(rgbaMatch[2] as string),\r\n\t\t\t\t\t\tb: parseInt(rgbaMatch[3] as string),\r\n\t\t\t\t\t\ta: parseFloat(rgbaMatch[4] as string)\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\treturn { r: 255, g: 0, b: 0, a: 1.0 }\r\n\t\t\t},\r\n\r\n\t\t\t// HSV转RGB\r\n\t\t\thsvToRgb(h: number, s: number, v: number): RGBValues {\r\n\t\t\t\tconst c: number = v * s\r\n\t\t\t\tconst x: number = c * (1.0 - Math.abs(((h / 60.0) % 2.0) - 1.0))\r\n\t\t\t\tconst m: number = v - c\r\n\r\n\t\t\t\tlet r: number = 0.0\r\n\t\t\t\tlet g: number = 0.0\r\n\t\t\t\tlet b: number = 0.0\r\n\r\n\t\t\t\tif (h >= 0 && h < 60) {\r\n\t\t\t\t\tr = c\r\n\t\t\t\t\tg = x\r\n\t\t\t\t\tb = 0.0\r\n\t\t\t\t} else if (h >= 60 && h < 120) {\r\n\t\t\t\t\tr = x\r\n\t\t\t\t\tg = c\r\n\t\t\t\t\tb = 0.0\r\n\t\t\t\t} else if (h >= 120 && h < 180) {\r\n\t\t\t\t\tr = 0.0\r\n\t\t\t\t\tg = c\r\n\t\t\t\t\tb = x\r\n\t\t\t\t} else if (h >= 180 && h < 240) {\r\n\t\t\t\t\tr = 0.0\r\n\t\t\t\t\tg = x\r\n\t\t\t\t\tb = c\r\n\t\t\t\t} else if (h >= 240 && h < 300) {\r\n\t\t\t\t\tr = x\r\n\t\t\t\t\tg = 0.0\r\n\t\t\t\t\tb = c\r\n\t\t\t\t} else if (h >= 300 && h < 360) {\r\n\t\t\t\t\tr = c\r\n\t\t\t\t\tg = 0.0\r\n\t\t\t\t\tb = x\r\n\t\t\t\t}\r\n\r\n\t\t\t\tconst result: RGBValues = {\r\n\t\t\t\t\tr: Math.round((r + m) * 255.0),\r\n\t\t\t\t\tg: Math.round((g + m) * 255.0),\r\n\t\t\t\t\tb: Math.round((b + m) * 255.0)\r\n\t\t\t\t}\r\n\t\t\t\treturn result\r\n\t\t\t},\r\n\r\n\t\t\t// RGB转十六进制\r\n\t\t\trgbToHex(r: number, g: number, b: number): string {\r\n\t\t\t\tconst toHex = (value: number): string => {\r\n\t\t\t\t\tconst hex = Math.round(Math.max(0, Math.min(255, value))).toString(16)\r\n\t\t\t\t\treturn hex.length == 1 ? '0' + hex : hex\r\n\t\t\t\t}\r\n\t\t\t\treturn '#' + toHex(r) + toHex(g) + toHex(b)\r\n\t\t\t},\r\n\r\n\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t/* 弹窗遮罩层 */\r\n\t.picker-overlay {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.5);\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: flex-end;\r\n\t\tz-index: 1000;\r\n\t}\r\n\r\n\t.picker-modal {\r\n\t\twidth: 100%;\r\n\t\tbackground-color: #ffffff;\r\n\t\tborder-top-left-radius: 20rpx;\r\n\t\tborder-top-right-radius: 20rpx;\r\n\t\toverflow: hidden;\r\n\t\tbox-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\r\n\t}\r\n\r\n\t.color-picker-container {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tbackground-color: #ffffff;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\r\n\t/* 导航栏样式 */\r\n\t.navbar {\r\n\t\theight: 44px;\r\n\t\tbackground-color: #f8f8f8;\r\n\t\tborder-bottom: 1px solid #e5e5e5;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tpadding: 0 10px;\r\n\t}\r\n\r\n\t.nav-btn {\r\n\t\tfont-size: 16px;\r\n\t\tcolor: #007aff;\r\n\t\tpadding: 8px 12px;\r\n\t}\r\n\r\n\t.cancel-btn {\r\n\t\tcolor: #999999;\r\n\t}\r\n\r\n\t.confirm-btn-container {\r\n\t\theight: 30px;\r\n\t\tbackground-color: #007aff;\r\n\t\tborder-radius: 8rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);\r\n\t}\r\n\r\n\t.confirm-btn {\r\n\t\tcolor: #ffffff;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.nav-title {\r\n\t\tfont-size: 17px;\r\n\t\tcolor: #333333;\r\n\t}\r\n\r\n\t/* 区域标题样式 */\r\n\t.section-title {\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #666666;\r\n\t\tmargin-bottom: 10px;\r\n\t}\r\n\r\n\t/* 颜色系列选择区域 */\r\n\t.color-series-section {\r\n\t\tpadding: 20rpx;\r\n\t\tborder-bottom: 1px solid #f0f0f0;\r\n\t}\r\n\r\n\t.color-series-buttons {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: space-between;\r\n\t\tmargin-top: 10rpx;\r\n\t\tpadding: 0 10rpx;\r\n\t}\r\n\r\n\t.series-button {\r\n\t\theight: 60rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t\tborder: 2px solid transparent;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tmargin-bottom: 10rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t/* 随机色按钮：两个普通按钮宽度 + 间距 */\r\n\t.random-button {\r\n\t\twidth: 220rpx;\r\n\t}\r\n\r\n\t/* 其他按钮正常宽度 */\r\n\t.normal-button {\r\n\t\twidth: 100rpx;\r\n\t}\r\n\r\n\t.series-button.active {\r\n\t\tborder-color: #007aff;\r\n\t\tbox-shadow: 0 0 0 1px #007aff;\r\n\t}\r\n\r\n\t.series-text {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #ffffff;\r\n\t\tfont-weight: bold;\r\n\t\ttext-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n\t}\r\n\r\n\t/* 颜色网格区域 */\r\n\t.color-grid-section {\r\n\t\tpadding: 20rpx;\r\n\t\tborder-bottom: 1px solid #f0f0f0;\r\n\t}\r\n\r\n\t.color-grid {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: space-around;\r\n\t\talign-items: flex-start;\r\n\t\tpadding: 15rpx;\r\n\t}\r\n\r\n\t.color-item {\r\n\t\twidth: 55rpx;\r\n\t\theight: 40rpx;\r\n\t\tborder-radius: 4px;\r\n\t\tborder: 2px solid transparent;\r\n\t\tmargin-bottom: 4px;\r\n\t\tflex-shrink: 0;\r\n\t\tflex-grow: 0;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.color-item.selected {\r\n\t\tborder-color: #007aff;\r\n\t\tbox-shadow: 0 0 0 1px #007aff;\r\n\t}\r\n\r\n\t/* 预览和透明度选择区域 */\r\n\t.preview-opacity-section {\r\n\t\tpadding: 15rpx 20rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tborder-bottom: 1px solid #f0f0f0;\r\n\t}\r\n\r\n\t.preview-area {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.preview-color {\r\n\t\twidth: 60rpx;\r\n\t\theight: 60rpx;\r\n\t\tborder-radius: 30rpx;\r\n\t\tborder: 1px solid #e5e5e5;\r\n\t\tmargin-right: 15rpx;\r\n\t\tbox-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n\t}\r\n\r\n\t.rgba-text {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #666666;\r\n\t\tfont-family: monospace;\r\n\t\tbackground-color: #f5f5f5;\r\n\t\tpadding: 8rpx 12rpx;\r\n\t\tborder-radius: 6rpx;\r\n\t}\r\n\r\n\t.opacity-area {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.opacity-label {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #666666;\r\n\t\tmargin-bottom: 8rpx;\r\n\t}\r\n\r\n\t.opacity-button {\r\n\t\tbackground-color: #007aff;\r\n\t\tpadding: 12rpx 20rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t\tborder: none;\r\n\t}\r\n\r\n\t.opacity-value {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #ffffff;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t/* RGB设置弹窗样式 */\r\n\t.rgb-modal-overlay {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.5);\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tz-index: 1000;\r\n\t}\r\n\r\n\t.rgb-modal {\r\n\t\tbackground-color: #ffffff;\r\n\t\tborder-radius: 12rpx;\r\n\t\twidth: 600rpx;\r\n\t\tmax-height: 800rpx;\r\n\t\tpadding: 30rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.rgb-modal-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.rgb-modal-title {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333333;\r\n\t}\r\n\r\n\t.rgb-preview-section {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tmargin-bottom: 25rpx;\r\n\t\tpadding: 15rpx;\r\n\t\tbackground-color: #f8f8f8;\r\n\t\tborder-radius: 8rpx;\r\n\t}\r\n\r\n\t.rgb-preview-color {\r\n\t\twidth: 60rpx;\r\n\t\theight: 60rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t\tborder: 1px solid #e5e5e5;\r\n\t\tmargin-right: 15rpx;\r\n\t}\r\n\r\n\t.rgb-preview-text {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #666666;\r\n\t\tfont-family: monospace;\r\n\t}\r\n\r\n\t.rgb-controls {\r\n\t\tmargin-bottom: 25rpx;\r\n\t}\r\n\r\n\t.rgb-control-item {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.rgb-label {\r\n\t\twidth: 40rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333333;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.rgb-slider {\r\n\t\tflex: 1;\r\n\t\tmargin: 0 15rpx;\r\n\t}\r\n\r\n\t.rgb-input {\r\n\t\twidth: 120rpx;\r\n\t\theight: 60rpx;\r\n\t\tborder: 1px solid #e5e5e5;\r\n\t\tborder-radius: 6rpx;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 24rpx;\r\n\t\tpadding: 0 10rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.rgb-modal-buttons {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\tjustify-content: space-between;\r\n\t}\r\n\r\n\t.rgb-button {\r\n\t\twidth: 45%;\r\n\t\theight: 70rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.rgb-cancel {\r\n\t\tbackground-color: #f5f5f5;\r\n\t\tborder: 1px solid #e5e5e5;\r\n\t}\r\n\r\n\t.rgb-confirm {\r\n\t\tbackground-color: #007aff;\r\n\t}\r\n\r\n\t.rgb-button-text {\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.rgb-cancel .rgb-button-text {\r\n\t\tcolor: #666666;\r\n\t}\r\n\r\n\t.rgb-confirm .rgb-button-text {\r\n\t\tcolor: #ffffff;\r\n\t}\r\n</style>"], "names": [], "mappings": ";;;;;;;;;;;;;+BAwUQ;AAhMF;;;;;;;0BAtIO,KAAA,OAAO;YAAnB,IA6GO,QAAA,gBA7Gc,WAAM,kBAAkB,aAAO,KAAA,cAAc;gBACjE,IA2GO,QAAA,IA3GD,WAAM,gBAAgB,aAAK,cAAN,KAAA,CAAA,GAAc;oBAAA;iBAAA;oBACxC,IAyGO,QAAA,IAzGD,WAAM,2BAAwB;wBAEnC,IAMO,QAAA,IAND,WAAM,WAAQ;4BACnB,IAA4D,QAAA,IAAtD,WAAM,sBAAsB,aAAO,KAAA,QAAQ,GAAE,MAAE,CAAA,EAAA;gCAAA;6BAAA;4BACrD,IAAmC,QAAA,IAA7B,WAAM,cAAY;4BACxB,IAEO,QAAA,IAFD,WAAM,0BAAuB;gCAClC,IAA8D,QAAA,IAAxD,WAAM,uBAAuB,aAAO,KAAA,SAAS,GAAE,MAAE,CAAA,EAAA;oCAAA;iCAAA;;;wBAKzD,IAiBO,QAAA,IAjBD,WAAM,yBAAsB;4BACjC,IAeO,QAAA,IAfD,WAAM,yBAAsB;gCACjC,IAaO,UAAA,IAAA,EAAA,cAAA,UAAA,CAZoB,KAAA,eAAe,EAAA,IAAjC,QAAQ,OAAR,SAAM,UAAA,GAAA,CAAA;2CADf,IAaO,QAAA,IAXL,SAAK,OACN,WAAK,IAAA;wCAAC;wCACE;qCAIP,GACA,WAAK,IAAE,IAAA,qBAAA,OAAA,KAAA,IACP,aAAK,KAAA;wCAAE,KAAA,cAAc,CAAC;oCAAK;wCAE5B,IAAkD,QAAA,IAA5C,WAAM,gBAAa,IAAI,OAAO,IAAI,GAAA,CAAA;;;;;;;wBAM3C,IAOO,QAAA,IAPD,WAAM,uBAAoB;4BAC/B,IAKO,QAAA,IALD,WAAM,eAAY;gCACvB,IAGO,UAAA,IAAA,EAAA,cAAA,UAAA,CAHwB,KAAA,SAAS,EAAA,IAA1B,OAAO,OAAP,SAAK,UAAA,GAAA,CAAA;2CAAnB,IAGO,QAAA,IAHoC,SAAK,OAAO,WAAK,IAAA;wCAAC;wCACpD,IAAA,eAAA,KAAA,kBAAA,IAAA;qCAA2C,GAAG,WAAK,IAAE,IAAA,qBAAA,SAC5D,aAAK,KAAA;wCAAE,KAAA,aAAa,CAAC;oCAAK;;;;;;wBAM9B,IAWO,QAAA,IAXD,WAAM,4BAAyB;4BACpC,IAGO,QAAA,IAHD,WAAM,gBAAgB,aAAO,KAAA,aAAa;gCAC/C,IAA4E,QAAA,IAAtE,WAAM,iBAAiB,WAAK,IAAE,IAAA,qBAAA,KAAA,UAAA;gCACpC,IAA+C,QAAA,IAAzC,WAAM,cAAW,IAAI,KAAA,UAAU,GAAA,CAAA;;;;4BAEtC,IAKO,QAAA,IALD,WAAM,iBAAc;gCACzB,IAAsC,QAAA,IAAhC,WAAM,kBAAgB;gCAC5B,IAEO,QAAA,IAFD,WAAM,kBAAkB,aAAO,KAAA,iBAAiB;oCACrD,IAAmE,QAAA,IAA7D,WAAM,kBAAe,IAAI,KAAK,KAAK,CAAC,KAAA,OAAO,GAAA,GAAA,KAAU,KAAC,CAAA;;;;;;mCAMnD,KAAA,YAAY;4BAAxB,IAiDO,QAAA,gBAjDmB,WAAM,qBAAqB,aAAO,KAAA,cAAc;gCACzE,IA+CO,QAAA,IA/CD,WAAM,aAAa,aAAO,KAAA,eAAe;oCAC9C,IAEO,QAAA,IAFD,WAAM,qBAAkB;wCAC7B,IAA4C,QAAA,IAAtC,WAAM,oBAAkB;;oCAG/B,IAGO,QAAA,IAHD,WAAM,wBAAqB;wCAChC,IAAkF,QAAA,IAA5E,WAAM,qBAAqB,WAAK,IAAE,IAAA,qBAAA,KAAA,YAAA;wCACxC,IAAwD,QAAA,IAAlD,WAAM,qBAAkB,IAAI,KAAA,YAAY,GAAA,CAAA;;oCAG/C,IA2BO,QAAA,IA3BD,WAAM,iBAAc;wCAEzB,IAMO,QAAA,IAND,WAAM,qBAAkB;4CAC7B,IAAgC,QAAA,IAA1B,WAAM,cAAY;4CACxB,IAC2B,mBAAA,IADnB,WAAM,cAAc,SAAK,CAAC,EAAG,SAAK,GAAG,EAAG,UAAM,CAAC,EAAG,WAAO,KAAA,KAAK,EACpE,cAAQ,KAAA,aAAa;;;;4CACvB,IAC6C,SAAA,IADtC,WAAM,aAAY,UAAK,UAAU,WAAO,KAAA,KAAK,CAAC,QAAQ,CAAA,EAAA,GAC3D,aAAO,KAAA,YAAY,EAAE,iBAAY;;;;;wCAIpC,IAMO,QAAA,IAND,WAAM,qBAAkB;4CAC7B,IAAgC,QAAA,IAA1B,WAAM,cAAY;4CACxB,IAC2B,mBAAA,IADnB,WAAM,cAAc,SAAK,CAAC,EAAG,SAAK,GAAG,EAAG,UAAM,CAAC,EAAG,WAAO,KAAA,KAAK,EACpE,cAAQ,KAAA,aAAa;;;;4CACvB,IAC6C,SAAA,IADtC,WAAM,aAAY,UAAK,UAAU,WAAO,KAAA,KAAK,CAAC,QAAQ,CAAA,EAAA,GAC3D,aAAO,KAAA,YAAY,EAAE,iBAAY;;;;;wCAIpC,IAMO,QAAA,IAND,WAAM,qBAAkB;4CAC7B,IAAgC,QAAA,IAA1B,WAAM,cAAY;4CACxB,IAC2B,mBAAA,IADnB,WAAM,cAAc,SAAK,CAAC,EAAG,SAAK,GAAG,EAAG,UAAM,CAAC,EAAG,WAAO,KAAA,KAAK,EACpE,cAAQ,KAAA,aAAa;;;;4CACvB,IAC6C,SAAA,IADtC,WAAM,aAAY,UAAK,UAAU,WAAO,KAAA,KAAK,CAAC,QAAQ,CAAA,EAAA,GAC3D,aAAO,KAAA,YAAY,EAAE,iBAAY;;;;;;oCAIrC,IAOO,QAAA,IAPD,WAAM,sBAAmB;wCAC9B,IAEO,QAAA,IAFD,WAAM,yBAAyB,aAAO,KAAA,cAAc;4CACzD,IAAuC,QAAA,IAAjC,WAAM,oBAAkB;;;;wCAE/B,IAEO,QAAA,IAFD,WAAM,0BAA0B,aAAO,KAAA,gBAAgB;4CAC5D,IAAuC,QAAA,IAAjC,WAAM,oBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;aAsClC,SAAkB,OAAO;aAEzB,qBAA0B,MAAM;aAEhC,SAAgB,MAAM;aAEtB,oBAAyB,MAAM;aAE/B,WAA2C;aAE3C,YAAiB,MAAM;aAEvB,cAAuB,OAAO;aAC9B,OAAc,MAAM;aACpB,OAAY,MAAM;aAClB,OAAY,MAAM;aAElB,aAAmB,MAAK;uCAKN;iCAiBL,MAAM;yBAoGL,MAAK;2BAuBH,MAAK;;;mBAlKrB,aAAS,KAAI,CAAA,EAAA,CAAK,OAAO,EAEzB,yBAAqB,CAAA,CAAA,EAAA,CAAK,MAAM,EAEhC,aAAS,GAAE,CAAA,EAAA,CAAK,MAAM,EAEtB,wBAAoB,CAAA,CAAA,EAAA,CAAK,MAAM,EAE/B,yBAAa,IAAG,KAAK,EAAE,IAAG,GAAG,EAAE,IAAG,GAAE,GAEpC,gBAAY,CAAA,CAAA,EAAA,CAAK,MAAM,EAEvB,kBAAc,KAAI,CAAA,EAAA,CAAK,OAAO,EAC9B,WAAO,GAAE,CAAA,EAAA,CAAK,MAAM,EACpB,WAAO,CAAA,CAAA,EAAA,CAAK,MAAM,EAClB,WAAO,CAAA,CAAA,EAAA,CAAK,MAAM,EAElB,iBAAa,GAAC,EAAA,CAAK,MAAK,yCAKN,cAAnB,gBAAmB,aAAY;YAC9B,OAAO;4BACJ,OAAM,OAAO,QAAO;4BACpB,OAAM,OAAO,QAAO;4BACpB,OAAM,MAAM,QAAO;4BACnB,OAAM,MAAM,QAAO;4BACnB,OAAM,MAAM,QAAO;4BACnB,OAAM,MAAM,QAAO;4BACnB,OAAM,MAAM,QAAO;4BACnB,OAAM,MAAM,QAAO;4BACnB,OAAM,MAAM,QAAO;4BACnB,OAAM,MAAM,QAAO;4BACnB,OAAM,MAAM,QAAO;aACtB;QACD;4CAGc,MAAM,GAApB,gBAAc,MAAM,EAAC;YACpB,IAAM,iBAAS,MAAM,IAAK,KAAC;gBAE3B;gBAAK,IAAI,YAAI,CAAC;gBAAd,MAAgB,IAAI,GAAG;oBACtB,IAAM,MAAM,KAAK,KAAK,CAAC,IAAI,EAAE;oBAC7B,IAAM,MAAM,IAAI,EAAC;oBAIjB,IAAM,YAAY,MAAM,IAAG;oBAG3B,IAAI,GAAG,MAAM;wBAAE,GAAG,MAAM;wBAAE,GAAG,MAAK;oBAElC,IAAI,IAAI,CAAC,mBAAkB,IAAK,CAAC,EAAE;wBAElC,IAAM,QAAQ,CAAC,MAAM,EAAC,GAAI,MAAM,IAAI,CAAC,UAAU,IAAI,GAAE;wBACrD,IAAM,QAAQ,CAAC,MAAM,EAAC,GAAI,MAAM,IAAI,CAAC,UAAS,GAAI,GAAG,IAAI,IAAG;wBAC5D,IAAM,QAAQ,CAAC,MAAM,EAAC,GAAI,MAAM,IAAI,CAAC,UAAS,GAAI,GAAG,IAAI,IAAG;wBAC5D,IAAI,KAAK,KAAK,CAAC,CAAC,KAAK,GAAG,CAAC,SAAS,GAAE,GAAI,GAAG,IAAI,GAAG;wBAClD,IAAI,KAAK,KAAK,CAAC,CAAC,KAAK,GAAG,CAAC,SAAS,GAAE,GAAI,GAAG,IAAI,GAAG;wBAClD,IAAI,KAAK,KAAK,CAAC,CAAC,KAAK,GAAG,CAAC,SAAS,GAAE,GAAI,GAAG,IAAI,GAAG;2BAC5C,IAAI,IAAI,CAAC,mBAAkB,IAAK,CAAC,EAAE;wBAEzC,IAAM,cAAc,CAAC,MAAM,EAAC,GAAI,GAAG,IAAI,KAAI;wBAC3C,IAAM,YAAY,KAAK,KAAK,CAAC,cAAc,GAAG;wBAC9C,IAAI;wBACJ,IAAI;wBACJ,IAAI;2BACE,IAAI,IAAI,CAAC,mBAAkB,IAAK,CAAC,EAAE;wBAEzC,IAAM,cAAc,CAAC,MAAM,EAAC,GAAI,GAAG,IAAI,KAAI;wBAC3C,IAAM,aAAa,GAAE,GAAI,cAAc,GAAE;wBACzC,IAAM,aAAa,GAAE,GAAI,CAAC,CAAA,GAAI,KAAK,GAAG,CAAC,cAAc,GAAG,IAAI,CAAC,IAAI,GAAE;wBACnE,IAAI,KAAK,KAAK,CAAC,aAAa,GAAG;wBAC/B,IAAI,KAAK,KAAK,CAAC,aAAa,CAAC,CAAA,GAAI,UAAU,IAAI,GAAG;wBAClD,IAAI,KAAK,KAAK,CAAC,aAAa,CAAC,CAAA,GAAI,UAAU,IAAI,GAAG;2BAC5C;wBAEN,IAAM,cAAc,CAAC,MAAM,EAAC,GAAI,GAAG,IAAI,KAAI;wBAG3C,IAAI,SAAS,MAAK;wBAClB,IAAI,IAAI,CAAC,mBAAkB,IAAK,CAAC;4BAAE,UAAU,EAAC;+BACzC,IAAI,IAAI,CAAC,mBAAkB,IAAK,CAAC;4BAAE,UAAU,EAAC;+BAC9C,IAAI,IAAI,CAAC,mBAAkB,IAAK,CAAC;4BAAE,UAAU,GAAE;+BAC/C,IAAI,IAAI,CAAC,mBAAkB,IAAK,CAAC;4BAAE,UAAU,GAAE;+BAC/C,IAAI,IAAI,CAAC,mBAAkB,IAAK,CAAC;4BAAE,UAAU,GAAE;+BAC/C,IAAI,IAAI,CAAC,mBAAkB,IAAK,CAAC;4BAAE,UAAU,GAAE;+BAC/C,IAAI,IAAI,CAAC,mBAAkB,IAAK,CAAC;4BAAE,UAAU,GAAE;;4BAC/C,UAAU,EAAC;;wBAGhB,IAAM,MAAM,UAAU,CAAC,YAAY,GAAG,IAAI,EAAC;wBAG3C,IAAI,cAAc,GAAG,EAAE;4BAEtB,IAAM,cAAc,cAAc,GAAE;4BACpC,IAAM,aAAa,GAAE,GAAI,cAAc,GAAE;4BACzC,IAAM,QAAQ,GAAE,GAAI,cAAc,GAAE;4BACpC,IAAM,MAAM,IAAI,CAAC,QAAQ,CAAC,KAAK,YAAY;4BAC3C,IAAI,IAAI,CAAA;4BACR,IAAI,IAAI,CAAA;4BACR,IAAI,IAAI,CAAA;+BACF,IAAI,cAAc,GAAG,EAAE;4BAE7B,IAAM,cAAc,CAAC,cAAc,GAAG,IAAI,GAAE;4BAC5C,IAAM,qBAAa,GAAE;4BACrB,IAAM,QAAQ,GAAE,GAAI,cAAc,GAAE;4BACpC,IAAM,MAAM,IAAI,CAAC,QAAQ,CAAC,KAAK,YAAY;4BAC3C,IAAI,IAAI,CAAA;4BACR,IAAI,IAAI,CAAA;4BACR,IAAI,IAAI,CAAA;+BACF;4BAEN,IAAM,cAAc,CAAC,cAAc,GAAG,IAAI,GAAE;4BAC5C,IAAM,aAAa,GAAE,GAAI,cAAc,GAAE;4BACzC,IAAM,QAAQ,GAAE,GAAI,cAAc,GAAE;4BACpC,IAAM,MAAM,IAAI,CAAC,QAAQ,CAAC,KAAK,YAAY;4BAC3C,IAAI,IAAI,CAAA;4BACR,IAAI,IAAI,CAAA;4BACR,IAAI,IAAI,CAAA;;;oBAKV,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,GAAG,EAAE;oBAC9B,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,GAAG,EAAE;oBAC9B,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,GAAG,EAAE;oBAE9B,OAAO,IAAI,CAAC,SAAO,IAAC,OAAK,IAAC,OAAK,IAAC;oBAxFR;;;YA2FzB,OAAO;QACR;oCAKe,MAAK,EAApB,OAAe,MAAK,CAAA;YAEnB,IAAI,aAAa;YACjB,IAAI,IAAI,CAAC,WAAU,IAAK,IAAI;gBAC3B,aAAa,IAAI,CAAC,WAAU;mBACtB,IAAI,IAAI,CAAC,SAAS,CAAC,MAAK,GAAI,IAAI,CAAC,kBAAkB,EAAE;gBAC3D,aAAa,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,kBAAkB,CAAA;;YAGpD,IAAI,cAAc,IAAI;gBAErB,IAAM,WAAW,WAAW,KAAK,CAAC;gBAClC,IAAI,YAAY,IAAI,EAAE;oBACrB,IAAM,IAAI,SAAS,QAAQ,CAAC,CAAC,CAAA,CAAA,EAAA,CAAK,MAAM;oBACxC,IAAM,IAAI,SAAS,QAAQ,CAAC,CAAC,CAAA,CAAA,EAAA,CAAK,MAAM;oBACxC,IAAM,IAAI,SAAS,QAAQ,CAAC,CAAC,CAAA,CAAA,EAAA,CAAK,MAAM;oBACxC,OAAO,UAAQ,IAAC,OAAK,IAAC,OAAK,IAAC,OAAK,IAAI,CAAC,OAAO,GAAA;;;YAG/C,OAAO,qBAAmB,IAAI,CAAC,OAAO,GAAA;QACvC;sCAGiB,MAAK,EAAtB,OAAiB,MAAK,CAAA;YACrB,OAAO,SAAO,IAAI,CAAC,KAAK,GAAA,OAAK,IAAI,CAAC,KAAK,GAAA,OAAK,IAAI,CAAC,KAAK,GAAA;QACvD;;;aAIA;aAAA,sBAAe,OAAO,MAAM,EAAA;QAC3B,IAAI,CAAC,mBAAkB,GAAI;QAC3B,IAAI,CAAC,kBAAiB,GAAI,CAAA;QAC1B,IAAI,CAAC,WAAU,GAAI;QAGnB,IAAI,SAAS,CAAC,EAAE;YACf,IAAI,CAAC,UAAS,GAAI,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,IAAI;;IAEnD;aAGA;aAAA,2BAAiB;QAChB,IAAM,iBAAiB;YACtB;YAAQ;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAChE;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;YAAO;SAChE;QAEA,2CACC,WAAU,gBACV,UAAS,IAAC,IAAM;YACf,IAAM,kBAAkB,CAAC,GAAE,GAAI,IAAI,QAAO,GAAI,CAAC,IAAI,GAAE;YACrD,IAAI,CAAC,OAAM,GAAI;QAChB;;IAEF;aAGA;aAAA,qBAAc,OAAQ,MAAM,EAAA;QAC3B,IAAI,CAAC,kBAAiB,GAAI;QAE1B,IAAI,CAAC,WAAU,GAAI;IACpB;aAGA;aAAA,uBAAa;QAEZ,IAAI,aAAa;QACjB,IAAI,IAAI,CAAC,WAAU,IAAK,IAAI;YAC3B,aAAa,IAAI,CAAC,WAAU;eACtB,IAAI,IAAI,CAAC,SAAS,CAAC,MAAK,GAAI,IAAI,CAAC,kBAAkB,EAAE;YAC3D,aAAa,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,kBAAkB,CAAA;;QAGpD,IAAI,cAAc,IAAI;YACrB,IAAM,WAAW,WAAW,KAAK,CAAC;YAClC,IAAI,YAAY,IAAI,EAAE;gBACrB,IAAI,CAAC,KAAI,GAAI,SAAS,QAAQ,CAAC,CAAC,CAAA,CAAA,EAAA,CAAK,MAAM;gBAC3C,IAAI,CAAC,KAAI,GAAI,SAAS,QAAQ,CAAC,CAAC,CAAA,CAAA,EAAA,CAAK,MAAM;gBAC3C,IAAI,CAAC,KAAI,GAAI,SAAS,QAAQ,CAAC,CAAC,CAAA,CAAA,EAAA,CAAK,MAAM;mBACrC;gBACN,IAAI,CAAC,KAAI,GAAI,GAAE;gBACf,IAAI,CAAC,KAAI,GAAI,CAAA;gBACb,IAAI,CAAC,KAAI,GAAI,CAAA;aACd;eACM;YACN,IAAI,CAAC,KAAI,GAAI,GAAE;YACf,IAAI,CAAC,KAAI,GAAI,CAAA;YACb,IAAI,CAAC,KAAI,GAAI,CAAA;;QAEd,IAAI,CAAC,YAAW,GAAI,IAAG;IACxB;aAGA;aAAA,wBAAc;QACb,IAAI,CAAC,YAAW,GAAI,KAAI;IACzB;aAGA;aAAA,yBAAe,CAEf;aAGA;aAAA,0BAAgB;QAEf,IAAI,CAAC,WAAU,GAAI,SAAO,IAAI,CAAC,KAAK,GAAA,OAAK,IAAI,CAAC,KAAK,GAAA,OAAK,IAAI,CAAC,KAAK,GAAA;QAClE,IAAI,CAAC,YAAW,GAAI,KAAI;IACzB;aAGA;aAAA,qBAAc,OAAO,oBAAoB,EAAA;QACxC,IAAI,CAAC,KAAI,GAAI,MAAM,MAAM,CAAC,KAAI,CAAA,EAAA,CAAK,MAAK;IACzC;aAGA;aAAA,qBAAc,OAAO,oBAAoB,EAAA;QACxC,IAAI,CAAC,KAAI,GAAI,MAAM,MAAM,CAAC,KAAI,CAAA,EAAA,CAAK,MAAK;IACzC;aAGA;aAAA,qBAAc,OAAO,oBAAoB,EAAA;QACxC,IAAI,CAAC,KAAI,GAAI,MAAM,MAAM,CAAC,KAAI,CAAA,EAAA,CAAK,MAAK;IACzC;aAGA;aAAA,oBAAa,OAAO,aAAa,EAAA;QAChC,IAAM,QAAQ,SAAS,MAAM,MAAM,CAAC,KAAK;QACzC,IAAI,CAAC,MAAM,QAAQ;YAClB,IAAI,CAAC,KAAI,GAAI,KAAK,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,GAAG,EAAE;;IAEzC;aAGA;aAAA,oBAAa,OAAO,aAAa,EAAA;QAChC,IAAM,QAAQ,SAAS,MAAM,MAAM,CAAC,KAAK;QACzC,IAAI,CAAC,MAAM,QAAQ;YAClB,IAAI,CAAC,KAAI,GAAI,KAAK,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,GAAG,EAAE;;IAEzC;aAGA;aAAA,oBAAa,OAAO,aAAa,EAAA;QAChC,IAAM,QAAQ,SAAS,MAAM,MAAM,CAAC,KAAK;QACzC,IAAI,CAAC,MAAM,QAAQ;YAClB,IAAI,CAAC,KAAI,GAAI,KAAK,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,GAAG,EAAE;;IAEzC;aAGA;aAAA,cAAI;QACH,IAAI,CAAC,OAAM,GAAI,IAAG;IACnB;aAGA;aAAA,eAAK;QACJ,IAAI,CAAC,OAAM,GAAI,KAAI;IACpB;aAGA;aAAA,wBAAc;QACb,IAAI,CAAC,KAAK;QACV,IAAI,CAAC,OAAK,CAAC;IACZ;aAGA;aAAA,kBAAQ;QACP,IAAI,CAAC,KAAK;QACV,IAAI,CAAC,OAAK,CAAC;IACZ;aAGA;aAAA,mBAAS;QACR,IAAI,CAAC,KAAK;QACV,IAAM,aAAa,IAAI,CAAC,aAAa;QACrC,IAAI,CAAC,OAAK,CAAC,WAAW,IACrB,WAAO,IAAI,CAAC,UAAU,EACtB,UAAM,YACN,SAAK,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,WAAW,CAAC,EAAE,WAAW,CAAC;IAE7D;aAGA;aAAA,wBAAkB,WAAS;QAC1B,IAAM,YAAY,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;QACxC,IAAI,aAAa,IAAI,EAAE;YACtB,kBACC,IAAG,SAAS,SAAS,CAAC,CAAC,CAAA,CAAA,EAAA,CAAK,MAAM,GAClC,IAAG,SAAS,SAAS,CAAC,CAAC,CAAA,CAAA,EAAA,CAAK,MAAM,GAClC,IAAG,SAAS,SAAS,CAAC,CAAC,CAAA,CAAA,EAAA,CAAK,MAAM,GAClC,IAAG,WAAW,SAAS,CAAC,CAAC,CAAA,CAAA,EAAA,CAAK,MAAM;;QAGtC,kBAAS,IAAG,GAAG,EAAE,IAAG,CAAC,EAAE,IAAG,CAAC,EAAE,IAAG,GAAE;IACnC;aAGA;aAAA,gBAAS,GAAG,MAAM,EAAE,GAAG,MAAM,EAAE,GAAG,MAAM,GAAG,UAAQ;QAClD,IAAM,GAAG,MAAK,GAAI,IAAI;QACtB,IAAM,GAAG,MAAK,GAAI,IAAI,CAAC,GAAE,GAAI,KAAK,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,GAAG,CAAC;QAC/D,IAAM,GAAG,MAAK,GAAI,IAAI;QAEtB,IAAI,GAAG,MAAK,GAAI,GAAE;QAClB,IAAI,GAAG,MAAK,GAAI,GAAE;QAClB,IAAI,GAAG,MAAK,GAAI,GAAE;QAElB,IAAI,KAAK,CAAA,IAAK,IAAI,EAAE,EAAE;YACrB,IAAI;YACJ,IAAI;YACJ,IAAI,GAAE;eACA,IAAI,KAAK,EAAC,IAAK,IAAI,GAAG,EAAE;YAC9B,IAAI;YACJ,IAAI;YACJ,IAAI,GAAE;eACA,IAAI,KAAK,GAAE,IAAK,IAAI,GAAG,EAAE;YAC/B,IAAI,GAAE;YACN,IAAI;YACJ,IAAI;eACE,IAAI,KAAK,GAAE,IAAK,IAAI,GAAG,EAAE;YAC/B,IAAI,GAAE;YACN,IAAI;YACJ,IAAI;eACE,IAAI,KAAK,GAAE,IAAK,IAAI,GAAG,EAAE;YAC/B,IAAI;YACJ,IAAI,GAAE;YACN,IAAI;eACE,IAAI,KAAK,GAAE,IAAK,IAAI,GAAG,EAAE;YAC/B,IAAI;YACJ,IAAI,GAAE;YACN,IAAI;;QAGL,IAAM,mBACL,IAAG,KAAK,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,KAAK,GAC7B,IAAG,KAAK,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,KAAK,GAC7B,IAAG,KAAK,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,KAAK;QAE9B,OAAO;IACR;aAGA;aAAA,gBAAS,GAAG,MAAM,EAAE,GAAG,MAAM,EAAE,GAAG,MAAM,GAAG,MAAK,CAAA;QAC/C,IAAM,QAAQ,IAAC,OAAO,MAAM,GAAG,MAAK,CAAG;YACtC,IAAM,MAAM,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,GAAG,EAAE,SAAS,QAAQ,CAAC,EAAE;YACrE,OAAO,IAAA,IAAI,MAAK,IAAK,CAAA;gBAAI,MAAM;;gBAAM;;QACtC;QACA,OAAO,MAAM,MAAM,KAAK,MAAM,KAAK,MAAM;IAC1C;;mBAtYK;;;;;;;;;;;;;;;;;AA0YP"}