<template>
	<view class="Mselect">
		
		
		
		<view class="Mdate">
			<view class="Mselect-name" :style="{color:color}">
				{{data.name}}
			</view>
				
			<view class="Mselect-box qShadow1" :style="{backgroundColor:bgColor}" @click="selectFun">
				<view class="mb-selectText">
					{{mText}}
				</view>
				
				<view class="mb-selectIcon">
						<mb-icons type="Clock-1" color="#333" size="40rpx"></mb-icons>
				</view>
			</view>
		</view>
		
		 
		
		<uv-datetime-picker ref="datetimePicker" v-model="mText" mode="time" @confirm="confirm">
				</uv-datetime-picker>
	
	</view>
	
	
	
</template>

<script>

	export default {
		name: "Mselect",
		props: {
			data: {
				type: Object,
				default: {}
			},
			keyName: {
				type: String,
				default: ""
			
			},
			index: {
				type: Number,
				default: 0
			},
			color: {
				type: String,
				default: '#000'
			},
			bgColor: {
				type: String,
				default: "#f1f4f9"
			
			}
		},
		created() {
			
			if(this.data.value==""){
				
				
				let hours = now.getHours();
				let minutes = now.getMinutes();
				
				// 格式化小时和分钟，确保是两位数
				let formattedHours = hours < 10 ? '0' + hours : hours;
				let formattedMinutes = minutes < 10 ? '0' + minutes : minutes;
				
				// 构建最终的时间字符串
				let currentTime = formattedHours + ':' + formattedMinutes;
				this.mText=currentTime
				this.$emit("change",{index:this.index,value:currentTime})
				
			}
		
		},
		data() {
			return {
				selectData:[],
				mText:"",
				mydate:"",
				dateshow:false
			};
		},
		watch:{
			data: {
				
				handler(newValue, oldValue) {
					
					console.log(newValue)
					if(this.data.value!=""){
						
						this.mText=String(this.data.value)
					}
				},
				immediate: true,
				deep: true
			}
			
		},
		methods:{
			confirm(e){
				
				this.mText =e.value
				this.$emit("change",{index:this.index,value:e.value})
				console.log(e)
			},
	
			selectFun(){
				this.$refs.datetimePicker.open();
				
			}
		}
	}
</script>

<style>
.Mdate {
		width: 100%;
		display: flex;
		flex-direction: column;
		padding: 0 20rpx;
		margin-bottom: 20rpx;
	
	}
	
	.Mselect-name {
		width: 100%;
		font-size: 32rpx;
	}
	
	.Mselect-box {
		width: 100%;
		height: 100rpx;
		border: 1rpx solid #fff;
		box-sizing: border-box;
		padding: 0 10rpx;
		border-radius: 20rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
	}
	
	.mb-selectText {
		flex: 1;
		padding-left: 20rpx;
	}
	
	.mb-selectIcon {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}
	.mb-selectIcon image{
		width: 100%;
		height: 100%;
	}
</style>