<template>
	<form-container :label="fieldName" :show-error="showError" :tip="tip" :error-message="errorMessage" :label-color="labelColor"
		:background-color="backgroundColor">
		<template #input-content>
			<view class="numberbox-container">
				<view class="numberbox-btn qShadow1" @click="numberFun('-')">
					<text class="numberbox-btn-text">-</text>
				</view>
				<view class="numberbox-input-wrapper">
					<input
						class="numberbox-input"
						type="number"
						v-model="inputValue"
						@input="onInputChange"
						@blur="onInputBlur"
					/>
				</view>
				<view class="numberbox-btn qShadow1" @click="numberFun('+')">
					<text class="numberbox-btn-text">+</text>
				</view>
				<view class="numberbox-unit" v-if="unitText">
					<text class="numberbox-unit-text">{{ unitText }}</text>
				</view>
			</view>
		</template>
	</form-container>
</template>

<script lang="uts">
	import { FormFieldData, FormChangeEvent } from '@/components/main-form/form_type.uts'
	import FormContainer from './form-container.uvue'

	export default {
		name: "FormNumberbox",
		emits: ['change'],
		components: {
			FormContainer
		},
		props: {
			data: {
				type: null as any as PropType<FormFieldData>
			},
			index: {
				type: Number,
				default: 0
			},
			keyName: {
				type: String,
				default: ""
			},
			labelColor: {
				type: String,
				default: "#000"
			},
			backgroundColor: {
				type: String,
				default: "#f1f4f9"
			}
		},
		data() {
			return {
				fieldName: "",
				fieldValue: 0,
				isSave: false,
				save_key: "",
				tip: "",
				minValue: 0,
				maxValue: 100,
				stepValue: 1,
				inputValue: "0",
				unitText: "",
				showError: false,
				errorMessage: ""
			}
		},
		computed: {

		},
		watch: {
			data: {
				handler(obj: FormFieldData) {
					// 只处理value的变化，当外部传入的value与当前fieldValue不同时，才更新fieldValue
					const newValue = obj.value as number
					if (newValue !== this.fieldValue) {
						this.fieldValue = newValue
						this.updateInputValue()
					}
				},
				deep: true
			}
		},
		created(): void {
			// 初始化时调用一次即可
			const fieldObj = this.$props["data"] as FormFieldData
			this.initFieldData(fieldObj)
		},
		methods: {
			// 初始化字段数据（仅在首次加载时调用）
			initFieldData(fieldObj: FormFieldData): void {
				const fieldKey = fieldObj.key
				const fieldValue = fieldObj.value as number

				// 设置基本信息
				this.fieldName = fieldObj.name
				this.fieldValue = fieldValue
				this.isSave = fieldObj.isSave ?? false
				this.save_key = this.keyName + "_" + fieldKey

				// 解析配置信息
				const extalJson = fieldObj.extra as UTSJSONObject
				this.minValue = extalJson.getNumber("min") ?? 0
				this.maxValue = extalJson.getNumber("max") ?? 100
				this.stepValue = extalJson.getNumber("step") ?? 1
				this.tip = extalJson.getString("tip") ?? ""
				this.unitText = extalJson.getString("unit") ?? ""

				// 更新输入框的值
				this.updateInputValue()

				// 获取缓存
				setTimeout(() => {
					this.getCache()
				}, 500)
			},

			// 更新输入框的值
			updateInputValue(): void {
				this.inputValue = this.fieldValue.toString()
			},

			// 验证值是否在有效范围内
			validateValue(value: number): number {
				if (value < this.minValue) {
					return this.minValue
				}
				if (value > this.maxValue) {
					return this.maxValue
				}

				// 处理步长
				if (this.stepValue % 1 == 0) {
					// 整数步长
					return Math.round(value)
				} else {
					// 小数步长，保留一位小数
					return Number.from(value.toFixed(1))
				}
			},

			getCache(): void {
				if (this.isSave) {
					const that = this
					uni.getStorage({
						key: this.save_key,
						success: (res: GetStorageSuccess) => {
							const save_value = res.data as number
							const validatedValue = that.validateValue(save_value)
							that.fieldValue = validatedValue
							that.updateInputValue()
							const result: FormChangeEvent = {
								index: this.index,
								value: validatedValue
							}
							this.change(result)
						}
					})
				}
			},

			setCache(): void {
				if (this.isSave) {
					uni.setStorage({
						key: this.save_key,
						data: this.fieldValue
					})
				}
			},

			validate(): boolean {
				// 数字输入框组件通常不需要额外验证，因为值已经被限制在min-max范围内
				this.showError = false
				this.errorMessage = ""
				return true
			},

			change(event: FormChangeEvent): void {
				// 更新字段值
				this.fieldValue = event.value as number
				// 保存缓存
				this.setCache()
				// 触发父组件事件
				this.$emit('change', event)
			},

			// 按钮点击事件
			numberFun(type: string): void {
				let value: number
				let num: number = this.fieldValue

				if (type == "+") {
					num = num + this.stepValue
				} else {
					num = num - this.stepValue
				}

				// 验证并限制值
				value = this.validateValue(num)

				// 更新输入框显示
				this.inputValue = value.toString()

				const result: FormChangeEvent = {
					index: this.index,
					value: value
				}
				this.change(result)
			},

			onInputChange(event: UniInputEvent): void {
				const inputStr = event.detail.value as string
				const inputNum = parseFloat(inputStr)

				if (!isNaN(inputNum)) {
					const validatedValue = this.validateValue(inputNum)

					const result: FormChangeEvent = {
						index: this.index,
						value: validatedValue
					}
					this.change(result)
				}
			},

			onInputBlur(): void {
				// 在失去焦点时进行验证和格式化
				const inputNum = parseFloat(this.inputValue)
				if (isNaN(inputNum)) {
					// 如果输入无效，恢复到当前字段值
					this.inputValue = this.fieldValue.toString()
				} else {
					// 验证并格式化输入值
					const validatedValue = this.validateValue(inputNum)
					this.inputValue = validatedValue.toString()

					if (validatedValue !== this.fieldValue) {
						const result: FormChangeEvent = {
							index: this.index,
							value: validatedValue
						}
						this.change(result)
					}
				}
				this.validate()
			}
		}
	}
</script>

<style>
	.numberbox-container {
		width: 100%;
		display: flex;
		flex-direction: row;
		align-items: center;
	}

	.numberbox-btn {
		width: 80rpx;
		height: 80rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		background-color: #e1e1e1;
		border-radius: 10rpx;
	}

	.numberbox-btn-text {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
	}

	.numberbox-input-wrapper {
		flex: 1;
		height: 80rpx;
		background-color: #f2f4f8;
		margin: 0 20rpx;
		border-radius: 10rpx;
	}

	.numberbox-input {
		width: 100%;
		height: 100%;
		text-align: center;
		font-size: 32rpx;
		border: none;
		background-color: transparent;
	}

	.numberbox-unit {
		padding: 0 10rpx;
		height: 80rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		background-color: #333;
		border-radius: 10rpx;
		margin-left: 20rpx;
	}

	.numberbox-unit-text {
		color: #fff;
		font-size: 28rpx;
	}

	/* 阴影样式 */
	.qShadow1 {
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	}
</style>