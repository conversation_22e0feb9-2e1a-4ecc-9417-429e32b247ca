@file:Suppress("UNCHECKED_CAST", "USELESS_CAST", "INAPPLICABLE_JVM_NAME", "UNUSED_ANONYMOUS_PARAMETER", "NAME_SHADOWING", "UNNECESSARY_NOT_NULL_ASSERTION")
package uni.UNIC178CB1
import io.dcloud.uniapp.*
import io.dcloud.uniapp.extapi.*
import io.dcloud.uniapp.framework.*
import io.dcloud.uniapp.runtime.*
import io.dcloud.uniapp.vue.*
import io.dcloud.uniapp.vue.shared.*
import io.dcloud.unicloud.*
import io.dcloud.uts.*
import io.dcloud.uts.Map
import io.dcloud.uts.Set
import io.dcloud.uts.UTSAndroid
import io.dcloud.uniapp.extapi.showModal as uni_showModal
open class GenComponentsMainFormComponentsFormContainer : VueComponent {
    constructor(__ins: ComponentInternalInstance) : super(__ins) {}
    @Suppress("UNUSED_PARAMETER", "UNUSED_VARIABLE")
    override fun `$render`(): Any? {
        val _ctx = this
        val _cache = this.`$`.renderCache
        return _cE("view", _uM("class" to "form-container-body"), _uA(
            _cE("view", _uM("class" to "form-container"), _uA(
                if (isTrue(_ctx.label)) {
                    _cE("view", _uM("key" to 0, "class" to "form-container-label"), _uA(
                        _cE("view", _uM("class" to "form-container-name"), _uA(
                            _cE("text", _uM("class" to "form-container-name-text", "style" to _nS(_uM("color" to _ctx.labelColor))), _tD(_ctx.label), 5),
                            if (_ctx.tip != "") {
                                _cE("view", _uM("key" to 0, "class" to "form-container-tip-icon", "onClick" to _ctx.showTipModal), _uA(
                                    _cE("text", _uM("class" to "form-container-tip-text"), "!")
                                ), 8, _uA(
                                    "onClick"
                                ))
                            } else {
                                _cC("v-if", true)
                            }
                        ))
                    ))
                } else {
                    _cC("v-if", true)
                }
                ,
                _cE("view", _uM("class" to "form-container-box", "style" to _nS(_uM("backgroundColor" to _ctx.backgroundColor, "borderColor" to if (_ctx.showError) {
                    "red"
                } else {
                    "#fff"
                }
                ))), _uA(
                    renderSlot(_ctx.`$slots`, "input-content")
                ), 4)
            )),
            if (isTrue(_ctx.showError)) {
                _cE("view", _uM("key" to 0, "class" to "form-container-tip"), _uA(
                    _cE("text", _uM("class" to "form-container-error"), _tD(_ctx.errorMessage), 1)
                ))
            } else {
                _cC("v-if", true)
            }
        ))
    }
    open var label: String by `$props`
    open var showError: Boolean by `$props`
    open var errorMessage: String by `$props`
    open var tip: String by `$props`
    open var labelColor: String by `$props`
    open var backgroundColor: String by `$props`
    open var showTipModal = ::gen_showTipModal_fn
    open fun gen_showTipModal_fn() {
        if (this.tip != "") {
            uni_showModal(ShowModalOptions(title = "提示", content = this.tip, showCancel = false, confirmText = "知道了"))
        }
    }
    companion object {
        var name = "FormContainer"
        val styles: Map<String, Map<String, Map<String, Any>>> by lazy {
            _nCS(_uA(
                styles0
            ))
        }
        val styles0: Map<String, Map<String, Map<String, Any>>>
            get() {
                return _uM("form-container-body" to _pS(_uM("width" to "100%", "display" to "flex", "flexDirection" to "column", "paddingTop" to 0, "paddingRight" to "20rpx", "paddingBottom" to 0, "paddingLeft" to "20rpx", "marginBottom" to "20rpx")), "form-container" to _pS(_uM("width" to "100%", "display" to "flex", "flexDirection" to "column")), "form-container-label" to _pS(_uM("width" to "100%", "marginBottom" to "10rpx")), "form-container-name" to _pS(_uM("display" to "flex", "flexDirection" to "row", "alignItems" to "center")), "form-container-name-text" to _pS(_uM("fontSize" to "30rpx")), "form-container-tip-icon" to _pS(_uM("width" to "40rpx", "height" to "40rpx", "backgroundColor" to "rgba(0,0,0,0.05)", "borderTopLeftRadius" to "20rpx", "borderTopRightRadius" to "20rpx", "borderBottomRightRadius" to "20rpx", "borderBottomLeftRadius" to "20rpx", "display" to "flex", "justifyContent" to "center", "alignItems" to "center", "marginLeft" to "10rpx")), "form-container-tip-text" to _pS(_uM("fontSize" to "24rpx", "color" to "#666666", "fontWeight" to "bold")), "form-container-box" to _pS(_uM("width" to "100%", "borderTopWidth" to "1rpx", "borderRightWidth" to "1rpx", "borderBottomWidth" to "1rpx", "borderLeftWidth" to "1rpx", "borderTopStyle" to "solid", "borderRightStyle" to "solid", "borderBottomStyle" to "solid", "borderLeftStyle" to "solid", "borderTopColor" to "#ffffff", "borderRightColor" to "#ffffff", "borderBottomColor" to "#ffffff", "borderLeftColor" to "#ffffff", "boxSizing" to "border-box", "paddingTop" to "20rpx", "paddingRight" to "20rpx", "paddingBottom" to "20rpx", "paddingLeft" to "20rpx", "borderTopLeftRadius" to "20rpx", "borderTopRightRadius" to "20rpx", "borderBottomRightRadius" to "20rpx", "borderBottomLeftRadius" to "20rpx", "display" to "flex", "flexDirection" to "row", "alignItems" to "center")), "form-container-tip" to _pS(_uM("width" to "100%", "marginTop" to "10rpx")), "form-container-error" to _pS(_uM("color" to "#FF0000", "fontSize" to "28rpx", "textAlign" to "right")), "form-input-element" to _pS(_uM("flex" to 1, "minHeight" to "60rpx")), "qShadow1" to _pS(_uM("boxShadow" to "0 2rpx 8rpx rgba(0, 0, 0, 0.1)")))
            }
        var inheritAttrs = true
        var inject: Map<String, Map<String, Any?>> = _uM()
        var emits: Map<String, Any?> = _uM()
        var props = _nP(_uM("label" to _uM("type" to "String", "default" to ""), "showError" to _uM("type" to "Boolean", "default" to false), "errorMessage" to _uM("type" to "String", "default" to ""), "tip" to _uM("type" to "String", "default" to ""), "labelColor" to _uM("type" to "String", "default" to "#000"), "backgroundColor" to _uM("type" to "String", "default" to "#f1f4f9")))
        var propsNeedCastKeys = _uA(
            "label",
            "showError",
            "errorMessage",
            "tip",
            "labelColor",
            "backgroundColor"
        )
        var components: Map<String, CreateVueComponent> = _uM()
    }
}
