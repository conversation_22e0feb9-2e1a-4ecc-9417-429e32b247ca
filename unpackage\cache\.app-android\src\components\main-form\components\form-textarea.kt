@file:Suppress("UNCHECKED_CAST", "USELESS_CAST", "INAPPLICABLE_JVM_NAME", "UNUSED_ANONYMOUS_PARAMETER", "NAME_SHADOWING", "UNNECESSARY_NOT_NULL_ASSERTION")
package uni.UNIC178CB1
import io.dcloud.uniapp.*
import io.dcloud.uniapp.extapi.*
import io.dcloud.uniapp.framework.*
import io.dcloud.uniapp.runtime.*
import io.dcloud.uniapp.vue.*
import io.dcloud.uniapp.vue.shared.*
import io.dcloud.unicloud.*
import io.dcloud.uts.*
import io.dcloud.uts.Map
import io.dcloud.uts.Set
import io.dcloud.uts.UTSAndroid
import io.dcloud.uniapp.extapi.getStorage as uni_getStorage
import io.dcloud.uniapp.extapi.setStorage as uni_setStorage
open class GenComponentsMainFormComponentsFormTextarea : VueComponent {
    constructor(__ins: ComponentInternalInstance) : super(__ins) {
        onCreated(fun(): Unit {
            val fieldObj = this.`$props`["data"] as FormFieldData
            this.initFieldData(fieldObj)
        }
        , __ins)
        this.`$watch`(fun(): Any? {
            return this.data
        }
        , fun(obj: FormFieldData) {
            val newValue = obj.value as String
            if (newValue !== this.fieldValue) {
                this.fieldValue = newValue
            }
        }
        , WatchOptions(deep = true))
    }
    @Suppress("UNUSED_PARAMETER", "UNUSED_VARIABLE")
    override fun `$render`(): Any? {
        val _ctx = this
        val _cache = this.`$`.renderCache
        val _component_form_container = resolveComponent("form-container")
        return _cV(_component_form_container, _uM("label" to _ctx.fieldName, "show-error" to _ctx.showError, "tip" to _ctx.tip, "error-message" to _ctx.errorMessage, "label-color" to _ctx.labelColor, "background-color" to _ctx.backgroundColor), _uM("input-content" to withSlotCtx(fun(): UTSArray<Any> {
            return _uA(
                _cE("textarea", _uM("class" to "form-textarea-element", "modelValue" to _ctx.fieldValue, "onInput" to _uA(
                    fun(`$event`: UniInputEvent){
                        _ctx.fieldValue = `$event`.detail.value
                    }
                    ,
                    _ctx.onInput
                ), "maxlength" to _ctx.maxLength, "placeholder" to _ctx.placeholder, "onBlur" to _ctx.onBlur), null, 40, _uA(
                    "modelValue",
                    "onInput",
                    "maxlength",
                    "placeholder",
                    "onBlur"
                ))
            )
        }
        ), "_" to 1), 8, _uA(
            "label",
            "show-error",
            "tip",
            "error-message",
            "label-color",
            "background-color"
        ))
    }
    open var data: FormFieldData? by `$props`
    open var index: Number by `$props`
    open var keyName: String by `$props`
    open var labelColor: String by `$props`
    open var backgroundColor: String by `$props`
    open var fieldName: String by `$data`
    open var fieldValue: String by `$data`
    open var isSave: Boolean by `$data`
    open var save_key: String by `$data`
    open var tip: String by `$data`
    open var placeholder: String by `$data`
    open var minLength: Number by `$data`
    open var maxLength: Number by `$data`
    open var showError: Boolean by `$data`
    open var errorMessage: String by `$data`
    @Suppress("USELESS_CAST")
    override fun data(): Map<String, Any?> {
        return _uM("fieldName" to "", "fieldValue" to "", "isSave" to false, "save_key" to "", "tip" to "", "placeholder" to "", "minLength" to 0, "maxLength" to -1, "showError" to false, "errorMessage" to "")
    }
    open var initFieldData = ::gen_initFieldData_fn
    open fun gen_initFieldData_fn(fieldObj: FormFieldData): Unit {
        val fieldKey = fieldObj.key
        val fieldValue = fieldObj.value as String
        this.fieldName = fieldObj.name
        this.fieldValue = fieldValue
        this.isSave = fieldObj.isSave ?: false
        this.save_key = this.keyName + "_" + fieldKey
        val extalJson = fieldObj.extra as UTSJSONObject
        this.minLength = extalJson.getNumber("minLength") ?: 0
        this.maxLength = extalJson.getNumber("maxLength") ?: -1
        this.placeholder = extalJson.getString("placeholder") ?: ""
        this.tip = extalJson.getString("tip") ?: ""
        this.getCache()
    }
    open var getCache = ::gen_getCache_fn
    open fun gen_getCache_fn(): Unit {
        if (this.isSave) {
            val that = this
            uni_getStorage(GetStorageOptions(key = this.save_key, success = fun(res: GetStorageSuccess){
                val save_value = res.data as String
                that.fieldValue = save_value
                this.manualChange()
            }
            ))
        }
    }
    open var setCache = ::gen_setCache_fn
    open fun gen_setCache_fn(): Unit {
        if (this.isSave) {
            uni_setStorage(SetStorageOptions(key = this.save_key, data = this.fieldValue))
        }
    }
    open var validate = ::gen_validate_fn
    open fun gen_validate_fn(): Boolean {
        if (this.maxLength == -1) {
            if (this.minLength > 0 && this.fieldValue.length < this.minLength) {
                this.showError = true
                this.errorMessage = "\u8F93\u5165\u5185\u5BB9\u4E0D\u80FD\u5C11\u4E8E" + this.minLength + "\u4E2A\u5B57\u7B26"
                return false
            }
            this.showError = false
            this.errorMessage = ""
            return true
        }
        if (this.fieldValue.length < this.minLength) {
            this.showError = true
            this.errorMessage = "\u8F93\u5165\u5185\u5BB9\u4E0D\u80FD\u5C11\u4E8E" + this.minLength + "\u4E2A\u5B57\u7B26"
            return false
        }
        if (this.maxLength > 0 && this.fieldValue.length > this.maxLength) {
            this.showError = true
            this.errorMessage = "\u8F93\u5165\u5185\u5BB9\u4E0D\u80FD\u8D85\u8FC7" + this.maxLength + "\u4E2A\u5B57\u7B26"
            return false
        }
        this.showError = false
        this.errorMessage = ""
        return true
    }
    open var change = ::gen_change_fn
    open fun gen_change_fn(event: FormChangeEvent): Unit {
        this.fieldValue = event.value as String
        this.setCache()
        this.`$emit`("change", event)
    }
    open var manualChange = ::gen_manualChange_fn
    open fun gen_manualChange_fn(): Unit {
        val result = FormChangeEvent(index = this.index, value = this.fieldValue)
        this.change(result)
    }
    open var onInput = ::gen_onInput_fn
    open fun gen_onInput_fn(event: UniInputEvent): Unit {
        val result = FormChangeEvent(index = this.index, value = event.detail.value)
        this.change(result)
    }
    open var onBlur = ::gen_onBlur_fn
    open fun gen_onBlur_fn(): Unit {
        this.validate()
    }
    companion object {
        var name = "FormTextarea"
        val styles: Map<String, Map<String, Map<String, Any>>> by lazy {
            _nCS(_uA(
                styles0
            ))
        }
        val styles0: Map<String, Map<String, Map<String, Any>>>
            get() {
                return _uM("form-textarea-element" to _pS(_uM("flex" to 1, "minHeight" to "120rpx")))
            }
        var inheritAttrs = true
        var inject: Map<String, Map<String, Any?>> = _uM()
        var emits: Map<String, Any?> = _uM()
        var props = _nP(_uM("data" to _uM("type" to "Object"), "index" to _uM("type" to "Number", "default" to 0), "keyName" to _uM("type" to "String", "default" to ""), "labelColor" to _uM("type" to "String", "default" to "#000"), "backgroundColor" to _uM("type" to "String", "default" to "#f1f4f9")))
        var propsNeedCastKeys = _uA(
            "index",
            "keyName",
            "labelColor",
            "backgroundColor"
        )
        var components: Map<String, CreateVueComponent> = _uM("FormContainer" to GenComponentsMainFormComponentsFormContainerClass)
    }
}
