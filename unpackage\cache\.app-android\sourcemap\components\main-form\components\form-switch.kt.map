{"version": 3, "sources": ["components/main-form/components/form-switch.uvue", "components/main-form/components/form-input.uvue"], "sourcesContent": ["<template>\n\t<form-container :label=\"fieldName\" :show-error=\"showError\" :tip=\"tip\" :error-message=\"errorMessage\" :label-color=\"labelColor\"\n\t\t:background-color=\"backgroundColor\">\n\t\t<template #input-content>\n\t\t\t<view class=\"switch-box\">  \r\n\t\t\t\t<switch class=\"form-switch-element\" :color=\"switchColor\" :checked=\"switchValue\" @change=\"handleChange\" />\r\n\t\t\t</view>\n\t\t</template>\n\t</form-container>\n</template>\n\n<script lang=\"uts\">\n\timport { FormFieldData, FormChangeEvent } from '@/components/main-form/form_type.uts'\n\timport FormContainer from './form-container.uvue'\n\n\texport default {\n\t\tname: \"FormSwitch\",\n\t\tcomponents: {\n\t\t\tFormContainer\n\t\t},\n\t\tprops: {\n\t\t\tdata: {\n\t\t\t\ttype: Object as PropType<FormFieldData>\n\t\t\t},\n\t\t\tindex: {\n\t\t\t\ttype: Number,\n\t\t\t\tdefault: 0\n\t\t\t},\n\t\t\tkeyName: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"\"\n\t\t\t},\n\t\t\tlabelColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#000\"\n\t\t\t},\n\t\t\tbackgroundColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#f1f4f9\"\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tfieldName: \"\",\n\t\t\t\tfieldValue: null as any | null,\n\t\t\t\tisSave: false,\n\t\t\t\tsave_key: \"\",\n\t\t\t\ttip: \"\",\n\t\t\t\tvarType: \"number\",\n\t\t\t\tswitchValue: false,\n\t\t\t\tswitchColor: \"#8A6DE9\",\n\t\t\t\tshowError: false,\n\t\t\t\terrorMessage: \"\"\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\n\t\t},\n\t\twatch: {\n\t\t\tdata: {\n\t\t\t\thandler(obj: FormFieldData) {\n\t\t\t\t\t// 只处理value的变化，当外部传入的value与当前fieldValue不同时，才更新fieldValue\n\t\t\t\t\t// 这避免了用户输入时的循环更新问题\n\t\t\t\t\tconst newValue = obj.value\n\t\t\t\t\tif (newValue !== this.fieldValue) {\r\n\t\t\t\t\t\tconsole.log(\"触发改变\")\n\t\t\t\t\t\tthis.fieldValue = newValue\n\t\t\t\t\t\tthis.updateSwitchValue()\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tdeep: true\n\t\t\t}\n\t\t},\n\t\tcreated(): void {\n\t\t\t// 初始化时调用一次即可\n\t\t\tconst fieldObj = this.$props[\"data\"] as FormFieldData\n\t\t\tthis.initFieldData(fieldObj)\n\t\t},\n\t\tmethods: {\n\t\t\t// 初始化字段数据（仅在首次加载时调用）\n\t\t\tinitFieldData(fieldObj: FormFieldData): void {\n\t\t\t\tconst fieldKey = fieldObj.key\n\t\t\t\tconst fieldValue = fieldObj.value\n\n\t\t\t\t// 设置基本信息\n\t\t\t\tthis.fieldName = fieldObj.name\n\t\t\t\tthis.fieldValue = fieldValue\n\t\t\t\tthis.isSave = fieldObj.isSave ?? false\n\t\t\t\tthis.save_key = this.keyName + \"_\" + fieldKey\n\t\t\t\t// 解析配置信息\n\t\t\t\tconst extalJson = fieldObj.extra as UTSJSONObject\n\t\t\t\tthis.tip = extalJson.getString(\"tip\") ?? \"\"\n\t\t\t\tthis.varType = extalJson.getString(\"varType\") ?? \"number\"\n\t\t\t\t\r\n\t\t\t\t\r\n\t\t\t\t\r\n\t\t\t\tthis.updateSwitchValue() \n\t\t\t\t\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tthis.getCache()\t\r\n\t\t\t\t},500)\n\t\t\t\t\n\t\t\t},\n\n\t\t\t// 更新开关值（根据 fieldValue 和 varType）\n\t\t\tupdateSwitchValue(): void {\r\n\t\t\t\t\r\n\t\t\t\tconst valueType :string=typeof this.fieldValue\r\n\t\t\t\tif(valueType!=this.varType){\r\n\t\t\t\t\tconsole.log(\"类型不匹配\")\r\n\t\t\t\t\tthis.switchValue = false\r\n\t\t\t\t\t if (this.varType == \"number\") {\r\n\t\t\t\t\t\tthis.fieldValue = 0\r\n\t\t\t\t\t} else{\r\n\t\t\t\t\t\tthis.fieldValue =false\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\n\t\t\t\tif (this.varType == \"boolean\") {\n\t\t\t\t\t// boolean 类型：直接使用布尔值\n\t\t\t\t\tthis.switchValue = this.fieldValue as boolean\n\t\t\t\t} else if (this.varType == \"number\") {\n\t\t\t\t\t// number 类型：判断是否为 1\n\t\t\t\t\tthis.switchValue = this.fieldValue == 1\n\t\t\t\t} else {\n\t\t\t\t\t// 默认按 number 处理\n\t\t\t\t\tthis.switchValue = this.fieldValue == 1\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t\n\t\t\t},\n\n\t\t\tgetCache(): void {\n\t\t\t\tif (this.isSave) {\n\t\t\t\t\tconst that = this\n\t\t\t\t\tuni.getStorage({\n\t\t\t\t\t\tkey: this.save_key,\n\t\t\t\t\t\tsuccess: (res: GetStorageSuccess) => {\n\t\t\t\t\t\t\tconst save_value = res.data\r\n\t\t\t\t\t\t\tif(typeof save_value === 'boolean'){\r\n\t\t\t\t\t\t\t\tthat.convertAndSetValue(save_value)\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tsetCache(cacheValue : boolean): void {\n\t\t\t\tif (this.isSave) {\n\t\t\t\t\t\n\t\t\t\t\tuni.setStorage({\n\t\t\t\t\t\tkey: this.save_key,\n\t\t\t\t\t\tdata: cacheValue\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 根据 varType 转换并设置值\n\t\t\tconvertAndSetValue(isChecked: boolean): void {\r\n\t\t\t\tlet cacheValue: any\r\n\t\t\t\tif (this.varType == \"boolean\") {\r\n\t\t\t\t\t// boolean 类型：直接使用布尔值\r\n\t\t\t\t\tcacheValue = isChecked\r\n\t\t\t\t} \n\t\t\t\telse if (this.varType === \"number\") {\n\t\t\t\t\tcacheValue = isChecked ? 1 : 0\n\t\t\t\t} else {\n\t\t\t\t\tcacheValue = isChecked \n\t\t\t\t}\r\n\t\t\t\tthis.fieldValue=cacheValue \n\t\t\t\tthis.switchValue = isChecked as boolean\r\n\t\t\t\t\r\n\t\t\t\t\r\n\t\t\t\tconst result={\r\n\t\t\t\t\tindex: this.index,\r\n\t\t\t\t\tvalue: cacheValue\r\n\t\t\t\t} as FormChangeEvent\r\n\t\t\t\t\r\n\t\t\t\tthis.$emit(\"change\", result)\r\n\t\t\t\tthis.setCache(isChecked)\n\t\t\t},\n\n\n\t\t\t// 处理开关变更\n\t\t\thandleChange(event: UniSwitchChangeEvent): void {\r\n\t\t\t\tconsole.log(\"触发改变\")\n\t\t\t\tconst isChecked = event.detail.value as boolean\n\t\t\t\t\n\t\t\t\tthis.convertAndSetValue(isChecked)\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\r\n\t.switch-box {\r\n\t\twidth: 100%;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tjustify-content: flex-end;\r\n\t}\n\t.form-switch-element {\n\t\tmargin-left: auto;\n\t}\n</style>\n", null], "names": [], "mappings": ";;;;;;;;;;;;;+BA4GO;+BAeE;AA5GH;;kBA0DJ,OAAW,IAAG,CAAA;YAEb,IAAM,WAAW,IAAI,CAAC,QAAM,CAAC,OAAM,CAAA,EAAA;YACnC,IAAI,CAAC,aAAa,CAAC;QACpB;;;;;UAjBE,IAAQ,kBAAkB,EAAA;YAGzB,IAAM,WAAW,IAAI,KAAI;YACzB,IAAI,aAAa,IAAI,CAAC,UAAU,EAAE;gBACjC,QAAQ,GAAG,CAAC,QAAM;gBAClB,IAAI,CAAC,UAAS,GAAI;gBAClB,IAAI,CAAC,iBAAiB;;QAExB;uBACA,OAAM,IAAG;;;;;;;;eArEZ,IAOiB,2BAAA,IAPA,WAAO,KAAA,SAAS,EAAG,gBAAY,KAAA,SAAS,EAAG,SAAK,KAAA,GAAG,EAAG,mBAAe,KAAA,YAAY,EAAG,iBAAa,KAAA,UAAU,EAC1H,sBAAkB,KAAA,eAAe,OACvB,mBAAa,YACvB,gBAEO,GAAA;mBAAA;gBAFP,IAEO,QAAA,IAFD,WAAM,eAAY;oBACvB,IAAyG,mBAAA,IAAjG,WAAM,uBAAuB,WAAO,KAAA,WAAW,EAAG,aAAS,KAAA,WAAW,EAAG,cAAQ,KAAA,YAAY;;;;;;;;;;;;;;;;;;;;;;aAsCrG;aACA,YAAoB,GAAE;aACtB;aACA;aACA;aACA;aACA;aACA;aACA;aACA;;;mBATA,eAAW,IACX,gBAAY,IAAG,CAAA,EAAA,CAAK,GAAE,GACtB,YAAQ,KAAK,EACb,cAAU,IACV,SAAK,IACL,aAAS,UACT,iBAAa,KAAK,EAClB,iBAAa,WACb,eAAW,KAAK,EAChB,kBAAc;;aA4Bf;aAAA,qBAAc,uBAAuB,GAAG,IAAG,CAAA;QAC1C,IAAM,WAAW,SAAS,GAAE;QAC5B,IAAM,aAAa,SAAS,KAAI;QAGhC,IAAI,CAAC,SAAQ,GAAI,SAAS,IAAG;QAC7B,IAAI,CAAC,UAAS,GAAI;QAClB,IAAI,CAAC,MAAK,GAAI,SAAS,MAAK,IAAK,KAAI;QACrC,IAAI,CAAC,QAAO,GAAI,IAAI,CAAC,OAAM,GAAI,MAAM;QAErC,IAAM,YAAY,SAAS,KAAI,CAAA,EAAA,CAAK;QACpC,IAAI,CAAC,GAAE,GAAI,UAAU,SAAS,CAAC,UAAU;QACzC,IAAI,CAAC,OAAM,GAAI,UAAU,SAAS,CAAC,cAAc;QAIjD,IAAI,CAAC,iBAAiB;QAEtB,WAAW,KAAI;YACd,IAAI,CAAC,QAAQ;QACd;UAAE,GAAG;IAEN;aAGA;aAAA,4BAAqB,IAAG,CAAA;QAEvB,IAAM,WAAW,MAAM,GAAC,oBAAO,IAAI,CAAC,UAAS;QAC7C,IAAG,aAAW,IAAI,CAAC,OAAO,EAAC;YAC1B,QAAQ,GAAG,CAAC,SAAO;YACnB,IAAI,CAAC,WAAU,GAAI,KAAI;YACtB,IAAI,IAAI,CAAC,OAAM,IAAK,UAAU;gBAC9B,IAAI,CAAC,UAAS,GAAI,CAAA;mBACb;gBACL,IAAI,CAAC,UAAS,GAAG,KAAI;;;QAIvB,IAAI,IAAI,CAAC,OAAM,IAAK,WAAW;YAE9B,IAAI,CAAC,WAAU,GAAI,IAAI,CAAC,UAAS,CAAA,EAAA,CAAK,OAAM;eACtC,IAAI,IAAI,CAAC,OAAM,IAAK,UAAU;YAEpC,IAAI,CAAC,WAAU,GAAI,IAAI,CAAC,UAAS,IAAK,CAAA;eAChC;YAEN,IAAI,CAAC,WAAU,GAAI,IAAI,CAAC,UAAS,IAAK,CAAA;;IAIxC;aAEA;aAAA,mBAAY,IAAG,CAAA;QACd,IAAI,IAAI,CAAC,MAAM,EAAE;YAChB,IAAM,OAAO,IAAG;YAChB,iCACC,MAAK,IAAI,CAAC,QAAQ,EAClB,UAAS,IAAC,KAAK,kBAAoB;gBAClC,IAAM,aAAa,IAAI,IAAG;gBAC1B,IAAG,oBAAO,gBAAe,WAAU;oBAClC,KAAK,kBAAkB,CAAC,WAAU,EAAA,CAAA,OAAA;;YAKpC;;;IAGH;aAEA;aAAA,gBAAS,YAAa,OAAO,GAAG,IAAG,CAAA;QAClC,IAAI,IAAI,CAAC,MAAM,EAAE;YAEhB,iCACC,MAAK,IAAI,CAAC,QAAQ,EAClB,OAAM;;IAGT;aAGA;aAAA,0BAAmB,WAAW,OAAO,GAAG,IAAG,CAAA;QAC1C,IAAI,YAAY,GAAE;QAClB,IAAI,IAAI,CAAC,OAAM,IAAK,WAAW;YAE9B,aAAa;eAET,IAAI,IAAI,CAAC,OAAM,KAAM,UAAU;YACnC,aAAa,IAAA;AAAY,iBAAA;;AAAI,iBAAA;aAAA;eACvB;YACN,aAAa;;QAEd,IAAI,CAAC,UAAU,GAAC;QAChB,IAAI,CAAC,WAAU,GAAI,UAAQ,EAAA,CAAK,OAAM;QAGtC,IAAM,yBACL,QAAO,IAAI,CAAC,KAAK,EACjB,QAAO;QAGR,IAAI,CAAC,OAAK,CAAC,UAAU;QACrB,IAAI,CAAC,QAAQ,CAAC;IACf;aAIA;aAAA,oBAAa,OAAO,oBAAoB,GAAG,IAAG,CAAA;QAC7C,QAAQ,GAAG,CAAC,QAAM;QAClB,IAAM,YAAY,MAAM,MAAM,CAAC,KAAI,CAAA,EAAA,CAAK,OAAM;QAE9C,IAAI,CAAC,kBAAkB,CAAC;IACzB;;mBAhLK;;;;;;;;;;;;;+GAUK,CAAA,qDAIA,0DAIA,mEAIA;;;;;;;;;AA4JZ"}