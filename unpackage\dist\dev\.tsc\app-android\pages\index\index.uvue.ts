
	import { FormFieldData } from '@/components/main-form/form_type.uts'
	import mainYearmonthPicker from '@/components/main-form/tools/main-yearmonth-picker.uvue'
	import mainDatetimePicker from '@/components/main-form/tools/main-datetime-picker.uvue'

	// 定义快捷选项类型
	type QuickOption = { __$originalPosition?: UTSSourceMapPosition<"QuickOption", "pages/index/index.uvue", 37, 7>;
		label: string,
		value: Date | Date[],
		autoConfirm?: boolean
	}

	const __sfc__ = defineComponent({
		components:{
			mainYearmonthPicker,
			mainDatetimePicker
		},
		data(){
			return {
				// 日期时间选择器配置
				dateTimeMode: 'datetime' as string,
				dateTimeTitle: '选择日期时间' as string,
				showSeconds: false as boolean,
				quickOptions: [
					{
						label: '现在',
						value: new Date(),
						autoConfirm: true
					},
					{
						label: '明天',
						value: new Date(Date.now() + 24 * 60 * 60 * 1000),
						autoConfirm: false
					},
					{
						label: '下周',
						value: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
						autoConfirm: false
					}
				] as QuickOption[],
			    formConfig: [
					{
						key: "username",
						name: "用户名1",
						type: "input",
						value: "",
						isSave:true,
						extra:{
							minLength: 0,
							maxLength: 20,
							placeholder: "请输入用户名",
							tip:"123",
							inputmode: "digit" 
						}
					},
					{
						key: "password",
						name: "密码",
						type: "input",
						value: "",
						isSave:false,
						extra:{
							minLength: 6,
							maxLength: 20,
							placeholder: "请输入密码",
							tip:"密码请自己保管好",
							inputmode: "number"
						}
					},
					{
						key: "email",
						name: "邮箱地址",
						type: "textarea",
						value: "",
						isSave:true,
						extra:{
							minLength: 6,
							maxLength: 20,
							placeholder: "请输入密码",
							tip:""
						}
						
					},
					{
					    key: "enable_feature",
					    name: "启用功能",
					    type: "switch",
					    value: 1,
					    isSave: false,
					    extra: { 
					        "varType": "number",
					        "tip": "开启后将启用此功能"
					    }
					},
					{
					    key: "slider",
					    name: "slider测试",
					    type: "slider",
					    value: 10,
					    isSave: true,
					    extra: {
					        "min": 0,
					        "max": 100,
							"step":1
					    }
					},
					{
					    key: "numberbox",
					    name: "数量选择",
					    type: "numberbox",
					    value: 5,
					    isSave: true,
					    extra: {
					        "min": 1,
					        "max": 50,
							"step": 1,
							"unit": "个",
							"tip": "请选择数量"
					    }
					},
					{
					    key: "themeColor",
					    name: "主题颜色",
					    type: "color",
					    value: "",
					    isSave: false,
					    extra: {
					        "varType": "hex",
					        "tip": "选择您喜欢的主题颜色"
					    }
					},
					{
					    key: "backgroundColor",
					    name: "背景颜色",
					    type: "color",
					    value: "rgba(255, 0, 0, 0.8)",
					    isSave: true,
					    extra: {
					        "varType": "rgba",
					        "tip": "选择背景颜色，支持透明度"
					    }
					},
					{
					    key: "birthYearMonth",
					    name: "出生年月",
					    type: "yearmonth",
					    value: "",
					    isSave: true,
					    extra: {
					        "tip": "请选择您的出生年月1"
					    }
					},
					{
					    key: "city",
					    name: "所在城市",
					    type: "select",
					    value: "",
					    isSave: true,
					    extra: {
					        "varType": "string",
					        "placeholder": "请选择城市",
					        "tip": "选择您所在的城市",
					        "options": [
					            {"text": "北京", "value": "beijing"},
					            {"text": "上海", "value": "shanghai"},
					            {"text": "广州", "value": "guangzhou"},
					            {"text": "深圳", "value": "shenzhen"},
					            {"text": "杭州", "value": "hangzhou"}
					        ]
					    }
					},
					{
					    key: "level",
					    name: "用户等级",
					    type: "select",
					    value: 1,
					    isSave: true,
					    extra: {
					        "varType": "int",
					        "placeholder": "请选择等级",
					        "tip": "选择您的用户等级",
					        "options": [
					            {"text": "初级用户", "value": 1},
					            {"text": "中级用户", "value": 2},
					            {"text": "高级用户", "value": 3},
					            {"text": "VIP用户", "value": 4}
					        ]
					    }
					},
					{
					    key: "score",
					    name: "评分",
					    type: "select",
					    value: 4.5,
					    isSave: true,
					    extra: {
					        "varType": "float",
					        "placeholder": "请选择评分",
					        "tip": "选择您的评分",
					        "options": [
					            {"text": "1.0分", "value": 1.0},
					            {"text": "2.5分", "value": 2.5},
					            {"text": "3.0分", "value": 3.0},
					            {"text": "4.5分", "value": 4.5},
					            {"text": "5.0分", "value": 5.0}
					        ]
					    }
					}
				] as FormFieldData[]
			}
		},
		methods: {
			viewForm(){
				// this.formConfig[0].value="111"
				// console.log(this.formConfig)
				uni.navigateTo({
					url:"/pages/calendar-test/calendar-test"
				})
			},
			openFun() {
				(this.$refs['yearmonthPicker'] as ComponentPublicInstance).$callMethod('open')
			
				// uni.showModal({
				// 	title: "onLoad 调用示例,请手动取消",
				// 	editable: true,
				// 	content: "Hello World",
				// 	success: function (res) {
				// 		if (res.confirm) {
				// 			console.log('用户点击确定')
				// 		} else if (res.cancel) {
				// 			console.log('用户点击取消')
				// 		}
				// 	}
				// })
			},
			openColorPicker() {
				// 使用$callMethod方式调用组件方法
				(this.$refs['colorPicker'] as ComponentPublicInstance).$callMethod('open')
			},

			onCancel() {
				console.log('用户取消选择', " at pages/index/index.uvue:272")
			},

			onConfirm(result : UTSJSONObject) {
				console.log(result, " at pages/index/index.uvue:276")
				console.log('选择的颜色:', result['color'], " at pages/index/index.uvue:277")
				console.log('RGBA值:', result['rgba'], " at pages/index/index.uvue:278")
			},

			// 打开日期时间选择器
			openDateTimePicker() {
				(this.$refs['datetimePicker'] as ComponentPublicInstance).$callMethod('show')
			},

			// 日期时间选择器取消事件
			onDateTimeCancel() {
				console.log('用户取消选择日期时间', " at pages/index/index.uvue:288")
			},

			// 日期时间选择器确认事件
			onDateTimeConfirm(result: UTSJSONObject) {
				console.log('选择的日期时间:', result, " at pages/index/index.uvue:293")
				console.log('格式化后的值:', result['formatted'], " at pages/index/index.uvue:294")
				console.log('Date对象:', result['value'], " at pages/index/index.uvue:295")
			}
		}
	})

export default __sfc__
function GenPagesIndexIndexRender(this: InstanceType<typeof __sfc__>): any | null {
const _ctx = this
const _cache = this.$.renderCache
const _component_main_color_picker = resolveEasyComponent("main-color-picker",_easycom_main_color_picker)
const _component_mainYearmonthPicker = resolveComponent("mainYearmonthPicker")
const _component_main_datetime_picker = resolveComponent("main-datetime-picker")
const _component_main_form = resolveEasyComponent("main-form",_easycom_main_form)

  return _cE("scroll-view", _uM({ class: "content" }), [
    _cE("button", _uM({ onClick: _ctx.openColorPicker }), "选择颜色", 8 /* PROPS */, ["onClick"]),
    _cE("button", _uM({ onClick: _ctx.openFun }), "对话框", 8 /* PROPS */, ["onClick"]),
    _cE("button", _uM({ onClick: _ctx.openDateTimePicker }), "测试日期时间选择器", 8 /* PROPS */, ["onClick"]),
    _cV(_component_main_color_picker, _uM({
      ref: "colorPicker",
      onCancel: _ctx.onCancel,
      onConfirm: _ctx.onConfirm
    }), null, 8 /* PROPS */, ["onCancel", "onConfirm"]),
    _cV(_component_mainYearmonthPicker, _uM({ ref: "yearmonthPicker" }), null, 512 /* NEED_PATCH */),
    _cV(_component_main_datetime_picker, _uM({
      ref: "datetimePicker",
      mode: _ctx.dateTimeMode,
      title: _ctx.dateTimeTitle,
      showSeconds: _ctx.showSeconds,
      quickOptions: _ctx.quickOptions,
      onCancel: _ctx.onDateTimeCancel,
      onConfirm: _ctx.onDateTimeConfirm
    }), null, 8 /* PROPS */, ["mode", "title", "showSeconds", "quickOptions", "onCancel", "onConfirm"]),
    _cV(_component_main_form, _uM({
      formData: _ctx.formConfig,
      title: "用户信息表单",
      keyName: "user_form",
      ref: "mainForm"
    }), null, 8 /* PROPS */, ["formData"]),
    _cE("button", _uM({ onClick: _ctx.viewForm }), "查看表单1", 8 /* PROPS */, ["onClick"])
  ])
}
const GenPagesIndexIndexStyles = [_uM([["content", _pS(_uM([["height", "100%"], ["paddingTop", "20rpx"], ["paddingRight", "20rpx"], ["paddingBottom", "20rpx"], ["paddingLeft", "20rpx"]]))]])]

import _easycom_main_color_picker from '@/components/main-color-picker/main-color-picker.uvue'
import _easycom_main_form from '@/components/main-form/main-form.uvue'
