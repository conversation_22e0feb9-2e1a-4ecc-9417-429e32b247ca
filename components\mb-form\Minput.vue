<template>

	<view class="Minput-body">
		<view class="Minput">
			<view class="Minput-name" :style="{color:color}">
				{{data.name}}
			</view>


			<view class="Minput-box qShadow1" :style="{backgroundColor:bgColor,borderColor:showErr?'red':'#fff'}">
				<input v-model="word" :maxlength="data.max" :type="data.inputType" :placeholder="data.tip"
					@input="change" @blur="blurfun" />

				<view class="Minput-unit " v-if="data.unit">{{data.unit}}</view>
			</view>
		</view>

		<view class="Minput-tip" v-if="showErr">
			{{tip}}
		</view>
	</view>
</template>

<script>
	export default {
		name: "Minput",
		props: {
			data: {
				type: Object,
				default: {}
			},
			keyName: {
				type: String,
				default: ""

			},
			index: {
				type: Number,
				default: 0
			},
			color: {
				type: String,
				default: '#000'
			},
			bgColor: {
				type: String,
				default: "#f1f4f9"

			}
		},
		data() {
			return {
				word: "",
				showErr: false,
				tip: "",
				saveKey: ""
			};
		},
		watch: {
			data: {

				handler(newValue, oldValue) {
					this.word = this.data.value

				},
				deep: true
			}

		},
		created() {
			if (this.data.isSave) {

				this.saveKey = this.keyName + "_" + this.data.key

				uni.getStorage({
					key: this.saveKey,
					success: (res) => {
						this.word = res.data
						this.$emit("change", {
							index: this.index,
							value: res.data
						})
					},
					fail: () => {
						this.word = this.data.value
					}
				});


			} else {
				this.word = this.data.value
			}

		},

		methods: {
			blurfun(e) {
				let inputValue = e.detail.value
				let varType = this.data.varType

				if (!this.data.allowNull && inputValue == "") {
					this.showErr = true
					this.tip = "输入内容不能为空"
				} else {
					this.showErr = false
				}


				if (this.data.varType == 'int' || this.data.varType == 'float') {

					if (inputValue != "") {
						let value = Number(inputValue)
						if (Number(value) >= this.data.max) {

							value = this.data.max

						} else if (Number(value) <= this.data.min) {

							value = this.data.min

						}

						this.word = value
					
					}




				}
			},
			change(e) {
				if (e.detail.value == "") {


					this.$emit("change", {
						index: this.index,
						value: ""
					})

					return false
				}

				let value = ""
				if (this.data.varType == 'int' || this.data.varType == 'float') {
					value = Number(e.detail.value)
					if (Number(value) >= this.data.max) {

						value = this.data.max

					} else if (Number(value) <= this.data.min) {

						value = this.data.min

					}



				} else {
					value = e.detail.value

				}




				this.$emit("change", {
					index: this.index,
					value: value
				})
				if (this.data.isSave) {
					try {
						uni.setStorageSync(this.saveKey, value);
					} catch (e) {
						// error
					}

				}

			}
		}
	}
</script>

<style>
	.Minput-body {
		width: 100%;
		display: flex;
		flex-direction: column;
		padding: 0 20rpx;
		margin-bottom: 20rpx;
	}

	.Minput {
		width: 100%;
		display: flex;
		flex-direction: column;



	}

	.Minput-name {
		width: 100%;
		font-size: 32rpx;
		margin-bottom: 10rpx;
	}

	.Minput-box {
		width: 100%;
		height: 100rpx;
		border: 1rpx solid #fff;
		box-sizing: border-box;
		padding: 0 20rpx;
		border-radius: 20rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
	}

	.Minput-box input {
		flex: 1;
		height: 100%;
	}

	.Minput-tip {
		width: 100%;
		text-align: right;
		color: red;
		font-size: 28rpx;
		margin-top: 10rpx;
	}

	.Minput-unit {
		padding: 0 10rpx;
		height: 80rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		background-color: #333;
		color: #fff;
		border-radius: 10rpx;
	}
</style>