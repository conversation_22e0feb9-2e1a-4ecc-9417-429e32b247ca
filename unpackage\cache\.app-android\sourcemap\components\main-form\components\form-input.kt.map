{"version": 3, "sources": ["components/main-form/components/form-input.uvue"], "sourcesContent": ["<template>\r\n\t<form-container :label=\"fieldName\" :show-error=\"showError\" :tip=\"tip\" :error-message=\"errorMessage\" :label-color=\"labelColor\"\r\n\t\t:background-color=\"backgroundColor\">\r\n\t\t<template #input-content>\r\n\t\t\t<input class=\"form-input-element\" v-model=\"fieldValue\" :type=\"inputmode\" :maxlength=\"maxLength\" :placeholder=\"placeholder\" @input=\"onInput\"\r\n\t\t\t\t@blur=\"onBlur\" />\r\n\t\t</template>\r\n\t</form-container>\r\n</template>\r\n\r\n<script lang=\"uts\">\r\n\timport { FormFieldData ,FormChangeEvent} from '@/components/main-form/form_type.uts'\r\n\timport FormContainer from './form-container.uvue'\r\n\r\n\texport default {\r\n\t\tname: \"FormInput\",\r\n\t\tcomponents: {\r\n\t\t\tFormContainer\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\tdata: {\r\n\t\t\t\ttype:Object  as PropType<FormFieldData>\r\n\t\t\t},\r\n\t\t\tindex: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 0\r\n\t\t\t},\r\n\t\t\tkeyName: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"\"\r\n\r\n\t\t\t},\r\n\t\t\tlabelColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"#000\"\r\n\t\t\t},\r\n\t\t\tbackgroundColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"#f1f4f9\"\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tfieldName: \"\",\r\n\t\t\t\tfieldValue: \"\",\r\n\t\t\t\tisSave: false,\r\n\t\t\t\tsave_key: \"\",\r\n\t\t\t\ttip: \"\",\r\n\t\t\t\tplaceholder: \"\",\r\n\t\t\t\tinputmode: \"text\",\r\n\t\t\t\tminLength: 0,\r\n\t\t\t\tmaxLength: -1,\r\n\t\t\t\tshowError: false,\r\n\t\t\t\terrorMessage: \"\"\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tdata: {\r\n\t\t\t\thandler(obj: FormFieldData) {\r\n\t\t\t\t\t// 只处理value的变化，当外部传入的value与当前fieldValue不同时，才更新fieldValue\r\n\t\t\t\t\t// 这避免了用户输入时的循环更新问题\r\n\t\t\t\t\tconst newValue = obj.value as string\r\n\t\t\t\t\tif (newValue !== this.fieldValue) {\r\n\t\t\t\t\t\tthis.fieldValue = newValue\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tdeep: true\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated(): void {\r\n\t\t\t// 初始化时调用一次即可\r\n\t\t\tconst fieldObj = this.$props[\"data\"] as FormFieldData\r\n\t\t\tthis.initFieldData(fieldObj)\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 初始化字段数据（仅在首次加载时调用）\r\n\t\t\tinitFieldData(fieldObj: FormFieldData): void {\r\n\t\t\t\tconst fieldKey = fieldObj.key\r\n\t\t\t\tconst fieldValue = fieldObj.value as string\r\n\r\n\t\t\t\t// 设置基本信息\r\n\t\t\t\tthis.fieldName = fieldObj.name\r\n\t\t\t\tthis.fieldValue = fieldValue\r\n\t\t\t\tthis.isSave = fieldObj.isSave ?? false\r\n\t\t\t\tthis.save_key = this.keyName + \"_\" + fieldKey\r\n\r\n\t\t\t\t// 解析配置信息\r\n\t\t\t\tconst extalJson = fieldObj.extra as UTSJSONObject\r\n\t\t\t\tthis.minLength = extalJson.getNumber(\"minLength\") ?? 0\r\n\t\t\t\tthis.maxLength = extalJson.getNumber(\"maxLength\") ?? -1\r\n\t\t\t\tthis.placeholder = extalJson.getString(\"placeholder\") ?? \"\"\r\n\t\t\t\tthis.inputmode = extalJson.getString(\"inputmode\") ?? \"text\"\r\n\t\t\t\tthis.tip = extalJson.getString(\"tip\") ?? \"\"\r\n\r\n\t\t\t\t// 获取缓存\r\n\t\t\t\tthis.getCache()\r\n\t\t\t},\r\n\r\n\t\t\tgetCache(): void {\r\n\t\t\t\tif (this.isSave) {\r\n\t\t\t\t\tconst that = this\r\n\t\t\t\t\tuni.getStorage({\r\n\t\t\t\t\t\tkey: this.save_key,\r\n\t\t\t\t\t\tsuccess: (res: GetStorageSuccess) => {\r\n\t\t\t\t\t\t\tconst save_value = res.data as string\r\n\t\t\t\t\t\t\tthat.fieldValue = save_value\r\n\t\t\t\t\t\t\tconst result: FormChangeEvent = {\r\n\t\t\t\t\t\t\t\tindex: this.index,\r\n\t\t\t\t\t\t\t\tvalue: save_value\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tthis.change(result)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tsetCache(): void {\r\n\t\t\t\tif (this.isSave) {\r\n\t\t\t\t\tuni.setStorage({\r\n\t\t\t\t\t\tkey: this.save_key,\r\n\t\t\t\t\t\tdata: this.fieldValue\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\tvalidate(): boolean {\r\n\t\t\t\t// 如果没有设置最大长度限制，只检查最小长度\r\n\t\t\t\tif (this.maxLength == -1) {\r\n\t\t\t\t\tif (this.minLength > 0 && this.fieldValue.length < this.minLength) {\r\n\t\t\t\t\t\tthis.showError = true\r\n\t\t\t\t\t\tthis.errorMessage = `输入内容不能少于${this.minLength}个字符`\r\n\t\t\t\t\t\treturn false\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.showError = false\r\n\t\t\t\t\tthis.errorMessage = \"\"\r\n\t\t\t\t\treturn true\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 检查最小长度\r\n\t\t\t\tif (this.fieldValue.length < this.minLength) {\r\n\t\t\t\t\tthis.showError = true\r\n\t\t\t\t\tthis.errorMessage = `输入内容不能少于${this.minLength}个字符`\r\n\t\t\t\t\treturn false\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 检查最大长度\r\n\t\t\t\tif (this.maxLength > 0 && this.fieldValue.length > this.maxLength) {\r\n\t\t\t\t\tthis.showError = true\r\n\t\t\t\t\tthis.errorMessage = `输入内容不能超过${this.maxLength}个字符`\r\n\t\t\t\t\treturn false\r\n\t\t\t\t}\r\n\r\n\t\t\t\tthis.showError = false\r\n\t\t\t\tthis.errorMessage = \"\"\r\n\t\t\t\treturn true\r\n\t\t\t},\r\n\t\t\tchange(event: FormChangeEvent): void {\r\n\t\t\t\t// 更新字段值\r\n\t\t\t\tthis.fieldValue = event.value as string\r\n\t\t\t\t// 保存缓存\r\n\t\t\t\tthis.setCache()\r\n\t\t\t\t// 触发父组件事件\r\n\t\t\t\tthis.$emit('change', event)\r\n\t\t\t},\r\n\r\n\t\t\tonInput(event: UniInputEvent): void {\r\n\t\t\t\tconst result: FormChangeEvent = {\r\n\t\t\t\t\tindex: this.index,\r\n\t\t\t\t\tvalue: event.detail.value\r\n\t\t\t\t}\r\n\t\t\t\tthis.change(result)\r\n\t\t\t},\r\n\r\n\t\t\tonBlur(): void {\r\n\t\t\t\t// 在失去焦点时进行验证\r\n\t\t\t\tthis.validate()\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.form-input-element {\r\n\t\tflex: 1;\r\n\t\tmin-height: 60rpx;\r\n\t}\r\n</style>"], "names": [], "mappings": ";;;;;;;;;;;;;+BAwGS;+BAgBA;AA1GH;;kBA0DJ,OAAW,IAAG,CAAA;YAEb,IAAM,WAAW,IAAI,CAAC,QAAM,CAAC,OAAM,CAAA,EAAA;YACnC,IAAI,CAAC,aAAa,CAAC;QACpB;;;;;UAfE,IAAQ,kBAAkB,EAAA;YAGzB,IAAM,WAAW,IAAI,KAAI,CAAA,EAAA,CAAK,MAAK;YACnC,IAAI,aAAa,IAAI,CAAC,UAAU,EAAE;gBACjC,IAAI,CAAC,UAAS,GAAI;;QAEpB;uBACA,OAAM,IAAG;;;;;;;eApEZ,IAMiB,2BAAA,IANA,WAAO,KAAA,SAAS,EAAG,gBAAY,KAAA,SAAS,EAAG,SAAK,KAAA,GAAG,EAAG,mBAAe,KAAA,YAAY,EAAG,iBAAa,KAAA,UAAU,EAC1H,sBAAkB,KAAA,eAAe,OACvB,mBAAa,YACvB,gBACkB,GAAA;mBAAA;gBADlB,IACkB,SAAA,IADX,WAAM,sCAA8B,KAAA,UAAU;;wBAAV,KAAA,UAAU,GAAA,SAAA,MAAA,CAAA,KAAA;oBAAA;;oBAA8E,KAAA,OAAO;iBAAA,EAAlF,UAAM,KAAA,SAAS,EAAG,eAAW,KAAA,SAAS,EAAG,iBAAa,KAAA,WAAW,EACvH,YAAM,KAAA,MAAM;;;;;;;;;;;;;;;;;;;;;;;;aAsCb;aACA;aACA;aACA;aACA;aACA;aACA;aACA;aACA;aACA;aACA;;;mBAVA,eAAW,IACX,gBAAY,IACZ,YAAQ,KAAK,EACb,cAAU,IACV,SAAK,IACL,iBAAa,IACb,eAAW,QACX,eAAW,CAAC,EACZ,eAAW,CAAC,CAAC,EACb,eAAW,KAAK,EAChB,kBAAc;;aA0Bf;aAAA,qBAAc,uBAAuB,GAAG,IAAG,CAAA;QAC1C,IAAM,WAAW,SAAS,GAAE;QAC5B,IAAM,aAAa,SAAS,KAAI,CAAA,EAAA,CAAK,MAAK;QAG1C,IAAI,CAAC,SAAQ,GAAI,SAAS,IAAG;QAC7B,IAAI,CAAC,UAAS,GAAI;QAClB,IAAI,CAAC,MAAK,GAAI,SAAS,MAAK,IAAK,KAAI;QACrC,IAAI,CAAC,QAAO,GAAI,IAAI,CAAC,OAAM,GAAI,MAAM;QAGrC,IAAM,YAAY,SAAS,KAAI,CAAA,EAAA,CAAK;QACpC,IAAI,CAAC,SAAQ,GAAI,UAAU,SAAS,CAAC,gBAAgB,CAAA;QACrD,IAAI,CAAC,SAAQ,GAAI,UAAU,SAAS,CAAC,gBAAgB,CAAC,CAAA;QACtD,IAAI,CAAC,WAAU,GAAI,UAAU,SAAS,CAAC,kBAAkB;QACzD,IAAI,CAAC,SAAQ,GAAI,UAAU,SAAS,CAAC,gBAAgB;QACrD,IAAI,CAAC,GAAE,GAAI,UAAU,SAAS,CAAC,UAAU;QAGzC,IAAI,CAAC,QAAQ;IACd;aAEA;aAAA,mBAAY,IAAG,CAAA;QACd,IAAI,IAAI,CAAC,MAAM,EAAE;YAChB,IAAM,OAAO,IAAG;YAChB,iCACC,MAAK,IAAI,CAAC,QAAQ,EAClB,UAAS,IAAC,KAAK,kBAAoB;gBAClC,IAAM,aAAa,IAAI,IAAG,CAAA,EAAA,CAAK,MAAK;gBACpC,KAAK,UAAS,GAAI;gBAClB,IAAM,yBACL,QAAO,IAAI,CAAC,KAAK,EACjB,QAAO;gBAER,IAAI,CAAC,MAAM,CAAC;YACb;;;IAGH;aACA;aAAA,mBAAY,IAAG,CAAA;QACd,IAAI,IAAI,CAAC,MAAM,EAAE;YAChB,iCACC,MAAK,IAAI,CAAC,QAAQ,EAClB,OAAM,IAAI,CAAC,UAAS;;IAGvB;aAEA;aAAA,mBAAY,OAAM,CAAA;QAEjB,IAAI,IAAI,CAAC,SAAQ,IAAK,CAAC,CAAC,EAAE;YACzB,IAAI,IAAI,CAAC,SAAQ,GAAI,CAAA,IAAK,IAAI,CAAC,UAAU,CAAC,MAAK,GAAI,IAAI,CAAC,SAAS,EAAE;gBAClE,IAAI,CAAC,SAAQ,GAAI,IAAG;gBACpB,IAAI,CAAC,YAAW,GAAI,qDAAW,IAAI,CAAC,SAAS,GAAA;gBAC7C,OAAO,KAAI;;YAEZ,IAAI,CAAC,SAAQ,GAAI,KAAI;YACrB,IAAI,CAAC,YAAW,GAAI;YACpB,OAAO,IAAG;;QAIX,IAAI,IAAI,CAAC,UAAU,CAAC,MAAK,GAAI,IAAI,CAAC,SAAS,EAAE;YAC5C,IAAI,CAAC,SAAQ,GAAI,IAAG;YACpB,IAAI,CAAC,YAAW,GAAI,qDAAW,IAAI,CAAC,SAAS,GAAA;YAC7C,OAAO,KAAI;;QAIZ,IAAI,IAAI,CAAC,SAAQ,GAAI,CAAA,IAAK,IAAI,CAAC,UAAU,CAAC,MAAK,GAAI,IAAI,CAAC,SAAS,EAAE;YAClE,IAAI,CAAC,SAAQ,GAAI,IAAG;YACpB,IAAI,CAAC,YAAW,GAAI,qDAAW,IAAI,CAAC,SAAS,GAAA;YAC7C,OAAO,KAAI;;QAGZ,IAAI,CAAC,SAAQ,GAAI,KAAI;QACrB,IAAI,CAAC,YAAW,GAAI;QACpB,OAAO,IAAG;IACX;aACA;aAAA,cAAO,sBAAsB,GAAG,IAAG,CAAA;QAElC,IAAI,CAAC,UAAS,GAAI,MAAM,KAAI,CAAA,EAAA,CAAK,MAAK;QAEtC,IAAI,CAAC,QAAQ;QAEb,IAAI,CAAC,OAAK,CAAC,UAAU;IACtB;aAEA;aAAA,eAAQ,OAAO,aAAa,GAAG,IAAG,CAAA;QACjC,IAAM,yBACL,QAAO,IAAI,CAAC,KAAK,EACjB,QAAO,MAAM,MAAM,CAAC,KAAI;QAEzB,IAAI,CAAC,MAAM,CAAC;IACb;aAEA;aAAA,iBAAU,IAAG,CAAA;QAEZ,IAAI,CAAC,QAAQ;IACd;;mBAnKK;;;;;;;;;;;;;+GAUK,CAAA,qDAIA,0DAKA,mEAIA;;;;;;;;;AA8IZ"}