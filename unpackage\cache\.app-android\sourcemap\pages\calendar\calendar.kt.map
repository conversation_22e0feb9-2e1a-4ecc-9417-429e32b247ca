{"version": 3, "sources": ["pages/calendar/calendar.uvue"], "sourcesContent": ["<template>\n  <view class=\"root\">\n    <view class=\"date\">\n      <text class=\"date-text\">{{ current_month }}</text>\n    </view>\n    <view ref=\"draw-header\" class=\"calendar-header\"></view>\n    <view ref=\"draw-weeks\" class=\"calendar-week\" @touchstart=\"select\"></view>\n    <view class=\"btn-group\">\n      <button size=\"mini\" @click=\"preDate\">上个月</button>\n      <button size=\"mini\" @click=\"gotoToday\">回到今天</button>\n      <button size=\"mini\" @click=\"nextDate\">下个月</button>\n    </view>\n    <view>{{ timeData.fullDate }} {{ current_day }}</view>\n  </view>\n</template>\n<script>\nimport { Calendar, DateType } from './index.uts'\n\ntype CoordsType = {\n  x: number;\n  y: number;\n  width: number;\n  height: number;\n  data: DateType\n}\n\nexport default {\n  data () {\n    return {\n      weeks: [] as Array<Array<DateType>>,\n      $coords: [] as Array<CoordsType>,\n      $calendar: new Calendar() as Calendar,\n      timeData: {\n        fullDate: '',\n        year: 0,\n        month: 0,\n        date: 0,\n        day: 0,\n        lunar: '',\n        disabled: false,\n        is_today: false\n      } as DateType,\n      testWidth: 0\n    }\n  },\n  computed: {\n    // 获取月份\n    current_month (): string {\n      const nowDate = this.timeData\n      const month = nowDate.month\n      return month < 10 ? '0' + month : month.toString()\n    },\n    current_day (): string {\n      const time = this.timeData.data\n      if (time == null) {\n        return ''\n      }\n      return time.IMonthCn + time.IDayCn\n    }\n  },\n  created () { },\n  onReady () {\n    const calendar = this.$data['$calendar'] as Calendar\n    this.weeks = calendar.getWeeks()\n    this.timeData = calendar.getDateInfo()\n    // 绘制日历头部\n    this.drawHeader()\n    this.drawWeek(this.weeks, '')\n\n    // 仅自动化测试\n    const header = this.$refs['draw-header'] as UniElement\n    this.testWidth = header.getBoundingClientRect().width;\n  },\n  methods: {\n\n    // 触发整个日历的点击事件，需要计算点击位置\n    select (event: TouchEvent) {\n      const refs = this.$refs['draw-weeks'] as UniElement\n      const rect = refs.getBoundingClientRect();\n      const dom_x = rect.left; // 元素左上角相对于视口的 X 坐标\n      const dom_y = rect.top; // 元素左上角相对于视口的 Y 坐标\n      const touch = event.touches[0];\n      const clientX = touch.clientX; // X 坐标\n      const clientY = touch.clientY; // Y 坐标\n      // 计算点击的相对位置\n      const x = clientX - dom_x\n      const y = clientY - dom_y\n\n      this.clickGrid(x, y)\n    },\n\n    // 点击具体的日历格子\n    clickGrid (x: number, y: number) {\n      // 小格子数组\n      // const gridArray = this.$data.$coords\n      const calendar = this.$data['$calendar'] as Calendar\n      const gridArray = this.$data['$coords'] as Array<CoordsType>\n\n      // 遍历小格子数组，找到最接近点击坐标的小格子\n      for (let i = 0; i < gridArray.length; i++) {\n        const grid = gridArray[i]\n        // 计算小格子理论上的最大值\n        const max_x = grid.x + grid.width\n        const max_y = grid.y + grid.height\n\n        const is_x_limit = grid.x < x && x < max_x\n        const is_y_limit = grid.y < y && y < max_y\n\n        const is_select = is_x_limit && is_y_limit\n\n        if (is_select) {\n          const data = grid.data\n          this.timeData = calendar.getDateInfo(data.fullDate)\n          this.drawWeek(this.weeks, grid.data.fullDate)\n        }\n      }\n    },\n    // 切换上个月\n    preDate () {\n      const fulldate = this.timeData.fullDate\n      const calendar = this.$data['$calendar'] as Calendar\n      let time = calendar.getDate(fulldate, -1, 'month')\n      const newDate = time.year + '-' + time.month + '-1';\n      time = calendar.getDate(newDate)\n\n      this.timeData = calendar.getDateInfo(time.fullDate)\n      this.weeks = calendar.getWeeks(time.fullDate)\n\n      // 判断是否回到当前月份\n      if (this.isCurrentMonth(time.year, time.month)) {\n        this.gotoToday();\n      } else {\n        // 否则正常绘制日历\n        this.drawWeek(this.weeks, time.fullDate)\n      }\n    },\n    // 切换下个他\n    nextDate () {\n      const fulldate = this.timeData.fullDate\n      const calendar = this.$data['$calendar'] as Calendar\n      let time = calendar.getDate(fulldate, 1, 'month')\n\n      const newDate = time.year + '-' + time.month + '-1';\n      time = calendar.getDate(newDate)\n\n      this.timeData = calendar.getDateInfo(time.fullDate)\n      this.weeks = calendar.getWeeks(time.fullDate)\n\n      // 判断是否回到当前月份\n      if (this.isCurrentMonth(time.year, time.month)) {\n        this.gotoToday();\n      } else {\n        // 否则正常绘制日历\n        this.drawWeek(this.weeks, time.fullDate)\n      }\n    },\n    // 回到今天\n    gotoToday () {\n      const calendar = this.$data['$calendar'] as Calendar\n      const time = calendar.getDate()\n      this.timeData = calendar.getDateInfo(time.fullDate)\n      this.weeks = calendar.getWeeks(time.fullDate)\n\n      // 重新绘制日历\n      this.drawWeek(this.weeks, time.fullDate)\n    },\n    // 判断是否为当前月份\n    isCurrentMonth(year: number, month: number): boolean {\n      const today = new Date();\n      return year === today.getFullYear() && month === today.getMonth() + 1;\n    },\n\n    // 绘制日历顶部信息\n    drawHeader () {\n      const refs = this.$refs['draw-header'] as UniElement\n      let ctx = refs.getDrawableContext()\n      if (ctx == null) return\n      const date_header_map = ['一', '二', '三', '四', '五', '六', '日']\n\n      const width = refs.getBoundingClientRect().width\n      const num = date_header_map.length\n      const one_width = width / num\n\n      ctx.font = '12'\n      ctx.textAlign = 'center'\n\n      for (let i = 0; i < num; i++) {\n        let box_left = i * one_width + 2\n        let box_width = one_width - 4\n        let box_height = 26\n\n        // 文本赋值\n        const text = date_header_map[i]\n        let text_left = box_width / 2 + box_left\n        let text_top = box_height / 2 + 6\n\n        ctx.fillText(text, text_left, text_top)\n      }\n      ctx.update()\n    },\n\n    // 绘制日历主体\n    drawWeek (weeks: Array<Array<DateType>>, time: string) {\n      const start_time = Date.now()\n      const refs = this.$refs['draw-weeks'] as UniElement\n      let ctx = refs.getDrawableContext()\n      if (ctx == null) return\n      const dom = refs.getBoundingClientRect()\n      const width = dom.width\n      const height = dom.height\n      let week_len = weeks.length\n      const one_width = width / weeks[0].length\n      const one_height = height / week_len\n\n      if (time !== '') {\n        this.$data['$coords'] = [] as Array<CoordsType>\n        ctx.reset()\n      }\n\n      ctx.textAlign = 'center'\n\n      for (let week = 0; week < week_len; week++) {\n\n        const week_item = weeks[week]\n        for (let day = 0; day < week_item.length; day++) {\n          const day_item = week_item[day]\n          let day_left = day * one_width + 2\n          let day_top = one_height * week + 2\n          let day_width = one_width - 4\n          let day_height = one_height - 4\n\n          // 文本赋值\n          let text = day_item.date.toString()\n          let text_left = day * one_width + (one_width / 2)\n          let text_top = one_height * week + 25\n\n          ctx.font = '16'\n\n          // 日期是否禁用\n          if (day_item.disabled) {\n            ctx.fillStyle = '#ccc'\n          } else {\n            // 是否为今天\n            if (day_item.is_today) {\n              ctx.fillStyle = 'red'\n            } // 是否为选中日期\n            else if (time == day_item.fullDate) {\n              ctx.fillStyle = 'blue';\n            }\n            // 默认颜色\n            else {\n              ctx.fillStyle = '#666';\n            }\n\n            // 第一次渲染获取数据\n            // if (time == '') {\n            // 存储坐标组，用于点击事件\n            const coords: CoordsType = {\n              x: day_left,\n              y: day_top,\n              width: day_width,\n              height: day_height,\n              data: day_item\n            }\n\n            // TODO 兼容安卓data内$开头的属性的赋值问题\n            let gridArr = this.$data['$coords'] as Array<CoordsType>\n            gridArr.push(coords)\n            // }\n          }\n\n          ctx.fillText(text, text_left, text_top)\n\n          text = day_item.lunar\n          let lunar_left = day * one_width + (one_width / 2)\n          let lunar_top = one_height * week + 42\n          ctx.font = '10'\n          ctx.fillText(text, lunar_left, lunar_top)\n        }\n      }\n\n      ctx.update()\n      console.log('diff time', Date.now() - start_time);\n\n    }\n  }\n}\n</script>\n\n<style>\n.root {\n  flex: 1;\n  position: relative;\n  padding: 15px;\n  background-color: #fff;\n}\n\n.calendar-header {\n  height: 30px;\n  margin-bottom: 10px;\n}\n\n.date {\n  margin-bottom: 10px;\n  margin-left: 10px;\n}\n\n.date-text {\n  font-size: 34px;\n  font-weight: bold;\n}\n\n.calendar-week {\n  height: 350px;\n  margin: 2px 0;\n}\n\n.btn-group {\n  display: flex;\n  flex-direction: row;\n  justify-content: space-between;\n  margin: 20px 0;\n}\n</style>\n"], "names": [], "mappings": ";;;;;;;;;;;;;AA0BK;;kBAkCH,MAAM,CAAO;gBACb,MAAM;YACJ,IAAM,WAAW,IAAI,CAAC,OAAK,CAAC,aAAW,CAAA,EAAA;YACvC,IAAI,CAAC,KAAI,GAAI,SAAS,QAAQ;YAC9B,IAAI,CAAC,QAAO,GAAI,SAAS,WAAW;YAEpC,IAAI,CAAC,UAAU;YACf,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE;YAG1B,IAAM,SAAS,IAAI,CAAC,OAAK,CAAC,cAAa,CAAA,EAAA,CAAK;YAC5C,IAAI,CAAC,SAAQ,GAAI,OAAO,qBAAqB,GAAG,KAAK;QACvD;;;;;;;eAvEA,IAYO,QAAA,IAZD,WAAM,SAAM;YAChB,IAEO,QAAA,IAFD,WAAM,SAAM;gBAChB,IAAkD,QAAA,IAA5C,WAAM,cAAW,IAAI,KAAA,aAAa,GAAA,CAAA;;YAE1C,IAAuD,QAAA,IAAjD,SAAI,eAAc,WAAM;YAC9B,IAAyE,QAAA,IAAnE,SAAI,cAAa,WAAM,iBAAiB,kBAAY,KAAA,MAAM;;;YAChE,IAIO,QAAA,IAJD,WAAM,cAAW;gBACrB,IAAiD,UAAA,IAAzC,UAAK,QAAQ,aAAO,KAAA,OAAO,GAAE,OAAG,CAAA,EAAA;oBAAA;iBAAA;gBACxC,IAAoD,UAAA,IAA5C,UAAK,QAAQ,aAAO,KAAA,SAAS,GAAE,QAAI,CAAA,EAAA;oBAAA;iBAAA;gBAC3C,IAAkD,UAAA,IAA1C,UAAK,QAAQ,aAAO,KAAA,QAAQ,GAAE,OAAG,CAAA,EAAA;oBAAA;iBAAA;;YAE3C,IAAsD,QAAA,IAAA,EAAA,IAA7C,KAAA,QAAQ,CAAC,QAAQ,IAAG,MAAC,IAAG,KAAA,WAAW,GAAA,CAAA;;;aAiB1C,OAAa,SAAM;aACnB,WAAe,SAAM;aACrB;aACA;aAUA;4BAKgB,MAAK;0BAKP,MAAK;;;mBAvBnB,WAAO,IAAY,uBACnB,cAAS,IAAY,eACrB,gBAAW,WAAa,EAAA,WACxB,uBACE,WAAU,IACV,OAAM,CAAC,EACP,QAAO,CAAC,EACR,OAAM,CAAC,EACP,MAAK,CAAC,EACN,QAAO,IACP,WAAU,KAAK,EACf,WAAU,KAAI,GAEhB,eAAW,CAAA,8BAKK,MAAK,EAAvB,OAAkB,MAAK,CAAA;YACrB,IAAM,UAAU,IAAI,CAAC,QAAO;YAC5B,IAAM,QAAQ,QAAQ,KAAI;YAC1B,OAAO,IAAA,QAAQ,EAAC;gBAAI,MAAM;;gBAAQ,MAAM,QAAQ,CAAA,EAAA;;QAClD;qCACgB,MAAK,EAArB,OAAgB,MAAK,CAAA;YACnB,IAAM,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAG;YAC9B,IAAI,QAAQ,IAAI,EAAE;gBAChB,OAAO;;YAET,OAAO,KAAK,QAAO,GAAI,KAAK,MAAK;QACnC;;;aAkBA;aAAA,cAAQ,OAAO,UAAU,EAAA;QACvB,IAAM,OAAO,IAAI,CAAC,OAAK,CAAC,aAAY,CAAA,EAAA,CAAK;QACzC,IAAM,OAAO,KAAK,qBAAqB;QACvC,IAAM,QAAQ,KAAK,IAAI;QACvB,IAAM,QAAQ,KAAK,GAAG;QACtB,IAAM,QAAQ,MAAM,OAAO,CAAC,CAAC,CAAC;QAC9B,IAAM,UAAU,MAAM,OAAO;QAC7B,IAAM,UAAU,MAAM,OAAO;QAE7B,IAAM,IAAI,UAAU;QACpB,IAAM,IAAI,UAAU;QAEpB,IAAI,CAAC,SAAS,CAAC,GAAG;IACpB;aAGA;aAAA,iBAAW,GAAG,MAAM,EAAE,GAAG,MAAM,EAAA;QAG7B,IAAM,WAAW,IAAI,CAAC,OAAK,CAAC,aAAW,CAAA,EAAA;QACvC,IAAM,YAAY,IAAI,CAAC,OAAK,CAAC,WAAS,CAAA,EAAA,CAAK,SAAM;YAGjD;YAAK,IAAI,YAAI,CAAC;YAAd,MAAgB,IAAI,UAAU,MAAM;gBAClC,IAAM,OAAO,SAAS,CAAC,EAAC;gBAExB,IAAM,QAAQ,KAAK,CAAA,GAAI,KAAK,KAAI;gBAChC,IAAM,QAAQ,KAAK,CAAA,GAAI,KAAK,MAAK;gBAEjC,IAAM,aAAa,KAAK,CAAA,GAAI,KAAK,IAAI;gBACrC,IAAM,aAAa,KAAK,CAAA,GAAI,KAAK,IAAI;gBAErC,IAAM,YAAY,cAAc;gBAEhC,IAAI,WAAW;oBACb,IAAM,OAAO,KAAK,IAAG;oBACrB,IAAI,CAAC,QAAO,GAAI,SAAS,WAAW,CAAC,KAAK,QAAQ;oBAClD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,IAAI,CAAC,QAAQ;;gBAdV;;;IAiBxC;aAEA;aAAA,iBAAM;QACJ,IAAM,WAAW,IAAI,CAAC,QAAQ,CAAC,QAAO;QACtC,IAAM,WAAW,IAAI,CAAC,OAAK,CAAC,aAAW,CAAA,EAAA;QACvC,IAAI,OAAO,SAAS,OAAO,CAAC,UAAU,CAAC,CAAC,EAAE;QAC1C,IAAM,UAAU,KAAK,IAAG,GAAI,MAAM,KAAK,KAAI,GAAI;QAC/C,OAAO,SAAS,OAAO,CAAC;QAExB,IAAI,CAAC,QAAO,GAAI,SAAS,WAAW,CAAC,KAAK,QAAQ;QAClD,IAAI,CAAC,KAAI,GAAI,SAAS,QAAQ,CAAC,KAAK,QAAQ;QAG5C,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,IAAI,EAAE,KAAK,KAAK,GAAG;YAC9C,IAAI,CAAC,SAAS;eACT;YAEL,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,QAAQ;;IAE3C;aAEA;aAAA,kBAAO;QACL,IAAM,WAAW,IAAI,CAAC,QAAQ,CAAC,QAAO;QACtC,IAAM,WAAW,IAAI,CAAC,OAAK,CAAC,aAAW,CAAA,EAAA;QACvC,IAAI,OAAO,SAAS,OAAO,CAAC,UAAU,CAAC,EAAE;QAEzC,IAAM,UAAU,KAAK,IAAG,GAAI,MAAM,KAAK,KAAI,GAAI;QAC/C,OAAO,SAAS,OAAO,CAAC;QAExB,IAAI,CAAC,QAAO,GAAI,SAAS,WAAW,CAAC,KAAK,QAAQ;QAClD,IAAI,CAAC,KAAI,GAAI,SAAS,QAAQ,CAAC,KAAK,QAAQ;QAG5C,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,IAAI,EAAE,KAAK,KAAK,GAAG;YAC9C,IAAI,CAAC,SAAS;eACT;YAEL,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,QAAQ;;IAE3C;aAEA;aAAA,mBAAQ;QACN,IAAM,WAAW,IAAI,CAAC,OAAK,CAAC,aAAW,CAAA,EAAA;QACvC,IAAM,OAAO,SAAS,OAAO;QAC7B,IAAI,CAAC,QAAO,GAAI,SAAS,WAAW,CAAC,KAAK,QAAQ;QAClD,IAAI,CAAC,KAAI,GAAI,SAAS,QAAQ,CAAC,KAAK,QAAQ;QAG5C,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,QAAQ;IACzC;aAEA;aAAA,sBAAe,MAAM,MAAM,EAAE,OAAO,MAAM,GAAG,OAAM,CAAA;QACjD,IAAM,QAAQ,AAAI;QAClB,OAAO,SAAS,MAAM,WAAW,MAAM,UAAU,MAAM,QAAQ,KAAK,CAAC;IACvE;aAGA;aAAA,oBAAS;QACP,IAAM,OAAO,IAAI,CAAC,OAAK,CAAC,cAAa,CAAA,EAAA,CAAK;QAC1C,IAAI,MAAM,KAAK,kBAAkB;QACjC,IAAI,OAAO,IAAI;YAAE;;QACjB,IAAM,kBAAkB;YAAC;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;SAAG;QAE1D,IAAM,QAAQ,KAAK,qBAAqB,GAAG,KAAI;QAC/C,IAAM,MAAM,gBAAgB,MAAK;QACjC,IAAM,YAAY,QAAQ;QAE1B,IAAI,IAAG,GAAI;QACX,IAAI,SAAQ,GAAI;YAEhB;YAAK,IAAI,YAAI,CAAC;YAAd,MAAgB,IAAI;gBAClB,IAAI,WAAW,IAAI,YAAY,CAAA;gBAC/B,IAAI,YAAY,YAAY,CAAA;gBAC5B,IAAI,qBAAa,EAAC;gBAGlB,IAAM,OAAO,eAAe,CAAC,EAAC;gBAC9B,IAAI,YAAY,YAAY,CAAA,GAAI;gBAChC,IAAI,WAAW,aAAa,CAAA,GAAI,CAAA;gBAEhC,IAAI,QAAQ,CAAC,MAAM,WAAW;gBAVP;;;QAYzB,IAAI,MAAM;IACZ;aAGA;aAAA,gBAAU,OAAO,SAAM,mBAAgB,EAAE,MAAM,MAAM,EAAA;QACnD,IAAM,aAAa,KAAK,GAAG;QAC3B,IAAM,OAAO,IAAI,CAAC,OAAK,CAAC,aAAY,CAAA,EAAA,CAAK;QACzC,IAAI,MAAM,KAAK,kBAAkB;QACjC,IAAI,OAAO,IAAI;YAAE;;QACjB,IAAM,MAAM,KAAK,qBAAqB;QACtC,IAAM,QAAQ,IAAI,KAAI;QACtB,IAAM,SAAS,IAAI,MAAK;QACxB,IAAI,WAAW,MAAM,MAAK;QAC1B,IAAM,YAAY,QAAQ,KAAK,CAAC,CAAC,CAAC,CAAC,MAAK;QACxC,IAAM,aAAa,SAAS;QAE5B,IAAI,SAAS,IAAI;YACf,IAAI,CAAC,OAAK,CAAC,WAAS,GAAI,IAAY;YACpC,IAAI,KAAK;;QAGX,IAAI,SAAQ,GAAI;YAEhB;YAAK,IAAI,eAAO,CAAC;YAAjB,MAAmB,OAAO;gBAExB,IAAM,YAAY,KAAK,CAAC,KAAI;oBAC5B;oBAAK,IAAI,cAAM,CAAC;oBAAhB,MAAkB,MAAM,UAAU,MAAM;wBACtC,IAAM,WAAW,SAAS,CAAC,IAAG;wBAC9B,IAAI,WAAW,MAAM,YAAY,CAAA;wBACjC,IAAI,UAAU,aAAa,OAAO,CAAA;wBAClC,IAAI,YAAY,YAAY,CAAA;wBAC5B,IAAI,aAAa,aAAa,CAAA;wBAG9B,IAAI,OAAO,SAAS,IAAI,CAAC,QAAQ,CAAA,EAAA;wBACjC,IAAI,YAAY,MAAM,YAAY,CAAC,YAAY,CAAC;wBAChD,IAAI,WAAW,aAAa,OAAO,EAAC;wBAEpC,IAAI,IAAG,GAAI;wBAGX,IAAI,SAAS,QAAQ,EAAE;4BACrB,IAAI,SAAQ,GAAI;+BACX;4BAEL,IAAI,SAAS,QAAQ,EAAE;gCACrB,IAAI,SAAQ,GAAI;mCAEb,IAAI,QAAQ,SAAS,QAAQ,EAAE;gCAClC,IAAI,SAAQ,GAAI;mCAGb;gCACH,IAAI,SAAQ,GAAI;;4BAMlB,IAAM,oBACJ,IAAG,UACH,IAAG,SACH,QAAO,WACP,SAAQ,YACR,OAAM;4BAIR,IAAI,UAAU,IAAI,CAAC,OAAK,CAAC,WAAS,CAAA,EAAA,CAAK,SAAM;4BAC7C,QAAQ,IAAI,CAAC;;wBAIf,IAAI,QAAQ,CAAC,MAAM,WAAW;wBAE9B,OAAO,SAAS,KAAI;wBACpB,IAAI,aAAa,MAAM,YAAY,CAAC,YAAY,CAAC;wBACjD,IAAI,YAAY,aAAa,OAAO,EAAC;wBACrC,IAAI,IAAG,GAAI;wBACX,IAAI,QAAQ,CAAC,MAAM,YAAY;wBArDS;;;gBAHR;;;QA4DpC,IAAI,MAAM;QACV,QAAQ,GAAG,CAAC,aAAa,KAAK,GAAG,KAAK,YAAW;IAEnD;;;;;;;;;;;;;;;;;;;;AAEJ"}