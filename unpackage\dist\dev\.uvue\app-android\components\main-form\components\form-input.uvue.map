{"version": 3, "sources": ["components/main-form/components/form-input.uvue"], "names": [], "mappings": "AAWC,OAAO,EAAE,aAAY,EAAG,eAAe,EAAA,MAAO,sCAAqC,CAAA;AACnF,OAAO,aAAY,MAAO,uBAAsB,CAAA;AAEhD,MAAK,OAAQ,GAAE,eAAA,CAAA;IACd,IAAI,EAAE,WAAW;IACjB,UAAU,EAAE;QACX,aAAY;KACZ;IACD,KAAK,EAAE;QACN,IAAI,EAAE;YACL,IAAI,EAAC,MAAK,IAAM,QAAQ,CAAC,aAAa,CAAA;SACtC;QACD,KAAK,EAAE;YACN,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,CAAA;SACT;QACD,OAAO,EAAE;YACR,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,EAAC;SAEV;QACD,UAAU,EAAE;YACX,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,MAAK;SACd;QACD,eAAe,EAAE;YAChB,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,SAAQ;SAClB;KACA;IACD,IAAI;QACH,OAAO;YACN,SAAS,EAAE,EAAE;YACb,UAAU,EAAE,EAAE;YACd,MAAM,EAAE,KAAK;YACb,QAAQ,EAAE,EAAE;YACZ,GAAG,EAAE,EAAE;YACP,WAAW,EAAE,EAAE;YACf,SAAS,EAAE,MAAM;YACjB,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE,CAAC,CAAC;YACb,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,EAAC;SAChB,CAAA;IACD,CAAC;IACD,QAAQ,EAAE,EAET;IACD,KAAK,EAAE;QACN,IAAI,EAAE;YACL,OAAO,CAAC,GAAG,EAAE,aAAa;gBACzB,wDAAuD;gBACvD,mBAAkB;gBAClB,MAAM,QAAO,GAAI,GAAG,CAAC,KAAI,IAAK,MAAK,CAAA;gBACnC,IAAI,QAAO,KAAM,IAAI,CAAC,UAAU,EAAE;oBACjC,IAAI,CAAC,UAAS,GAAI,QAAO,CAAA;iBAC1B;YACD,CAAC;YACD,IAAI,EAAE,IAAG;SACV;KACA;IACD,OAAO,IAAI,IAAG;QACb,aAAY;QACZ,MAAM,QAAO,GAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAA,IAAK,aAAY,CAAA;QACpD,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAA,CAAA;IAC5B,CAAC;IACD,OAAO,EAAE;QACR,qBAAoB;QACpB,aAAa,CAAC,QAAQ,EAAE,aAAa,GAAG,IAAG;YAC1C,MAAM,QAAO,GAAI,QAAQ,CAAC,GAAE,CAAA;YAC5B,MAAM,UAAS,GAAI,QAAQ,CAAC,KAAI,IAAK,MAAK,CAAA;YAE1C,SAAQ;YACR,IAAI,CAAC,SAAQ,GAAI,QAAQ,CAAC,IAAG,CAAA;YAC7B,IAAI,CAAC,UAAS,GAAI,UAAS,CAAA;YAC3B,IAAI,CAAC,MAAK,GAAI,QAAQ,CAAC,MAAK,IAAK,KAAI,CAAA;YACrC,IAAI,CAAC,QAAO,GAAI,IAAI,CAAC,OAAM,GAAI,GAAE,GAAI,QAAO,CAAA;YAE5C,SAAQ;YACR,MAAM,SAAQ,GAAI,QAAQ,CAAC,KAAI,IAAK,aAAY,CAAA;YAChD,IAAI,CAAC,SAAQ,GAAI,SAAS,CAAC,SAAS,CAAC,WAAW,CAAA,IAAK,CAAA,CAAA;YACrD,IAAI,CAAC,SAAQ,GAAI,SAAS,CAAC,SAAS,CAAC,WAAW,CAAA,IAAK,CAAC,CAAA,CAAA;YACtD,IAAI,CAAC,WAAU,GAAI,SAAS,CAAC,SAAS,CAAC,aAAa,CAAA,IAAK,EAAC,CAAA;YAC1D,IAAI,CAAC,SAAQ,GAAI,SAAS,CAAC,SAAS,CAAC,WAAW,CAAA,IAAK,MAAK,CAAA;YAC1D,IAAI,CAAC,GAAE,GAAI,SAAS,CAAC,SAAS,CAAC,KAAK,CAAA,IAAK,EAAC,CAAA;YAE1C,OAAM;YACN,IAAI,CAAC,QAAQ,EAAC,CAAA;QACf,CAAC;QAED,QAAQ,IAAI,IAAG;YACd,IAAI,IAAI,CAAC,MAAM,EAAE;gBAChB,MAAM,IAAG,GAAI,IAAG,CAAA;gBAChB,GAAG,CAAC,UAAU,CAAC;oBACd,GAAG,EAAE,IAAI,CAAC,QAAQ;oBAClB,OAAO,EAAE,CAAC,GAAG,EAAE,iBAAiB,EAAE,EAAC;wBAClC,MAAM,UAAS,GAAI,GAAG,CAAC,IAAG,IAAK,MAAK,CAAA;wBACpC,IAAI,CAAC,UAAS,GAAI,UAAS,CAAA;wBAC3B,MAAM,MAAM,EAAE,eAAc,GAAI;4BAC/B,KAAK,EAAE,IAAI,CAAC,KAAK;4BACjB,KAAK,EAAE,UAAS;yBACjB,CAAA;wBACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAA,CAAA;oBACnB,CAAA;iBACA,CAAA,CAAA;aACF;QACD,CAAC;QACD,QAAQ,IAAI,IAAG;YACd,IAAI,IAAI,CAAC,MAAM,EAAE;gBAChB,GAAG,CAAC,UAAU,CAAC;oBACd,GAAG,EAAE,IAAI,CAAC,QAAQ;oBAClB,IAAI,EAAE,IAAI,CAAC,UAAS;iBACpB,CAAA,CAAA;aACF;QACD,CAAC;QAED,QAAQ,IAAI,OAAM;YACjB,uBAAsB;YACtB,IAAI,IAAI,CAAC,SAAQ,IAAK,CAAC,CAAC,EAAE;gBACzB,IAAI,IAAI,CAAC,SAAQ,GAAI,CAAA,IAAK,IAAI,CAAC,UAAU,CAAC,MAAK,GAAI,IAAI,CAAC,SAAS,EAAE;oBAClE,IAAI,CAAC,SAAQ,GAAI,IAAG,CAAA;oBACpB,IAAI,CAAC,YAAW,GAAI,WAAW,IAAI,CAAC,SAAS,KAAI,CAAA;oBACjD,OAAO,KAAI,CAAA;iBACZ;gBACA,IAAI,CAAC,SAAQ,GAAI,KAAI,CAAA;gBACrB,IAAI,CAAC,YAAW,GAAI,EAAC,CAAA;gBACrB,OAAO,IAAG,CAAA;aACX;YAEA,SAAQ;YACR,IAAI,IAAI,CAAC,UAAU,CAAC,MAAK,GAAI,IAAI,CAAC,SAAS,EAAE;gBAC5C,IAAI,CAAC,SAAQ,GAAI,IAAG,CAAA;gBACpB,IAAI,CAAC,YAAW,GAAI,WAAW,IAAI,CAAC,SAAS,KAAI,CAAA;gBACjD,OAAO,KAAI,CAAA;aACZ;YAEA,SAAQ;YACR,IAAI,IAAI,CAAC,SAAQ,GAAI,CAAA,IAAK,IAAI,CAAC,UAAU,CAAC,MAAK,GAAI,IAAI,CAAC,SAAS,EAAE;gBAClE,IAAI,CAAC,SAAQ,GAAI,IAAG,CAAA;gBACpB,IAAI,CAAC,YAAW,GAAI,WAAW,IAAI,CAAC,SAAS,KAAI,CAAA;gBACjD,OAAO,KAAI,CAAA;aACZ;YAEA,IAAI,CAAC,SAAQ,GAAI,KAAI,CAAA;YACrB,IAAI,CAAC,YAAW,GAAI,EAAC,CAAA;YACrB,OAAO,IAAG,CAAA;QACX,CAAC;QACD,MAAM,CAAC,KAAK,EAAE,eAAe,GAAG,IAAG;YAClC,QAAO;YACP,IAAI,CAAC,UAAS,GAAI,KAAK,CAAC,KAAI,IAAK,MAAK,CAAA;YACtC,OAAM;YACN,IAAI,CAAC,QAAQ,EAAC,CAAA;YACd,UAAS;YACT,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAA,CAAA;QAC3B,CAAC;QAED,OAAO,CAAC,KAAK,EAAE,aAAa,GAAG,IAAG;YACjC,MAAM,MAAM,EAAE,eAAc,GAAI;gBAC/B,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,KAAI;aACzB,CAAA;YACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAA,CAAA;QACnB,CAAC;QAED,MAAM,IAAI,IAAG;YACZ,aAAY;YACZ,IAAI,CAAC,QAAQ,EAAC,CAAA;QACf,CAAA;KACD;CACD,CAAA,CAAA;;;;;;WAnLA,GAAA,CAMiB,yBAAA,EAAA,GAAA,CAAA;QANA,KAAK,EAAE,IAAA,CAAA,SAAS;QAAG,YAAU,EAAE,IAAA,CAAA,SAAS;QAAG,GAAG,EAAE,IAAA,CAAA,GAAG;QAAG,eAAa,EAAE,IAAA,CAAA,YAAY;QAAG,aAAW,EAAE,IAAA,CAAA,UAAU;QAC1H,kBAAgB,EAAE,IAAA,CAAA,eAAe;;QACvB,eAAa,EAAA,WAAA,CACvB,IACkB,GAAA,EAAA,CAAA,EAAA,CAAA;YADlB,GAAA,CACkB,OAAA,EAAA,GAAA,CAAA;gBADX,KAAK,EAAC,oBAAoB;4BAAU,IAAA,CAAA,UAAU;wDAAV,IAAA,CAAA,UAAU,CAAA,GAAA,MAAA,CAAA,MAAA,CAAA,KAAA,CAAA,CAAA,CAAA,EAA8E,IAAA,CAAA,OAAO,CAAA;gBAAlF,IAAI,EAAE,IAAA,CAAA,SAAS;gBAAG,SAAS,EAAE,IAAA,CAAA,SAAS;gBAAG,WAAW,EAAE,IAAA,CAAA,WAAW;gBACvH,MAAI,EAAE,IAAA,CAAA,MAAM", "file": "components/main-form/components/form-input.uvue", "sourcesContent": ["<template>\r\n\t<form-container :label=\"fieldName\" :show-error=\"showError\" :tip=\"tip\" :error-message=\"errorMessage\" :label-color=\"labelColor\"\r\n\t\t:background-color=\"backgroundColor\">\r\n\t\t<template #input-content>\r\n\t\t\t<input class=\"form-input-element\" v-model=\"fieldValue\" :type=\"inputmode\" :maxlength=\"maxLength\" :placeholder=\"placeholder\" @input=\"onInput\"\r\n\t\t\t\t@blur=\"onBlur\" />\r\n\t\t</template>\r\n\t</form-container>\r\n</template>\r\n\r\n<script lang=\"uts\">\r\n\timport { FormFieldData ,FormChangeEvent} from '@/components/main-form/form_type.uts'\r\n\timport FormContainer from './form-container.uvue'\r\n\r\n\texport default {\r\n\t\tname: \"FormInput\",\r\n\t\tcomponents: {\r\n\t\t\tFormContainer\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\tdata: {\r\n\t\t\t\ttype:Object  as PropType<FormFieldData>\r\n\t\t\t},\r\n\t\t\tindex: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 0\r\n\t\t\t},\r\n\t\t\tkeyName: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"\"\r\n\r\n\t\t\t},\r\n\t\t\tlabelColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"#000\"\r\n\t\t\t},\r\n\t\t\tbackgroundColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"#f1f4f9\"\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tfieldName: \"\",\r\n\t\t\t\tfieldValue: \"\",\r\n\t\t\t\tisSave: false,\r\n\t\t\t\tsave_key: \"\",\r\n\t\t\t\ttip: \"\",\r\n\t\t\t\tplaceholder: \"\",\r\n\t\t\t\tinputmode: \"text\",\r\n\t\t\t\tminLength: 0,\r\n\t\t\t\tmaxLength: -1,\r\n\t\t\t\tshowError: false,\r\n\t\t\t\terrorMessage: \"\"\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tdata: {\r\n\t\t\t\thandler(obj: FormFieldData) {\r\n\t\t\t\t\t// 只处理value的变化，当外部传入的value与当前fieldValue不同时，才更新fieldValue\r\n\t\t\t\t\t// 这避免了用户输入时的循环更新问题\r\n\t\t\t\t\tconst newValue = obj.value as string\r\n\t\t\t\t\tif (newValue !== this.fieldValue) {\r\n\t\t\t\t\t\tthis.fieldValue = newValue\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tdeep: true\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated(): void {\r\n\t\t\t// 初始化时调用一次即可\r\n\t\t\tconst fieldObj = this.$props[\"data\"] as FormFieldData\r\n\t\t\tthis.initFieldData(fieldObj)\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 初始化字段数据（仅在首次加载时调用）\r\n\t\t\tinitFieldData(fieldObj: FormFieldData): void {\r\n\t\t\t\tconst fieldKey = fieldObj.key\r\n\t\t\t\tconst fieldValue = fieldObj.value as string\r\n\r\n\t\t\t\t// 设置基本信息\r\n\t\t\t\tthis.fieldName = fieldObj.name\r\n\t\t\t\tthis.fieldValue = fieldValue\r\n\t\t\t\tthis.isSave = fieldObj.isSave ?? false\r\n\t\t\t\tthis.save_key = this.keyName + \"_\" + fieldKey\r\n\r\n\t\t\t\t// 解析配置信息\r\n\t\t\t\tconst extalJson = fieldObj.extra as UTSJSONObject\r\n\t\t\t\tthis.minLength = extalJson.getNumber(\"minLength\") ?? 0\r\n\t\t\t\tthis.maxLength = extalJson.getNumber(\"maxLength\") ?? -1\r\n\t\t\t\tthis.placeholder = extalJson.getString(\"placeholder\") ?? \"\"\r\n\t\t\t\tthis.inputmode = extalJson.getString(\"inputmode\") ?? \"text\"\r\n\t\t\t\tthis.tip = extalJson.getString(\"tip\") ?? \"\"\r\n\r\n\t\t\t\t// 获取缓存\r\n\t\t\t\tthis.getCache()\r\n\t\t\t},\r\n\r\n\t\t\tgetCache(): void {\r\n\t\t\t\tif (this.isSave) {\r\n\t\t\t\t\tconst that = this\r\n\t\t\t\t\tuni.getStorage({\r\n\t\t\t\t\t\tkey: this.save_key,\r\n\t\t\t\t\t\tsuccess: (res: GetStorageSuccess) => {\r\n\t\t\t\t\t\t\tconst save_value = res.data as string\r\n\t\t\t\t\t\t\tthat.fieldValue = save_value\r\n\t\t\t\t\t\t\tconst result: FormChangeEvent = {\r\n\t\t\t\t\t\t\t\tindex: this.index,\r\n\t\t\t\t\t\t\t\tvalue: save_value\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tthis.change(result)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tsetCache(): void {\r\n\t\t\t\tif (this.isSave) {\r\n\t\t\t\t\tuni.setStorage({\r\n\t\t\t\t\t\tkey: this.save_key,\r\n\t\t\t\t\t\tdata: this.fieldValue\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\tvalidate(): boolean {\r\n\t\t\t\t// 如果没有设置最大长度限制，只检查最小长度\r\n\t\t\t\tif (this.maxLength == -1) {\r\n\t\t\t\t\tif (this.minLength > 0 && this.fieldValue.length < this.minLength) {\r\n\t\t\t\t\t\tthis.showError = true\r\n\t\t\t\t\t\tthis.errorMessage = `输入内容不能少于${this.minLength}个字符`\r\n\t\t\t\t\t\treturn false\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.showError = false\r\n\t\t\t\t\tthis.errorMessage = \"\"\r\n\t\t\t\t\treturn true\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 检查最小长度\r\n\t\t\t\tif (this.fieldValue.length < this.minLength) {\r\n\t\t\t\t\tthis.showError = true\r\n\t\t\t\t\tthis.errorMessage = `输入内容不能少于${this.minLength}个字符`\r\n\t\t\t\t\treturn false\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 检查最大长度\r\n\t\t\t\tif (this.maxLength > 0 && this.fieldValue.length > this.maxLength) {\r\n\t\t\t\t\tthis.showError = true\r\n\t\t\t\t\tthis.errorMessage = `输入内容不能超过${this.maxLength}个字符`\r\n\t\t\t\t\treturn false\r\n\t\t\t\t}\r\n\r\n\t\t\t\tthis.showError = false\r\n\t\t\t\tthis.errorMessage = \"\"\r\n\t\t\t\treturn true\r\n\t\t\t},\r\n\t\t\tchange(event: FormChangeEvent): void {\r\n\t\t\t\t// 更新字段值\r\n\t\t\t\tthis.fieldValue = event.value as string\r\n\t\t\t\t// 保存缓存\r\n\t\t\t\tthis.setCache()\r\n\t\t\t\t// 触发父组件事件\r\n\t\t\t\tthis.$emit('change', event)\r\n\t\t\t},\r\n\r\n\t\t\tonInput(event: UniInputEvent): void {\r\n\t\t\t\tconst result: FormChangeEvent = {\r\n\t\t\t\t\tindex: this.index,\r\n\t\t\t\t\tvalue: event.detail.value\r\n\t\t\t\t}\r\n\t\t\t\tthis.change(result)\r\n\t\t\t},\r\n\r\n\t\t\tonBlur(): void {\r\n\t\t\t\t// 在失去焦点时进行验证\r\n\t\t\t\tthis.validate()\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.form-input-element {\r\n\t\tflex: 1;\r\n\t\tmin-height: 60rpx;\r\n\t}\r\n</style>"]}