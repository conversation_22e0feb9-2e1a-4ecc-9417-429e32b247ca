@file:Suppress("UNCHECKED_CAST", "USELESS_CAST", "INAPPLICABLE_JVM_NAME", "UNUSED_ANONYMOUS_PARAMETER", "NAME_SHADOWING", "UNNECESSARY_NOT_NULL_ASSERTION")
package uni.UNIC178CB1
import io.dcloud.uniapp.*
import io.dcloud.uniapp.extapi.*
import io.dcloud.uniapp.framework.*
import io.dcloud.uniapp.runtime.*
import io.dcloud.uniapp.vue.*
import io.dcloud.uniapp.vue.shared.*
import io.dcloud.unicloud.*
import io.dcloud.uts.*
import io.dcloud.uts.Map
import io.dcloud.uts.Set
import io.dcloud.uts.UTSAndroid
import io.dcloud.uniapp.extapi.getStorage as uni_getStorage
import io.dcloud.uniapp.extapi.setStorage as uni_setStorage
open class GenComponentsMainFormComponentsFormSlider : VueComponent {
    constructor(__ins: ComponentInternalInstance) : super(__ins) {
        onCreated(fun(): Unit {
            val fieldObj = this.`$props`["data"] as FormFieldData
            this.initFieldData(fieldObj)
        }
        , __ins)
        this.`$watch`(fun(): Any? {
            return this.data
        }
        , fun(obj: FormFieldData) {
            val newValue = obj.value as Number
            if (newValue !== this.fieldValue) {
                this.fieldValue = newValue
                this.updateSliderAndInput()
            }
        }
        , WatchOptions(deep = true))
    }
    @Suppress("UNUSED_PARAMETER", "UNUSED_VARIABLE")
    override fun `$render`(): Any? {
        val _ctx = this
        val _cache = this.`$`.renderCache
        val _component_slider = resolveComponent("slider")
        val _component_form_container = resolveComponent("form-container")
        return _cV(_component_form_container, _uM("label" to _ctx.fieldName, "show-error" to _ctx.showError, "tip" to _ctx.tip, "error-message" to _ctx.errorMessage, "label-color" to _ctx.labelColor, "background-color" to _ctx.backgroundColor), _uM("input-content" to withSlotCtx(fun(): UTSArray<Any> {
            return _uA(
                _cE("view", _uM("class" to "slider-container"), _uA(
                    _cE("view", _uM("class" to "slider-wrapper"), _uA(
                        _cV(_component_slider, _uM("value" to _ctx.sliderValue, "min" to _ctx.minValue, "max" to _ctx.maxValue, "step" to _ctx.stepValue, "onChange" to _ctx.onSliderChange, "show-value" to false, "activeColor" to _ctx.activeColor, "backgroundColor" to _ctx.sliderBackgroundColor, "block-color" to _ctx.blockColor, "block-size" to _ctx.blockSize), null, 8, _uA(
                            "value",
                            "min",
                            "max",
                            "step",
                            "onChange",
                            "activeColor",
                            "backgroundColor",
                            "block-color",
                            "block-size"
                        ))
                    )),
                    _cE("view", _uM("class" to "input-wrapper"), _uA(
                        _cE("input", _uM("class" to "slider-input", "type" to "number", "modelValue" to _ctx.inputValue, "onInput" to _uA(
                            fun(`$event`: UniInputEvent){
                                _ctx.inputValue = `$event`.detail.value
                            }
                            ,
                            _ctx.onInputChange
                        ), "onBlur" to _ctx.onInputBlur), null, 40, _uA(
                            "modelValue",
                            "onInput",
                            "onBlur"
                        ))
                    ))
                ))
            )
        }
        ), "_" to 1), 8, _uA(
            "label",
            "show-error",
            "tip",
            "error-message",
            "label-color",
            "background-color"
        ))
    }
    open var data: Any? by `$props`
    open var index: Number by `$props`
    open var keyName: String by `$props`
    open var labelColor: String by `$props`
    open var backgroundColor: String by `$props`
    open var fieldName: String by `$data`
    open var fieldValue: Number by `$data`
    open var isSave: Boolean by `$data`
    open var save_key: String by `$data`
    open var tip: String by `$data`
    open var minValue: Number by `$data`
    open var maxValue: Number by `$data`
    open var stepValue: Number by `$data`
    open var sliderValue: Number by `$data`
    open var inputValue: String by `$data`
    open var activeColor: String by `$data`
    open var sliderBackgroundColor: String by `$data`
    open var blockColor: String by `$data`
    open var blockSize: Number by `$data`
    open var showError: Boolean by `$data`
    open var errorMessage: String by `$data`
    @Suppress("USELESS_CAST")
    override fun data(): Map<String, Any?> {
        return _uM("fieldName" to "", "fieldValue" to 0, "isSave" to false, "save_key" to "", "tip" to "", "minValue" to 0, "maxValue" to 100, "stepValue" to 1, "sliderValue" to 0, "inputValue" to "0", "activeColor" to "#3399FF", "sliderBackgroundColor" to "#000000", "blockColor" to "#8A6DE9", "blockSize" to 20, "showError" to false, "errorMessage" to "")
    }
    open var initFieldData = ::gen_initFieldData_fn
    open fun gen_initFieldData_fn(fieldObj: FormFieldData): Unit {
        val fieldKey = fieldObj.key
        val fieldValue = fieldObj.value as Number
        this.fieldName = fieldObj.name
        this.fieldValue = fieldValue
        this.isSave = fieldObj.isSave ?: false
        this.save_key = this.keyName + "_" + fieldKey
        val extalJson = fieldObj.extra as UTSJSONObject
        this.minValue = extalJson.getNumber("min") ?: 0
        this.maxValue = extalJson.getNumber("max") ?: 100
        this.stepValue = extalJson.getNumber("step") ?: 1
        this.tip = extalJson.getString("tip") ?: ""
        this.activeColor = extalJson.getString("activeColor") ?: "#3399FF"
        this.sliderBackgroundColor = extalJson.getString("sliderBackgroundColor") ?: "#000000"
        this.blockColor = extalJson.getString("blockColor") ?: "#8A6DE9"
        this.blockSize = extalJson.getNumber("blockSize") ?: 20
        this.updateSliderAndInput()
        setTimeout(fun(){
            this.getCache()
        }
        , 500)
    }
    open var updateSliderAndInput = ::gen_updateSliderAndInput_fn
    open fun gen_updateSliderAndInput_fn(): Unit {
        this.sliderValue = this.fieldValue
        this.inputValue = this.fieldValue.toString(10)
    }
    open var validateValue = ::gen_validateValue_fn
    open fun gen_validateValue_fn(value: Number): Number {
        if (value < this.minValue) {
            return this.minValue
        }
        if (value > this.maxValue) {
            return this.maxValue
        }
        if (this.stepValue % 1 == 0) {
            return Math.round(value)
        } else {
            return UTSNumber.from(value.toFixed(1))
        }
    }
    open var getCache = ::gen_getCache_fn
    open fun gen_getCache_fn(): Unit {
        if (this.isSave) {
            val that = this
            uni_getStorage(GetStorageOptions(key = this.save_key, success = fun(res: GetStorageSuccess){
                val save_value = res.data as Number
                val validatedValue = that.validateValue(save_value)
                that.fieldValue = validatedValue
                that.updateSliderAndInput()
                val result = FormChangeEvent(index = this.index, value = validatedValue)
                this.change(result)
            }
            ))
        }
    }
    open var setCache = ::gen_setCache_fn
    open fun gen_setCache_fn(): Unit {
        if (this.isSave) {
            uni_setStorage(SetStorageOptions(key = this.save_key, data = this.fieldValue))
        }
    }
    open var validate = ::gen_validate_fn
    open fun gen_validate_fn(): Boolean {
        this.showError = false
        this.errorMessage = ""
        return true
    }
    open var change = ::gen_change_fn
    open fun gen_change_fn(event: FormChangeEvent): Unit {
        this.fieldValue = event.value as Number
        this.setCache()
        this.`$emit`("change", event)
    }
    open var onSliderChange = ::gen_onSliderChange_fn
    open fun gen_onSliderChange_fn(event: UniSliderChangeEvent): Unit {
        val rawValue = event.detail.value as Number
        val validatedValue = this.validateValue(rawValue)
        this.inputValue = validatedValue.toString(10)
        val result = FormChangeEvent(index = this.index, value = validatedValue)
        this.change(result)
    }
    open var onInputChange = ::gen_onInputChange_fn
    open fun gen_onInputChange_fn(event: UniInputEvent): Unit {
        val inputStr = event.detail.value as String
        val inputNum = parseFloat(inputStr)
        if (!isNaN(inputNum)) {
            val validatedValue = this.validateValue(inputNum)
            this.sliderValue = validatedValue
            val result = FormChangeEvent(index = this.index, value = validatedValue)
            this.change(result)
        }
    }
    open var onInputBlur = ::gen_onInputBlur_fn
    open fun gen_onInputBlur_fn(): Unit {
        val inputNum = parseFloat(this.inputValue)
        if (isNaN(inputNum)) {
            this.inputValue = this.fieldValue.toString(10)
        } else {
            val validatedValue = this.validateValue(inputNum)
            this.inputValue = validatedValue.toString(10)
            this.sliderValue = validatedValue
            if (validatedValue !== this.fieldValue) {
                val result = FormChangeEvent(index = this.index, value = validatedValue)
                this.change(result)
            }
        }
        this.validate()
    }
    companion object {
        var name = "FormSlider"
        val styles: Map<String, Map<String, Map<String, Any>>> by lazy {
            _nCS(_uA(
                styles0
            ))
        }
        val styles0: Map<String, Map<String, Map<String, Any>>>
            get() {
                return _uM("slider-container" to _pS(_uM("width" to "100%", "display" to "flex", "flexDirection" to "row", "alignItems" to "center")), "slider-wrapper" to _pS(_uM("flex" to 1, "marginRight" to "20rpx")), "input-wrapper" to _pS(_uM("width" to "120rpx")), "slider-input" to _pS(_uM("width" to "100%", "height" to "60rpx", "textAlign" to "center", "borderTopWidth" to "1rpx", "borderRightWidth" to "1rpx", "borderBottomWidth" to "1rpx", "borderLeftWidth" to "1rpx", "borderTopStyle" to "solid", "borderRightStyle" to "solid", "borderBottomStyle" to "solid", "borderLeftStyle" to "solid", "borderTopColor" to "#dddddd", "borderRightColor" to "#dddddd", "borderBottomColor" to "#dddddd", "borderLeftColor" to "#dddddd", "borderTopLeftRadius" to "10rpx", "borderTopRightRadius" to "10rpx", "borderBottomRightRadius" to "10rpx", "borderBottomLeftRadius" to "10rpx", "paddingTop" to 0, "paddingRight" to "10rpx", "paddingBottom" to 0, "paddingLeft" to "10rpx", "boxSizing" to "border-box")))
            }
        var inheritAttrs = true
        var inject: Map<String, Map<String, Any?>> = _uM()
        var emits: Map<String, Any?> = _uM()
        var props = _nP(_uM("data" to _uM(), "index" to _uM("type" to "Number", "default" to 0), "keyName" to _uM("type" to "String", "default" to ""), "labelColor" to _uM("type" to "String", "default" to "#000"), "backgroundColor" to _uM("type" to "String", "default" to "#f1f4f9")))
        var propsNeedCastKeys = _uA(
            "index",
            "keyName",
            "labelColor",
            "backgroundColor"
        )
        var components: Map<String, CreateVueComponent> = _uM("FormContainer" to GenComponentsMainFormComponentsFormContainerClass)
    }
}
