<template>
	<view class="Mselect">



		<view class="Mdate">
			<view class="Mselect-name" :style="{color:color}">
				{{data.name}}
			</view>

			<view class="Mselect-box qShadow1" :style="{backgroundColor:bgColor}" @click="selectFun">
				<view class="mb-selectText">
					{{mText}}
				</view>

				<view class="mb-selectIcon">
					<mb-icons type="Calendar-1" color="#333" size="40rpx"></mb-icons>
				</view>
			</view>
		</view>



		<uni-calendar ref="calendar" @confirm="calendachange" :insert="false" :lunar="true" />

	</view>



</template>

<script>
	export default {
		name: "Mselect",
		props: {
			data: {
				type: Object,
				default: {}
			},
			keyName: {
				type: String,
				default: ""

			},
			index: {
				type: Number,
				default: 0
			},
			color: {
				type: String,
				default: '#000'
			},
			bgColor: {
				type: String,
				default: "#f1f4f9"

			}
		},
		created() {


			if (this.data.isSave) {

				this.saveKey = this.keyName + "_" + this.data.key

				uni.getStorage({
					key: this.saveKey,
					success: (res) => {
						const udate = res.data

						//判断udate是不是xxxx-xx-xx时间格式
						if (udate.match(/^\d{4}-\d{2}-\d{2}$/)) {
							this.mText = udate
							this.$emit("change", {
								index: this.index,
								value: udate
							})
							
						} else {
							this.setCurrent()
						}




					
					},
					fail: () => {



						this.setCurrent()

					}
				});


			} else {
				this.setCurrent()
			}




		},
		data() {
			return {
				saveKey:"",
				selectData: [],
				mText: "",
				mydate: ""
			};
		},
		watch: {
			data: {

				handler(newValue, oldValue) {


					if (this.data.value != "") {
						this.mText = String(this.data.value)
					}
				},
				immediate: true,
				deep: true
			}

		},
		methods: {
			setCurrent() {
				if (this.data.value == "") {

					const today = new Date();
					const year = today.getFullYear();
					const month = String(today.getMonth() + 1).padStart(2, '0');
					const day = String(today.getDate()).padStart(2, '0');
					const formattedDate = `${year}-${month}-${day}`;

					this.mText = formattedDate
					this.$emit("change", {
						index: this.index,
						value: formattedDate
					})

				}
			},
			calendachange(m) {
				console.log(m)
				this.mText = m.fulldate
				this.$emit("change", {
					index: this.index,
					value: m.fulldate
				})
				if(this.data.isSave){
					try {
						uni.setStorageSync(this.saveKey, m.fulldate);
					} catch (e) {
						// error
					}
					
				}

			},
			selectFun() {
				this.$refs.calendar.open();

			}
		}
	}
</script>

<style>
	.Mdate {
		width: 100%;
		display: flex;
		flex-direction: column;
		padding: 0 20rpx;
		margin-bottom: 20rpx;

	}

	.Mselect-name {
		width: 100%;
		font-size: 32rpx;
	}

	.Mselect-box {
		width: 100%;
		height: 100rpx;
		border: 1rpx solid #fff;
		box-sizing: border-box;
		padding: 0 10rpx;
		border-radius: 20rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
	}

	.mb-selectText {
		flex: 1;
		padding-left: 20rpx;
	}

	.mb-selectIcon {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.mb-selectIcon image {
		width: 100%;
		height: 100%;
	}
</style>