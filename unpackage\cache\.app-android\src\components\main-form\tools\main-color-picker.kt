@file:Suppress("UNCHECKED_CAST", "USELESS_CAST", "INAPPLICABLE_JVM_NAME", "UNUSED_ANONYMOUS_PARAMETER", "NAME_SHADOWING", "UNNECESSARY_NOT_NULL_ASSERTION")
package uni.UNIC178CB1
import io.dcloud.uniapp.*
import io.dcloud.uniapp.extapi.*
import io.dcloud.uniapp.framework.*
import io.dcloud.uniapp.runtime.*
import io.dcloud.uniapp.vue.*
import io.dcloud.uniapp.vue.shared.*
import io.dcloud.unicloud.*
import io.dcloud.uts.*
import io.dcloud.uts.Map
import io.dcloud.uts.Set
import io.dcloud.uts.UTSAndroid
import io.dcloud.uniapp.extapi.showActionSheet as uni_showActionSheet
open class GenComponentsMainFormToolsMainColorPicker : VueComponent {
    constructor(__ins: ComponentInternalInstance) : super(__ins) {}
    @Suppress("UNUSED_PARAMETER", "UNUSED_VARIABLE")
    override fun `$render`(): Any? {
        val _ctx = this
        val _cache = this.`$`.renderCache
        val _component_slider = resolveComponent("slider")
        return if (isTrue(_ctx.visible)) {
            _cE("view", _uM("key" to 0, "class" to "picker-overlay", "onClick" to _ctx.onOverlayClick), _uA(
                _cE("view", _uM("class" to "picker-modal", "onClick" to withModifiers(fun(){}, _uA(
                    "stop"
                ))), _uA(
                    _cE("view", _uM("class" to "color-picker-container"), _uA(
                        _cE("view", _uM("class" to "navbar"), _uA(
                            _cE("text", _uM("class" to "nav-btn cancel-btn", "onClick" to _ctx.onCancel), "取消", 8, _uA(
                                "onClick"
                            )),
                            _cE("text", _uM("class" to "nav-title"), "颜色选择"),
                            _cE("view", _uM("class" to "confirm-btn-container"), _uA(
                                _cE("text", _uM("class" to "nav-btn confirm-btn", "onClick" to _ctx.onConfirm), "确定", 8, _uA(
                                    "onClick"
                                ))
                            ))
                        )),
                        _cE("view", _uM("class" to "color-series-section"), _uA(
                            _cE("view", _uM("class" to "color-series-buttons"), _uA(
                                _cE(Fragment, null, RenderHelpers.renderList(_ctx.colorSeriesList, fun(series, index, __index, _cached): Any {
                                    return _cE("view", _uM("key" to index, "class" to _nC(_uA(
                                        "series-button",
                                        _uM("active" to (_ctx.selectedSeriesIndex == index), "random-button" to (index == 0), "normal-button" to (index != 0))
                                    )), "style" to _nS(_uM("backgroundColor" to series.color)), "onClick" to fun(){
                                        _ctx.onSeriesSelect(index)
                                    }), _uA(
                                        _cE("text", _uM("class" to "series-text"), _tD(series.name), 1)
                                    ), 14, _uA(
                                        "onClick"
                                    ))
                                }), 128)
                            ))
                        )),
                        _cE("view", _uM("class" to "color-grid-section"), _uA(
                            _cE("view", _uM("class" to "color-grid"), _uA(
                                _cE(Fragment, null, RenderHelpers.renderList(_ctx.colorList, fun(color, index, __index, _cached): Any {
                                    return _cE("view", _uM("key" to index, "class" to _nC(_uA(
                                        "color-item",
                                        _uM("selected" to (_ctx.selectedColorIndex == index))
                                    )), "style" to _nS(_uM("backgroundColor" to color)), "onClick" to fun(){
                                        _ctx.onColorSelect(index)
                                    }), null, 14, _uA(
                                        "onClick"
                                    ))
                                }), 128)
                            ))
                        )),
                        _cE("view", _uM("class" to "preview-opacity-section"), _uA(
                            _cE("view", _uM("class" to "preview-area", "onClick" to _ctx.showRGBPicker), _uA(
                                _cE("view", _uM("class" to "preview-color", "style" to _nS(_uM("backgroundColor" to _ctx.finalColor))), null, 4),
                                _cE("text", _uM("class" to "rgba-text"), _tD(_ctx.finalColor), 1)
                            ), 8, _uA(
                                "onClick"
                            )),
                            _cE("view", _uM("class" to "opacity-area"), _uA(
                                _cE("text", _uM("class" to "opacity-label"), "透明度"),
                                _cE("view", _uM("class" to "opacity-button", "onClick" to _ctx.showOpacityPicker), _uA(
                                    _cE("text", _uM("class" to "opacity-value"), _tD(Math.round(_ctx.opacity * 100)) + "%", 1)
                                ), 8, _uA(
                                    "onClick"
                                ))
                            ))
                        )),
                        if (isTrue(_ctx.showRGBModal)) {
                            _cE("view", _uM("key" to 0, "class" to "rgb-modal-overlay", "onClick" to _ctx.closeRGBPicker), _uA(
                                _cE("view", _uM("class" to "rgb-modal", "onClick" to _ctx.onRGBModalClick), _uA(
                                    _cE("view", _uM("class" to "rgb-modal-header"), _uA(
                                        _cE("text", _uM("class" to "rgb-modal-title"), "RGB颜色设置")
                                    )),
                                    _cE("view", _uM("class" to "rgb-preview-section"), _uA(
                                        _cE("view", _uM("class" to "rgb-preview-color", "style" to _nS(_uM("backgroundColor" to _ctx.tempRGBColor))), null, 4),
                                        _cE("text", _uM("class" to "rgb-preview-text"), _tD(_ctx.tempRGBColor), 1)
                                    )),
                                    _cE("view", _uM("class" to "rgb-controls"), _uA(
                                        _cE("view", _uM("class" to "rgb-control-item"), _uA(
                                            _cE("text", _uM("class" to "rgb-label"), "R"),
                                            _cV(_component_slider, _uM("class" to "rgb-slider", "min" to 0, "max" to 255, "step" to 1, "value" to _ctx.tempR, "onChange" to _ctx.onTempRChange), null, 8, _uA(
                                                "value",
                                                "onChange"
                                            )),
                                            _cE("input", _uM("class" to "rgb-input", "type" to "number", "value" to _ctx.tempR.toString(10), "onInput" to _ctx.onTempRInput, "placeholder" to "0-255"), null, 40, _uA(
                                                "value",
                                                "onInput"
                                            ))
                                        )),
                                        _cE("view", _uM("class" to "rgb-control-item"), _uA(
                                            _cE("text", _uM("class" to "rgb-label"), "G"),
                                            _cV(_component_slider, _uM("class" to "rgb-slider", "min" to 0, "max" to 255, "step" to 1, "value" to _ctx.tempG, "onChange" to _ctx.onTempGChange), null, 8, _uA(
                                                "value",
                                                "onChange"
                                            )),
                                            _cE("input", _uM("class" to "rgb-input", "type" to "number", "value" to _ctx.tempG.toString(10), "onInput" to _ctx.onTempGInput, "placeholder" to "0-255"), null, 40, _uA(
                                                "value",
                                                "onInput"
                                            ))
                                        )),
                                        _cE("view", _uM("class" to "rgb-control-item"), _uA(
                                            _cE("text", _uM("class" to "rgb-label"), "B"),
                                            _cV(_component_slider, _uM("class" to "rgb-slider", "min" to 0, "max" to 255, "step" to 1, "value" to _ctx.tempB, "onChange" to _ctx.onTempBChange), null, 8, _uA(
                                                "value",
                                                "onChange"
                                            )),
                                            _cE("input", _uM("class" to "rgb-input", "type" to "number", "value" to _ctx.tempB.toString(10), "onInput" to _ctx.onTempBInput, "placeholder" to "0-255"), null, 40, _uA(
                                                "value",
                                                "onInput"
                                            ))
                                        ))
                                    )),
                                    _cE("view", _uM("class" to "rgb-modal-buttons"), _uA(
                                        _cE("view", _uM("class" to "rgb-button rgb-cancel", "onClick" to _ctx.closeRGBPicker), _uA(
                                            _cE("text", _uM("class" to "rgb-button-text"), "取消")
                                        ), 8, _uA(
                                            "onClick"
                                        )),
                                        _cE("view", _uM("class" to "rgb-button rgb-confirm", "onClick" to _ctx.confirmRGBPicker), _uA(
                                            _cE("text", _uM("class" to "rgb-button-text"), "确定")
                                        ), 8, _uA(
                                            "onClick"
                                        ))
                                    ))
                                ), 8, _uA(
                                    "onClick"
                                ))
                            ), 8, _uA(
                                "onClick"
                            ))
                        } else {
                            _cC("v-if", true)
                        }
                    ))
                ), 8, _uA(
                    "onClick"
                ))
            ), 8, _uA(
                "onClick"
            ))
        } else {
            _cC("v-if", true)
        }
    }
    open var visible: Boolean by `$data`
    open var selectedSeriesIndex: Number by `$data`
    open var opacity: Number by `$data`
    open var selectedColorIndex: Number by `$data`
    open var baseColor: ColorInfo1 by `$data`
    open var randomSeed: Number by `$data`
    open var showRGBModal: Boolean by `$data`
    open var tempR: Number by `$data`
    open var tempG: Number by `$data`
    open var tempB: Number by `$data`
    open var customColor: String by `$data`
    open var colorSeriesList: UTSArray<ColorSeries1> by `$data`
    open var colorList: UTSArray<String> by `$data`
    open var finalColor: String by `$data`
    open var tempRGBColor: String by `$data`
    @Suppress("USELESS_CAST")
    override fun data(): Map<String, Any?> {
        return _uM("visible" to false as Boolean, "selectedSeriesIndex" to 0 as Number, "opacity" to 1.0 as Number, "selectedColorIndex" to 0 as Number, "baseColor" to ColorInfo1(r = 255.0, g = 0.0, b = 0.0), "randomSeed" to 0 as Number, "showRGBModal" to false as Boolean, "tempR" to 255 as Number, "tempG" to 0 as Number, "tempB" to 0 as Number, "customColor" to "" as String, "colorSeriesList" to computed<UTSArray<ColorSeries1>>(fun(): UTSArray<ColorSeries1> {
            return _uA(
                ColorSeries1(name = "随机色", color = "#FF6B35"),
                ColorSeries1(name = "黑白灰", color = "#808080"),
                ColorSeries1(name = "红色", color = "#FF4444"),
                ColorSeries1(name = "橙色", color = "#FF8844"),
                ColorSeries1(name = "黄色", color = "#FFDD44"),
                ColorSeries1(name = "绿色", color = "#44FF44"),
                ColorSeries1(name = "青色", color = "#44FFFF"),
                ColorSeries1(name = "蓝色", color = "#4444FF"),
                ColorSeries1(name = "紫色", color = "#AA44FF"),
                ColorSeries1(name = "粉色", color = "#FF88CC"),
                ColorSeries1(name = "棕色", color = "#AA6644")
            )
        }
        ), "colorList" to computed<UTSArray<String>>(fun(): UTSArray<String> {
            val colors: UTSArray<String> = _uA()
            run {
                var i: Number = 0
                while(i < 120){
                    val row = Math.floor(i / 12)
                    val col = i % 12
                    val colFactor = col / 11.0
                    var r: Number
                    var g: Number
                    var b: Number
                    if (this.selectedSeriesIndex == 0) {
                        val seed1 = (row * 12 + col + this.randomSeed) * 0.1
                        val seed2 = (row * 12 + col + this.randomSeed + 100) * 0.13
                        val seed3 = (row * 12 + col + this.randomSeed + 200) * 0.17
                        r = Math.round((Math.sin(seed1) * 0.5 + 0.5) * 255)
                        g = Math.round((Math.sin(seed2) * 0.5 + 0.5) * 255)
                        b = Math.round((Math.sin(seed3) * 0.5 + 0.5) * 255)
                    } else if (this.selectedSeriesIndex == 1) {
                        val totalFactor = (row * 12 + col) / 119.0
                        val grayValue = Math.round(totalFactor * 255)
                        r = grayValue
                        g = grayValue
                        b = grayValue
                    } else if (this.selectedSeriesIndex == 2) {
                        val totalFactor = (row * 12 + col) / 119.0
                        val brightness = 0.2 + totalFactor * 0.8
                        val saturation = 0.3 + (1 - Math.abs(totalFactor - 0.5) * 2) * 0.7
                        r = Math.round(brightness * 255)
                        g = Math.round(brightness * (1 - saturation) * 255)
                        b = Math.round(brightness * (1 - saturation) * 255)
                    } else {
                        val totalFactor = (row * 12 + col) / 119.0
                        var baseHue: Number
                        if (this.selectedSeriesIndex == 3) {
                            baseHue = 30
                        } else if (this.selectedSeriesIndex == 4) {
                            baseHue = 60
                        } else if (this.selectedSeriesIndex == 5) {
                            baseHue = 120
                        } else if (this.selectedSeriesIndex == 6) {
                            baseHue = 180
                        } else if (this.selectedSeriesIndex == 7) {
                            baseHue = 240
                        } else if (this.selectedSeriesIndex == 8) {
                            baseHue = 300
                        } else if (this.selectedSeriesIndex == 9) {
                            baseHue = 330
                        } else {
                            baseHue = 25
                        }
                        val hue = baseHue + (colFactor - 0.5) * 20
                        if (totalFactor < 0.4) {
                            val localFactor = totalFactor / 0.4
                            val saturation = 0.8 + localFactor * 0.2
                            val value = 0.4 + localFactor * 0.3
                            val rgb = this.hsvToRgb(hue, saturation, value)
                            r = rgb.r
                            g = rgb.g
                            b = rgb.b
                        } else if (totalFactor < 0.6) {
                            val localFactor = (totalFactor - 0.4) / 0.2
                            val saturation: Number = 1.0
                            val value = 0.8 + localFactor * 0.2
                            val rgb = this.hsvToRgb(hue, saturation, value)
                            r = rgb.r
                            g = rgb.g
                            b = rgb.b
                        } else {
                            val localFactor = (totalFactor - 0.6) / 0.4
                            val saturation = 0.8 - localFactor * 0.6
                            val value = 0.9 + localFactor * 0.1
                            val rgb = this.hsvToRgb(hue, saturation, value)
                            r = rgb.r
                            g = rgb.g
                            b = rgb.b
                        }
                    }
                    r = Math.max(0, Math.min(255, r))
                    g = Math.max(0, Math.min(255, g))
                    b = Math.max(0, Math.min(255, b))
                    colors.push("rgb(" + r + ", " + g + ", " + b + ")")
                    i++
                }
            }
            return colors
        }
        ), "finalColor" to computed<String>(fun(): String {
            var colorToUse = ""
            if (this.customColor != "") {
                colorToUse = this.customColor
            } else if (this.colorList.length > this.selectedColorIndex) {
                colorToUse = this.colorList[this.selectedColorIndex]
            }
            if (colorToUse != "") {
                val rgbMatch = colorToUse.match(UTSRegExp("rgb\\((\\d+),\\s*(\\d+),\\s*(\\d+)\\)", ""))
                if (rgbMatch != null) {
                    val r = parseInt(rgbMatch[1] as String)
                    val g = parseInt(rgbMatch[2] as String)
                    val b = parseInt(rgbMatch[3] as String)
                    return "rgba(" + r + ", " + g + ", " + b + ", " + this.opacity + ")"
                }
            }
            return "rgba(255, 0, 0, " + this.opacity + ")"
        }
        ), "tempRGBColor" to computed<String>(fun(): String {
            return "rgb(" + this.tempR + ", " + this.tempG + ", " + this.tempB + ")"
        }
        ))
    }
    open var onSeriesSelect = ::gen_onSeriesSelect_fn
    open fun gen_onSeriesSelect_fn(index: Number) {
        this.selectedSeriesIndex = index
        this.selectedColorIndex = 0
        this.customColor = ""
        if (index == 0) {
            this.randomSeed = Math.floor(Math.random() * 1000)
        }
    }
    open var showOpacityPicker = ::gen_showOpacityPicker_fn
    open fun gen_showOpacityPicker_fn() {
        val opacityOptions = _uA(
            "100%",
            "95%",
            "90%",
            "85%",
            "80%",
            "75%",
            "70%",
            "65%",
            "60%",
            "55%",
            "50%",
            "45%",
            "40%",
            "35%",
            "30%",
            "25%",
            "20%",
            "15%",
            "10%",
            "5%"
        )
        uni_showActionSheet(ShowActionSheetOptions(itemList = opacityOptions, success = fun(res){
            val selectedOpacity = (100 - res.tapIndex * 5) / 100
            this.opacity = selectedOpacity
        }
        ))
    }
    open var onColorSelect = ::gen_onColorSelect_fn
    open fun gen_onColorSelect_fn(index: Number) {
        this.selectedColorIndex = index
        this.customColor = ""
    }
    open var showRGBPicker = ::gen_showRGBPicker_fn
    open fun gen_showRGBPicker_fn() {
        var colorToUse = ""
        if (this.customColor != "") {
            colorToUse = this.customColor
        } else if (this.colorList.length > this.selectedColorIndex) {
            colorToUse = this.colorList[this.selectedColorIndex]
        }
        if (colorToUse != "") {
            val rgbMatch = colorToUse.match(UTSRegExp("rgb\\((\\d+),\\s*(\\d+),\\s*(\\d+)\\)", ""))
            if (rgbMatch != null) {
                this.tempR = parseInt(rgbMatch[1] as String)
                this.tempG = parseInt(rgbMatch[2] as String)
                this.tempB = parseInt(rgbMatch[3] as String)
            } else {
                this.tempR = 255
                this.tempG = 0
                this.tempB = 0
            }
        } else {
            this.tempR = 255
            this.tempG = 0
            this.tempB = 0
        }
        this.showRGBModal = true
    }
    open var closeRGBPicker = ::gen_closeRGBPicker_fn
    open fun gen_closeRGBPicker_fn() {
        this.showRGBModal = false
    }
    open var onRGBModalClick = ::gen_onRGBModalClick_fn
    open fun gen_onRGBModalClick_fn() {}
    open var confirmRGBPicker = ::gen_confirmRGBPicker_fn
    open fun gen_confirmRGBPicker_fn() {
        this.customColor = "rgb(" + this.tempR + ", " + this.tempG + ", " + this.tempB + ")"
        this.showRGBModal = false
    }
    open var onTempRChange = ::gen_onTempRChange_fn
    open fun gen_onTempRChange_fn(event: UniSliderChangeEvent) {
        this.tempR = event.detail.value as Number
    }
    open var onTempGChange = ::gen_onTempGChange_fn
    open fun gen_onTempGChange_fn(event: UniSliderChangeEvent) {
        this.tempG = event.detail.value as Number
    }
    open var onTempBChange = ::gen_onTempBChange_fn
    open fun gen_onTempBChange_fn(event: UniSliderChangeEvent) {
        this.tempB = event.detail.value as Number
    }
    open var onTempRInput = ::gen_onTempRInput_fn
    open fun gen_onTempRInput_fn(event: UniInputEvent) {
        val value = parseInt(event.detail.value)
        if (!isNaN(value)) {
            this.tempR = Math.max(0, Math.min(255, value))
        }
    }
    open var onTempGInput = ::gen_onTempGInput_fn
    open fun gen_onTempGInput_fn(event: UniInputEvent) {
        val value = parseInt(event.detail.value)
        if (!isNaN(value)) {
            this.tempG = Math.max(0, Math.min(255, value))
        }
    }
    open var onTempBInput = ::gen_onTempBInput_fn
    open fun gen_onTempBInput_fn(event: UniInputEvent) {
        val value = parseInt(event.detail.value)
        if (!isNaN(value)) {
            this.tempB = Math.max(0, Math.min(255, value))
        }
    }
    open var open = ::gen_open_fn
    open fun gen_open_fn() {
        this.visible = true
    }
    open var close = ::gen_close_fn
    open fun gen_close_fn() {
        this.visible = false
    }
    open var onOverlayClick = ::gen_onOverlayClick_fn
    open fun gen_onOverlayClick_fn() {
        this.close()
        this.`$emit`("cancel")
    }
    open var onCancel = ::gen_onCancel_fn
    open fun gen_onCancel_fn() {
        this.close()
        this.`$emit`("cancel")
    }
    open var onConfirm = ::gen_onConfirm_fn
    open fun gen_onConfirm_fn() {
        this.close()
        val rgbaValues = this.getRGBAValues()
        this.`$emit`("confirm", _uO("color" to this.finalColor, "rgba" to rgbaValues, "hex" to this.rgbToHex(rgbaValues.r, rgbaValues.g, rgbaValues.b)))
    }
    open var getRGBAValues = ::gen_getRGBAValues_fn
    open fun gen_getRGBAValues_fn(): RGBAValues1 {
        val rgbaMatch = this.finalColor.match(UTSRegExp("rgba\\((\\d+),\\s*(\\d+),\\s*(\\d+),\\s*([\\d.]+)\\)", ""))
        if (rgbaMatch != null) {
            return RGBAValues1(r = parseInt(rgbaMatch[1] as String), g = parseInt(rgbaMatch[2] as String), b = parseInt(rgbaMatch[3] as String), a = parseFloat(rgbaMatch[4] as String))
        }
        return RGBAValues1(r = 255, g = 0, b = 0, a = 1.0)
    }
    open var hsvToRgb = ::gen_hsvToRgb_fn
    open fun gen_hsvToRgb_fn(h: Number, s: Number, v: Number): RGBValues1 {
        val c: Number = v * s
        val x: Number = c * (1.0 - Math.abs(((h / 60.0) % 2.0) - 1.0))
        val m: Number = v - c
        var r: Number = 0.0
        var g: Number = 0.0
        var b: Number = 0.0
        if (h >= 0 && h < 60) {
            r = c
            g = x
            b = 0.0
        } else if (h >= 60 && h < 120) {
            r = x
            g = c
            b = 0.0
        } else if (h >= 120 && h < 180) {
            r = 0.0
            g = c
            b = x
        } else if (h >= 180 && h < 240) {
            r = 0.0
            g = x
            b = c
        } else if (h >= 240 && h < 300) {
            r = x
            g = 0.0
            b = c
        } else if (h >= 300 && h < 360) {
            r = c
            g = 0.0
            b = x
        }
        val result = RGBValues1(r = Math.round((r + m) * 255.0), g = Math.round((g + m) * 255.0), b = Math.round((b + m) * 255.0))
        return result
    }
    open var rgbToHex = ::gen_rgbToHex_fn
    open fun gen_rgbToHex_fn(r: Number, g: Number, b: Number): String {
        val toHex = fun(value: Number): String {
            val hex = Math.round(Math.max(0, Math.min(255, value))).toString(16)
            return if (hex.length == 1) {
                "0" + hex
            } else {
                hex
            }
        }
        return "#" + toHex(r) + toHex(g) + toHex(b)
    }
    companion object {
        var name = "main-color-picker"
        val styles: Map<String, Map<String, Map<String, Any>>> by lazy {
            _nCS(_uA(
                styles0
            ))
        }
        val styles0: Map<String, Map<String, Map<String, Any>>>
            get() {
                return _uM("picker-overlay" to _pS(_uM("position" to "fixed", "top" to 0, "left" to 0, "right" to 0, "bottom" to 0, "backgroundColor" to "rgba(0,0,0,0.5)", "display" to "flex", "alignItems" to "center", "justifyContent" to "flex-end", "zIndex" to 1000)), "picker-modal" to _pS(_uM("width" to "100%", "backgroundColor" to "#ffffff", "borderTopLeftRadius" to "20rpx", "borderTopRightRadius" to "20rpx", "overflow" to "hidden", "boxShadow" to "0 8px 32px rgba(0, 0, 0, 0.3)")), "color-picker-container" to _pS(_uM("width" to "100%", "height" to "100%", "backgroundColor" to "#ffffff", "display" to "flex", "flexDirection" to "column")), "navbar" to _pS(_uM("height" to 44, "backgroundColor" to "#f8f8f8", "borderBottomWidth" to 1, "borderBottomStyle" to "solid", "borderBottomColor" to "#e5e5e5", "display" to "flex", "flexDirection" to "row", "alignItems" to "center", "justifyContent" to "space-between", "paddingTop" to 0, "paddingRight" to 10, "paddingBottom" to 0, "paddingLeft" to 10)), "nav-btn" to _pS(_uM("fontSize" to 16, "color" to "#007aff", "paddingTop" to 8, "paddingRight" to 12, "paddingBottom" to 8, "paddingLeft" to 12)), "cancel-btn" to _pS(_uM("color" to "#999999")), "confirm-btn-container" to _pS(_uM("height" to 30, "backgroundColor" to "#007aff", "borderTopLeftRadius" to "8rpx", "borderTopRightRadius" to "8rpx", "borderBottomRightRadius" to "8rpx", "borderBottomLeftRadius" to "8rpx", "display" to "flex", "justifyContent" to "center", "alignItems" to "center", "boxShadow" to "0 2rpx 8rpx rgba(0, 122, 255, 0.3)")), "confirm-btn" to _pS(_uM("color" to "#ffffff", "fontWeight" to "bold")), "nav-title" to _pS(_uM("fontSize" to 17, "color" to "#333333")), "section-title" to _pS(_uM("fontSize" to 14, "color" to "#666666", "marginBottom" to 10)), "color-series-section" to _pS(_uM("paddingTop" to "20rpx", "paddingRight" to "20rpx", "paddingBottom" to "20rpx", "paddingLeft" to "20rpx", "borderBottomWidth" to 1, "borderBottomStyle" to "solid", "borderBottomColor" to "#f0f0f0")), "color-series-buttons" to _pS(_uM("display" to "flex", "flexDirection" to "row", "flexWrap" to "wrap", "justifyContent" to "space-between", "marginTop" to "10rpx", "paddingTop" to 0, "paddingRight" to "10rpx", "paddingBottom" to 0, "paddingLeft" to "10rpx")), "series-button" to _uM("" to _uM("height" to "60rpx", "borderTopLeftRadius" to "8rpx", "borderTopRightRadius" to "8rpx", "borderBottomRightRadius" to "8rpx", "borderBottomLeftRadius" to "8rpx", "borderTopWidth" to 2, "borderRightWidth" to 2, "borderBottomWidth" to 2, "borderLeftWidth" to 2, "borderTopStyle" to "solid", "borderRightStyle" to "solid", "borderBottomStyle" to "solid", "borderLeftStyle" to "solid", "borderTopColor" to "rgba(0,0,0,0)", "borderRightColor" to "rgba(0,0,0,0)", "borderBottomColor" to "rgba(0,0,0,0)", "borderLeftColor" to "rgba(0,0,0,0)", "display" to "flex", "alignItems" to "center", "justifyContent" to "center", "marginBottom" to "10rpx", "boxSizing" to "border-box"), ".active" to _uM("borderTopColor" to "#007aff", "borderRightColor" to "#007aff", "borderBottomColor" to "#007aff", "borderLeftColor" to "#007aff", "boxShadow" to "0 0 0 1px #007aff")), "random-button" to _pS(_uM("width" to "220rpx")), "normal-button" to _pS(_uM("width" to "100rpx")), "series-text" to _pS(_uM("fontSize" to "24rpx", "color" to "#ffffff", "fontWeight" to "bold", "textShadow" to "1px 1px 2px rgba(0, 0, 0, 0.5)")), "color-grid-section" to _pS(_uM("paddingTop" to "20rpx", "paddingRight" to "20rpx", "paddingBottom" to "20rpx", "paddingLeft" to "20rpx", "borderBottomWidth" to 1, "borderBottomStyle" to "solid", "borderBottomColor" to "#f0f0f0")), "color-grid" to _pS(_uM("display" to "flex", "flexDirection" to "row", "flexWrap" to "wrap", "justifyContent" to "space-around", "alignItems" to "flex-start", "paddingTop" to "15rpx", "paddingRight" to "15rpx", "paddingBottom" to "15rpx", "paddingLeft" to "15rpx")), "color-item" to _uM("" to _uM("width" to "55rpx", "height" to "40rpx", "borderTopLeftRadius" to 4, "borderTopRightRadius" to 4, "borderBottomRightRadius" to 4, "borderBottomLeftRadius" to 4, "borderTopWidth" to 2, "borderRightWidth" to 2, "borderBottomWidth" to 2, "borderLeftWidth" to 2, "borderTopStyle" to "solid", "borderRightStyle" to "solid", "borderBottomStyle" to "solid", "borderLeftStyle" to "solid", "borderTopColor" to "rgba(0,0,0,0)", "borderRightColor" to "rgba(0,0,0,0)", "borderBottomColor" to "rgba(0,0,0,0)", "borderLeftColor" to "rgba(0,0,0,0)", "marginBottom" to 4, "flexShrink" to 0, "flexGrow" to 0, "boxSizing" to "border-box"), ".selected" to _uM("borderTopColor" to "#007aff", "borderRightColor" to "#007aff", "borderBottomColor" to "#007aff", "borderLeftColor" to "#007aff", "boxShadow" to "0 0 0 1px #007aff")), "preview-opacity-section" to _pS(_uM("paddingTop" to "15rpx", "paddingRight" to "20rpx", "paddingBottom" to "15rpx", "paddingLeft" to "20rpx", "display" to "flex", "flexDirection" to "row", "alignItems" to "center", "justifyContent" to "space-between", "borderBottomWidth" to 1, "borderBottomStyle" to "solid", "borderBottomColor" to "#f0f0f0")), "preview-area" to _pS(_uM("display" to "flex", "flexDirection" to "row", "alignItems" to "center", "flex" to 1)), "preview-color" to _pS(_uM("width" to "60rpx", "height" to "60rpx", "borderTopLeftRadius" to "30rpx", "borderTopRightRadius" to "30rpx", "borderBottomRightRadius" to "30rpx", "borderBottomLeftRadius" to "30rpx", "borderTopWidth" to 1, "borderRightWidth" to 1, "borderBottomWidth" to 1, "borderLeftWidth" to 1, "borderTopStyle" to "solid", "borderRightStyle" to "solid", "borderBottomStyle" to "solid", "borderLeftStyle" to "solid", "borderTopColor" to "#e5e5e5", "borderRightColor" to "#e5e5e5", "borderBottomColor" to "#e5e5e5", "borderLeftColor" to "#e5e5e5", "marginRight" to "15rpx", "boxShadow" to "0 2px 8px rgba(0, 0, 0, 0.1)")), "rgba-text" to _pS(_uM("fontSize" to "24rpx", "color" to "#666666", "fontFamily" to "monospace", "backgroundColor" to "#f5f5f5", "paddingTop" to "8rpx", "paddingRight" to "12rpx", "paddingBottom" to "8rpx", "paddingLeft" to "12rpx", "borderTopLeftRadius" to "6rpx", "borderTopRightRadius" to "6rpx", "borderBottomRightRadius" to "6rpx", "borderBottomLeftRadius" to "6rpx")), "opacity-area" to _pS(_uM("display" to "flex", "flexDirection" to "column", "alignItems" to "center")), "opacity-label" to _pS(_uM("fontSize" to "24rpx", "color" to "#666666", "marginBottom" to "8rpx")), "opacity-button" to _pS(_uM("backgroundColor" to "#007aff", "paddingTop" to "12rpx", "paddingRight" to "20rpx", "paddingBottom" to "12rpx", "paddingLeft" to "20rpx", "borderTopLeftRadius" to "8rpx", "borderTopRightRadius" to "8rpx", "borderBottomRightRadius" to "8rpx", "borderBottomLeftRadius" to "8rpx", "borderTopWidth" to "medium", "borderRightWidth" to "medium", "borderBottomWidth" to "medium", "borderLeftWidth" to "medium", "borderTopStyle" to "none", "borderRightStyle" to "none", "borderBottomStyle" to "none", "borderLeftStyle" to "none", "borderTopColor" to "#000000", "borderRightColor" to "#000000", "borderBottomColor" to "#000000", "borderLeftColor" to "#000000")), "opacity-value" to _pS(_uM("fontSize" to "26rpx", "color" to "#ffffff", "fontWeight" to "bold")), "rgb-modal-overlay" to _pS(_uM("position" to "fixed", "top" to 0, "left" to 0, "right" to 0, "bottom" to 0, "backgroundColor" to "rgba(0,0,0,0.5)", "display" to "flex", "alignItems" to "center", "justifyContent" to "center", "zIndex" to 1000)), "rgb-modal" to _pS(_uM("backgroundColor" to "#ffffff", "borderTopLeftRadius" to "12rpx", "borderTopRightRadius" to "12rpx", "borderBottomRightRadius" to "12rpx", "borderBottomLeftRadius" to "12rpx", "width" to "600rpx", "maxHeight" to "800rpx", "paddingTop" to "30rpx", "paddingRight" to "30rpx", "paddingBottom" to "30rpx", "paddingLeft" to "30rpx", "boxSizing" to "border-box")), "rgb-modal-header" to _pS(_uM("display" to "flex", "justifyContent" to "center", "alignItems" to "center", "marginBottom" to "20rpx")), "rgb-modal-title" to _pS(_uM("fontSize" to "32rpx", "fontWeight" to "bold", "color" to "#333333")), "rgb-preview-section" to _pS(_uM("display" to "flex", "flexDirection" to "row", "alignItems" to "center", "justifyContent" to "center", "marginBottom" to "25rpx", "paddingTop" to "15rpx", "paddingRight" to "15rpx", "paddingBottom" to "15rpx", "paddingLeft" to "15rpx", "backgroundColor" to "#f8f8f8", "borderTopLeftRadius" to "8rpx", "borderTopRightRadius" to "8rpx", "borderBottomRightRadius" to "8rpx", "borderBottomLeftRadius" to "8rpx")), "rgb-preview-color" to _pS(_uM("width" to "60rpx", "height" to "60rpx", "borderTopLeftRadius" to "8rpx", "borderTopRightRadius" to "8rpx", "borderBottomRightRadius" to "8rpx", "borderBottomLeftRadius" to "8rpx", "borderTopWidth" to 1, "borderRightWidth" to 1, "borderBottomWidth" to 1, "borderLeftWidth" to 1, "borderTopStyle" to "solid", "borderRightStyle" to "solid", "borderBottomStyle" to "solid", "borderLeftStyle" to "solid", "borderTopColor" to "#e5e5e5", "borderRightColor" to "#e5e5e5", "borderBottomColor" to "#e5e5e5", "borderLeftColor" to "#e5e5e5", "marginRight" to "15rpx")), "rgb-preview-text" to _pS(_uM("fontSize" to "24rpx", "color" to "#666666", "fontFamily" to "monospace")), "rgb-controls" to _pS(_uM("marginBottom" to "25rpx")), "rgb-control-item" to _pS(_uM("display" to "flex", "flexDirection" to "row", "alignItems" to "center", "marginBottom" to "20rpx")), "rgb-label" to _pS(_uM("width" to "40rpx", "fontSize" to "28rpx", "fontWeight" to "bold", "color" to "#333333", "textAlign" to "center")), "rgb-slider" to _pS(_uM("flex" to 1, "marginTop" to 0, "marginRight" to "15rpx", "marginBottom" to 0, "marginLeft" to "15rpx")), "rgb-input" to _pS(_uM("width" to "120rpx", "height" to "60rpx", "borderTopWidth" to 1, "borderRightWidth" to 1, "borderBottomWidth" to 1, "borderLeftWidth" to 1, "borderTopStyle" to "solid", "borderRightStyle" to "solid", "borderBottomStyle" to "solid", "borderLeftStyle" to "solid", "borderTopColor" to "#e5e5e5", "borderRightColor" to "#e5e5e5", "borderBottomColor" to "#e5e5e5", "borderLeftColor" to "#e5e5e5", "borderTopLeftRadius" to "6rpx", "borderTopRightRadius" to "6rpx", "borderBottomRightRadius" to "6rpx", "borderBottomLeftRadius" to "6rpx", "textAlign" to "center", "fontSize" to "24rpx", "paddingTop" to 0, "paddingRight" to "10rpx", "paddingBottom" to 0, "paddingLeft" to "10rpx", "boxSizing" to "border-box")), "rgb-modal-buttons" to _pS(_uM("display" to "flex", "flexDirection" to "row", "justifyContent" to "space-between")), "rgb-button" to _pS(_uM("width" to "45%", "height" to "70rpx", "borderTopLeftRadius" to "8rpx", "borderTopRightRadius" to "8rpx", "borderBottomRightRadius" to "8rpx", "borderBottomLeftRadius" to "8rpx", "display" to "flex", "alignItems" to "center", "justifyContent" to "center")), "rgb-cancel" to _pS(_uM("backgroundColor" to "#f5f5f5", "borderTopWidth" to 1, "borderRightWidth" to 1, "borderBottomWidth" to 1, "borderLeftWidth" to 1, "borderTopStyle" to "solid", "borderRightStyle" to "solid", "borderBottomStyle" to "solid", "borderLeftStyle" to "solid", "borderTopColor" to "#e5e5e5", "borderRightColor" to "#e5e5e5", "borderBottomColor" to "#e5e5e5", "borderLeftColor" to "#e5e5e5")), "rgb-confirm" to _pS(_uM("backgroundColor" to "#007aff")), "rgb-button-text" to _uM("" to _uM("fontSize" to "28rpx", "fontWeight" to "bold"), ".rgb-cancel " to _uM("color" to "#666666"), ".rgb-confirm " to _uM("color" to "#ffffff")))
            }
        var inheritAttrs = true
        var inject: Map<String, Map<String, Any?>> = _uM()
        var emits: Map<String, Any?> = _uM("cancel" to null, "confirm" to null)
        var props = _nP(_uM())
        var propsNeedCastKeys: UTSArray<String> = _uA()
        var components: Map<String, CreateVueComponent> = _uM()
    }
}
