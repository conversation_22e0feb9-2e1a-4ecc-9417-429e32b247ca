<template>
	<!-- #ifdef APP -->
	<scroll-view class="container">
	<!-- #endif -->
		<view class="content">
			<text class="title">日历弹窗测试</text>
			
			<view class="test-section">
				<button class="test-btn" @click="openCalendarPicker">打开日历选择器</button>
				
				<view class="result-section">
					<text class="result-label">选择的日期：</text>
					<text class="result-value">{{ selectedDate  }}</text>
				</view>
			</view>
		</view>

		<!-- 日历选择器 -->
		<main-calendar-picker 
			ref="calendarPicker" 
			:initial-date="initialDate"
			@confirm="onCalendarConfirm" 
			@cancel="onCalendarCancel">
		</main-calendar-picker>
	<!-- #ifdef APP -->
	</scroll-view>
	<!-- #endif -->
</template>

<script>
	import MainCalendarPicker from '@/components/main-calendar-picker.uvue'

	export default {
		name: "calendar-test",
		components: {
			MainCalendarPicker
		},
		data() {
			return {
				// 初始日期
				initialDate: "" as string,
				// 选择的日期
				selectedDate: "" as string
			}
		},
		created() {
			// 设置初始日期为今天
			const today = new Date()
			const year = today.getFullYear()
			const month = today.getMonth() + 1
			const date = today.getDate()
			this.initialDate = `${year}-${month < 10 ? '0' + month : month}-${date < 10 ? '0' + date : date}`
		},
		methods: {
			// 打开日历选择器
			openCalendarPicker() {
				const calendarPicker = this.$refs["calendarPicker"] as ComponentPublicInstance
				calendarPicker.$callMethod("open")
			},

			// 日历选择确认
			onCalendarConfirm(dateData: UTSJSONObject) {
				console.log('选择的日期:', dateData)
				const date = dateData.getString("fullDate")
				if (date != null) {
					this.selectedDate = date
				}
				
				uni.showToast({
					title: `已选择: ${this.selectedDate}`,
					icon: 'success'
				})
			},

			// 日历选择取消
			onCalendarCancel() {
				console.log('取消选择日期')
				uni.showToast({
					title: '取消选择',
					icon: 'none'
				})
			}
		}
	}
</script>

<style>
	.container {
		flex: 1;
		background-color: #f5f5f5;
	}

	.content {
		padding: 40rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.title {
		font-size: 48rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 60rpx;
	}

	.test-section {
		width: 100%;
		max-width: 600rpx;
		background-color: #ffffff;
		border-radius: 20rpx;
		padding: 40rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	}

	.test-btn {
		width: 100%;
		height: 80rpx;
		background-color: #007aff;
		color: #ffffff;
		border-radius: 12rpx;
		font-size: 32rpx;
		font-weight: bold;
		border: none;
		margin-bottom: 40rpx;
	}

	.result-section {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 30rpx;
		background-color: #f8f9fa;
		border-radius: 12rpx;
	}

	.result-label {
		font-size: 28rpx;
		color: #666666;
		margin-bottom: 10rpx;
	}

	.result-value {
		font-size: 36rpx;
		color: #007aff;
		font-weight: bold;
	}
</style>
