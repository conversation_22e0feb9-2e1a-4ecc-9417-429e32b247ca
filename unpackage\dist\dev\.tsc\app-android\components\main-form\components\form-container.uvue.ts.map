{"version": 3, "file": "components/main-form/components/form-container.uvue", "names": [], "sources": ["components/main-form/components/form-container.uvue"], "sourcesContent": ["<template>\n\t<view class=\"form-container-body\">\n\t\t<view class=\"form-container\">\n\t\t\t<view class=\"form-container-label\" v-if=\"label\">\n\t\t\t\t<view class=\"form-container-name\" >\r\n\t\t\t\t\t\n\t\t\t\t\t<text class=\"form-container-name-text\" :style=\"{color: labelColor}\">{{ label }}</text>\n\t\t\t\t\t<view class=\"form-container-tip-icon\" v-if=\"tip!=''\" @click=\"showTipModal\">\r\n\t\t\t\t\t\t<text class=\"form-container-tip-text\">!</text>\r\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<view class=\"form-container-box\" :style=\"{\n\t\t\t\tbackgroundColor: backgroundColor,\n\t\t\t\tborderColor: showError ? 'red' : '#fff'\n\t\t\t}\">\n\t\t\t\t<slot name=\"input-content\"></slot>\n\t\t\t</view> \n\t\t</view>\n\n\t\t<view class=\"form-container-tip\" v-if=\"showError\">\n\t\t\t<text class=\"form-container-error\">{{ errorMessage }}</text>\n\t\t</view>\n\t</view>\n</template>\n\n<script lang=\"uts\">\n\texport default {\n\t\tname: \"FormContainer\",\n\t\tprops: {\n\t\t\tlabel: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"\"\n\t\t\t},\n\t\t\tshowError: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: false\n\t\t\t},\n\t\t\terrorMessage: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"\"\n\t\t\t},\n\t\t\ttip: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"\"\n\t\t\t},\n\t\t\tlabelColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#000\"\n\t\t\t},\n\t\t\tbackgroundColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#f1f4f9\"\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\tshowTipModal() {\n\t\t\t\tif (this.tip!=\"\") {\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\tcontent: this.tip,\n\t\t\t\t\t\tshowCancel: false,\n\t\t\t\t\t\tconfirmText: '知道了'\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t.form-container-body {\n\t\twidth: 100%;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tpadding: 0 20rpx;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.form-container {\n\t\twidth: 100%;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t}\n\n\t.form-container-label {\n\t\twidth: 100%;\n\t\tmargin-bottom: 10rpx;\n\t}\n\n\t.form-container-name {\n\t\t\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t}\n\t.form-container-name-text{\r\n\t\tfont-size: 30rpx;\r\n\t}\n\t.form-container-tip-icon {\n\t\twidth: 40rpx;\n\t\theight: 40rpx;\n\t\tbackground-color: rgba(0, 0, 0, 0.05);\n\t\tborder-radius: 20rpx;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tmargin-left: 10rpx;\n\t}\n\n\t.form-container-tip-text {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666;\n\t\tfont-weight: bold;\n\t}\n\n\t.form-container-box {\n\t\twidth: 100%;\n\t\tborder: 1rpx solid #fff;\n\t\tbox-sizing: border-box;\n\t\tpadding: 20rpx;\n\t\tborder-radius: 20rpx;\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t}\n\n\t.form-container-tip {\n\t\twidth: 100%;\n\t\t\n\t\tmargin-top: 10rpx;\n\t}\n\n\t.form-container-error {\n\t\tcolor: red;\n\t\tfont-size: 28rpx;\n\t\ttext-align: right;\n\t}\n\n\t/* 通用输入元素样式类 */\n\t.form-input-element {\n\t\tflex: 1;\n\t\tmin-height: 60rpx;\n\t}\n\n\t/* 阴影样式 */\n\t.qShadow1 {\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n\t}\n</style>\n"], "mappings": ";CA4BC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACd,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;GACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;GACX,CAAC;GACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;GACd,CAAC;GACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;GACX,CAAC;GACD,CAAC,CAAC,CAAC,EAAE;IACJ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;GACX,CAAC;GACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;GACf,CAAC;GACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GAClB;EACD,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;GACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;KACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KAClB,CAAC;IACF;GACD;EACD;CACD;;;;;;SAnEA,IAuBO,cAvBD,KAAK,EAAC,qBAAqB;IAChC,IAiBO,cAjBD,KAAK,EAAC,gBAAgB;aACc,UAAK;UAA9C,IAQO;;YARD,KAAK,EAAC,sBAAsB;;YACjC,IAMO,cAND,KAAK,EAAC,qBAAqB;cAEhC,IAAsF;gBAAhF,KAAK,EAAC,0BAA0B;gBAAE,KAAK,MAAE,6BAAmB;sBAAK,UAAK;cAChC,QAAG;kBAA/C,IAEO;;oBAFD,KAAK,EAAC,yBAAyB;oBAAiB,OAAK,EAAE,iBAAY;;oBACxE,IAA8C,cAAxC,KAAK,EAAC,yBAAyB,KAAC,GAAC;;;;;;MAK1C,IAKO;QALD,KAAK,EAAC,oBAAoB;QAAE,KAAK,MAAE;;;KAGxC;;QACA,WAAkC;;;WAIG,cAAS;QAAhD,IAEO;;UAFD,KAAK,EAAC,oBAAoB;;UAC/B,IAA4D,cAAtD,KAAK,EAAC,sBAAsB,SAAI,iBAAY"}