<template>
	<view class="Mswitch">
		<view class="Mswitch-name" :style="{color:color}">
			{{data.name}}
		</view>
	
		<view class="Mswitch-box qShadow1" :style="{backgroundColor:bgColor}">
			<switch color="#8A6DE9" :checked="mValue" @change="change"/>
		</view>
	
	</view>
	
	
	
</template>

<script>
	export default {
		name: "Ms<PERSON>",
		props: {
			data: {
				type: Object,
				default: {}
			},
			keyName: {
				type: String,
				default: ""
			
			},
			index: {
				type: Number,
				default: 0
			},
			color: {
				type: String,
				default: '#000'
			},
			bgColor: {
				type: String,
				default: "#f1f4f9"
			
			}
		},
		created() {
			
			
			if(this.data.isSave){
				
				this.saveKey=this.keyName+"_"+this.data.key
				
				uni.getStorage({
					key: this.saveKey,
					success:  (res)=> {
						
						if(res.data==1){
							this.mValue=true
							if(this.data.varType=="Boolean"){
								this.$emit("change",{index:this.index,value:true})
							}else{
								this.$emit("change",{index:this.index,value:1})
							}
						}else{
							this.mValue=false
							if(this.data.varType=="Boolean"){
								this.$emit("change",{index:this.index,value:false})
							}else{
								this.$emit("change",{index:this.index,value:0})
							}
						}
						
						
						
						
						
					},
					fail: () => {
						
						
						if(this.data.value){
							this.mValue=true
							if(this.data.varType=="Boolean"){
								this.$emit("change",{index:this.index,value:true})
							}else{
								this.$emit("change",{index:this.index,value:1})
							}
						}else{
							this.mValue=false
							if(this.data.varType=="Boolean"){
								this.$emit("change",{index:this.index,value:false})
							}else{
								this.$emit("change",{index:this.index,value:0})
							}
						}
						
						
					}
				});
				
				
			}else{
				
				if(this.data.varType=="Boolean"){
					
					
					this.mValue=this.data.value
				}else{
					
					
					if(this.data.value==1){
						this.mValue=true
					}else{
						this.mValue=false
					}
					
					
				}
				
				
			}
		
		},
		data() {
			return {
				mValue:false,
				saveKey:""
			};
		},
		watch:{
			data: {
				
				handler(newValue, oldValue) {
					if(this.data.varType=="Boolean"){
						
						
						this.mValue=this.data.value
					}else{
						
						
						if(this.data.value==1){
							this.mValue=true
						}else{
							this.mValue=false
						}
						
						
					}
					
				},
				deep: true
			}
			
		},
		methods:{
			change(e){
				if(e.detail.value){
					if(this.data.varType=="Boolean"){
						
						this.$emit("change",{index:this.index,value:true})
						
					}else{
						
						
						this.$emit("change",{index:this.index,value:1})
						
					}
					
					
					if(this.data.isSave){
						try {
							uni.setStorageSync(this.saveKey, 1);
						} catch (e) {
							// error
						}
						
					}
					
				}else{
					if(this.data.varType=="Boolean"){
						
						this.$emit("change",{index:this.index,value:false})
						
					}else{
						
						
						this.$emit("change",{index:this.index,value:0})
						
					}
					
					if(this.data.isSave){
						try {
							uni.setStorageSync(this.saveKey, 0);
						} catch (e) {
							// error
						}
						
					}
					
				}
			}
		}
	}
</script>

<style>
.Mswitch {
		width: 100%;
		display: flex;
		flex-direction: column;
		padding: 0 20rpx;
		margin-bottom: 20rpx;
	
	}
	
	.Mswitch-name {
		width: 100%;
		font-size: 32rpx;
		margin-bottom: 10rpx;
	}
	
	.Mswitch-box {
		width: 100%;
		height: 100rpx;
		border: 1rpx solid #fff;
		box-sizing: border-box;
		padding: 0 10rpx;
		border-radius: 20rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: flex-end;
	}
</style>