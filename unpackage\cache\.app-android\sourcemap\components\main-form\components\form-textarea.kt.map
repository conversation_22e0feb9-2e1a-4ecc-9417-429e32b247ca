{"version": 3, "sources": ["components/main-form/components/form-textarea.uvue", "components/main-form/components/form-input.uvue"], "sourcesContent": ["<template>\n\t<form-container :label=\"fieldName\" :show-error=\"showError\" :tip=\"tip\" :error-message=\"errorMessage\" :label-color=\"labelColor\"\n\t\t:background-color=\"backgroundColor\">\n\t\t<template #input-content>\n\t\t\t<textarea class=\"form-textarea-element\" v-model=\"fieldValue\" :maxlength=\"maxLength\" :placeholder=\"placeholder\" @input=\"onInput\"\n\t\t\t\t@blur=\"onBlur\" />\n\t\t</template>\n\t</form-container>\n</template>\n\n<script lang=\"uts\">\n\timport { FormFieldData, FormChangeEvent } from '@/components/main-form/form_type.uts'\n\timport FormContainer from './form-container.uvue'\n\n\texport default {\n\t\tname: \"FormTextarea\",\n\t\tcomponents: {\n\t\t\tFormContainer\n\t\t},\n\t\tprops: {\n\t\t\tdata: {\n\t\t\t\ttype: Object as PropType<FormFieldData>\n\t\t\t},\n\t\t\tindex: {\n\t\t\t\ttype: Number,\n\t\t\t\tdefault: 0\n\t\t\t},\n\t\t\tkeyName: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"\"\n\t\t\t},\n\t\t\tlabelColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#000\"\n\t\t\t},\n\t\t\tbackgroundColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#f1f4f9\"\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tfieldName: \"\",\n\t\t\t\tfieldValue: \"\",\n\t\t\t\tisSave: false,\n\t\t\t\tsave_key: \"\",\n\t\t\t\ttip: \"\",\n\t\t\t\tplaceholder: \"\",\n\t\t\t\tminLength: 0,\n\t\t\t\tmaxLength: -1,\n\t\t\t\tshowError: false,\n\t\t\t\terrorMessage: \"\"\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\n\t\t},\n\t\twatch: {\n\t\t\tdata: {\n\t\t\t\thandler(obj: FormFieldData) {\n\t\t\t\t\t// 只处理value的变化，当外部传入的value与当前fieldValue不同时，才更新fieldValue\n\t\t\t\t\t// 这避免了用户输入时的循环更新问题\n\t\t\t\t\tconst newValue = obj.value as string\n\t\t\t\t\tif (newValue !== this.fieldValue) {\n\t\t\t\t\t\tthis.fieldValue = newValue\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tdeep: true\n\t\t\t}\n\t\t},\n\t\tcreated(): void {\n\t\t\t// 初始化时调用一次即可\n\t\t\tconst fieldObj = this.$props[\"data\"] as FormFieldData\n\t\t\tthis.initFieldData(fieldObj)\n\t\t},\n\t\tmethods: {\n\t\t\t// 初始化字段数据（仅在首次加载时调用）\n\t\t\tinitFieldData(fieldObj: FormFieldData): void {\n\t\t\t\tconst fieldKey = fieldObj.key\n\t\t\t\tconst fieldValue = fieldObj.value as string\n\n\t\t\t\t// 设置基本信息\n\t\t\t\tthis.fieldName = fieldObj.name\n\t\t\t\tthis.fieldValue = fieldValue\n\t\t\t\tthis.isSave = fieldObj.isSave ?? false\n\t\t\t\tthis.save_key = this.keyName + \"_\" + fieldKey\n\n\t\t\t\t// 解析配置信息（注意：textarea没有inputmode属性）\n\t\t\t\tconst extalJson = fieldObj.extra as UTSJSONObject\n\t\t\t\tthis.minLength = extalJson.getNumber(\"minLength\") ?? 0\n\t\t\t\tthis.maxLength = extalJson.getNumber(\"maxLength\") ?? -1\n\t\t\t\tthis.placeholder = extalJson.getString(\"placeholder\") ?? \"\"\n\t\t\t\tthis.tip = extalJson.getString(\"tip\") ?? \"\"\n\n\t\t\t\t// 获取缓存\n\t\t\t\tthis.getCache()\n\t\t\t},\n\n\t\t\tgetCache(): void {\n\t\t\t\tif (this.isSave) {\n\t\t\t\t\tconst that = this\n\t\t\t\t\tuni.getStorage({\n\t\t\t\t\t\tkey: this.save_key,\n\t\t\t\t\t\tsuccess: (res: GetStorageSuccess) => {\n\t\t\t\t\t\t\tconst save_value = res.data as string\n\t\t\t\t\t\t\tthat.fieldValue = save_value\r\n\t\t\t\t\t\t\tthis.manualChange()\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tsetCache(): void {\n\t\t\t\tif (this.isSave) {\n\t\t\t\t\tuni.setStorage({\n\t\t\t\t\t\tkey: this.save_key,\n\t\t\t\t\t\tdata: this.fieldValue\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tvalidate(): boolean {\n\t\t\t\t// 如果没有设置最大长度限制，只检查最小长度\n\t\t\t\tif (this.maxLength == -1) {\n\t\t\t\t\tif (this.minLength > 0 && this.fieldValue.length < this.minLength) {\n\t\t\t\t\t\tthis.showError = true\n\t\t\t\t\t\tthis.errorMessage = `输入内容不能少于${this.minLength}个字符`\n\t\t\t\t\t\treturn false\n\t\t\t\t\t}\n\t\t\t\t\tthis.showError = false\n\t\t\t\t\tthis.errorMessage = \"\"\n\t\t\t\t\treturn true\n\t\t\t\t}\n\n\t\t\t\t// 检查最小长度\n\t\t\t\tif (this.fieldValue.length < this.minLength) {\n\t\t\t\t\tthis.showError = true\n\t\t\t\t\tthis.errorMessage = `输入内容不能少于${this.minLength}个字符`\n\t\t\t\t\treturn false\n\t\t\t\t}\n\n\t\t\t\t// 检查最大长度\n\t\t\t\tif (this.maxLength > 0 && this.fieldValue.length > this.maxLength) {\n\t\t\t\t\tthis.showError = true\n\t\t\t\t\tthis.errorMessage = `输入内容不能超过${this.maxLength}个字符`\n\t\t\t\t\treturn false\n\t\t\t\t}\n\n\t\t\t\tthis.showError = false\n\t\t\t\tthis.errorMessage = \"\"\n\t\t\t\treturn true\n\t\t\t},\n\t\t\t\n\t\t\tchange(event: FormChangeEvent): void {\n\t\t\t\t// 更新字段值\n\t\t\t\tthis.fieldValue = event.value as string\n\t\t\t\t// 保存缓存\n\t\t\t\tthis.setCache()\n\t\t\t\t// 触发父组件事件\n\t\t\t\tthis.$emit('change', event)\n\t\t\t},\n\t\t\tmanualChange(): void {\r\n\t\t\t\tconst result: FormChangeEvent = {\r\n\t\t\t\t\tindex: this.index,\r\n\t\t\t\t\tvalue: this.fieldValue\r\n\t\t\t\t}\r\n\t\t\t\tthis.change(result)\r\n\t\t\t},\n\t\t\tonInput(event: UniInputEvent): void {\n\t\t\t\tconst result: FormChangeEvent = {\n\t\t\t\t\tindex: this.index,\n\t\t\t\t\tvalue: event.detail.value\n\t\t\t\t}\n\t\t\t\tthis.change(result)\n\t\t\t},\n\n\t\t\tonBlur(): void {\n\t\t\t\t// 在失去焦点时进行验证\n\t\t\t\tthis.validate()\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t.form-textarea-element {\n\t\tflex: 1;\n\t\tmin-height: 120rpx;\n\t}\n</style>\n", null], "names": [], "mappings": ";;;;;;;;;;;;;+BAuGM;+BAkBS;AA3GT;;kBAwDJ,OAAW,IAAG,CAAA;YAEb,IAAM,WAAW,IAAI,CAAC,QAAM,CAAC,OAAM,CAAA,EAAA;YACnC,IAAI,CAAC,aAAa,CAAC;QACpB;;;;;UAfE,IAAQ,kBAAkB,EAAA;YAGzB,IAAM,WAAW,IAAI,KAAI,CAAA,EAAA,CAAK,MAAK;YACnC,IAAI,aAAa,IAAI,CAAC,UAAU,EAAE;gBACjC,IAAI,CAAC,UAAS,GAAI;;QAEpB;uBACA,OAAM,IAAG;;;;;;;eAlEZ,IAMiB,2BAAA,IANA,WAAO,KAAA,SAAS,EAAG,gBAAY,KAAA,SAAS,EAAG,SAAK,KAAA,GAAG,EAAG,mBAAe,KAAA,YAAY,EAAG,iBAAa,KAAA,UAAU,EAC1H,sBAAkB,KAAA,eAAe,OACvB,mBAAa,YACvB,gBACkB,GAAA;mBAAA;gBADlB,IACkB,YAAA,IADR,WAAM,yCAAiC,KAAA,UAAU;;wBAAV,KAAA,UAAU,GAAA,SAAA,MAAA,CAAA,KAAA;oBAAA;;oBAA4D,KAAA,OAAO;iBAAA,EAAhE,eAAW,KAAA,SAAS,EAAG,iBAAa,KAAA,WAAW,EAC3G,YAAM,KAAA,MAAM;;;;;;;;;;;;;;;;;;;;;;;aAqCb;aACA;aACA;aACA;aACA;aACA;aACA;aACA;aACA;aACA;;;mBATA,eAAW,IACX,gBAAY,IACZ,YAAQ,KAAK,EACb,cAAU,IACV,SAAK,IACL,iBAAa,IACb,eAAW,CAAC,EACZ,eAAW,CAAC,CAAC,EACb,eAAW,KAAK,EAChB,kBAAc;;aA0Bf;aAAA,qBAAc,uBAAuB,GAAG,IAAG,CAAA;QAC1C,IAAM,WAAW,SAAS,GAAE;QAC5B,IAAM,aAAa,SAAS,KAAI,CAAA,EAAA,CAAK,MAAK;QAG1C,IAAI,CAAC,SAAQ,GAAI,SAAS,IAAG;QAC7B,IAAI,CAAC,UAAS,GAAI;QAClB,IAAI,CAAC,MAAK,GAAI,SAAS,MAAK,IAAK,KAAI;QACrC,IAAI,CAAC,QAAO,GAAI,IAAI,CAAC,OAAM,GAAI,MAAM;QAGrC,IAAM,YAAY,SAAS,KAAI,CAAA,EAAA,CAAK;QACpC,IAAI,CAAC,SAAQ,GAAI,UAAU,SAAS,CAAC,gBAAgB,CAAA;QACrD,IAAI,CAAC,SAAQ,GAAI,UAAU,SAAS,CAAC,gBAAgB,CAAC,CAAA;QACtD,IAAI,CAAC,WAAU,GAAI,UAAU,SAAS,CAAC,kBAAkB;QACzD,IAAI,CAAC,GAAE,GAAI,UAAU,SAAS,CAAC,UAAU;QAGzC,IAAI,CAAC,QAAQ;IACd;aAEA;aAAA,mBAAY,IAAG,CAAA;QACd,IAAI,IAAI,CAAC,MAAM,EAAE;YAChB,IAAM,OAAO,IAAG;YAChB,iCACC,MAAK,IAAI,CAAC,QAAQ,EAClB,UAAS,IAAC,KAAK,kBAAoB;gBAClC,IAAM,aAAa,IAAI,IAAG,CAAA,EAAA,CAAK,MAAK;gBACpC,KAAK,UAAS,GAAI;gBAClB,IAAI,CAAC,YAAY;YAClB;;;IAGH;aAEA;aAAA,mBAAY,IAAG,CAAA;QACd,IAAI,IAAI,CAAC,MAAM,EAAE;YAChB,iCACC,MAAK,IAAI,CAAC,QAAQ,EAClB,OAAM,IAAI,CAAC,UAAS;;IAGvB;aAEA;aAAA,mBAAY,OAAM,CAAA;QAEjB,IAAI,IAAI,CAAC,SAAQ,IAAK,CAAC,CAAC,EAAE;YACzB,IAAI,IAAI,CAAC,SAAQ,GAAI,CAAA,IAAK,IAAI,CAAC,UAAU,CAAC,MAAK,GAAI,IAAI,CAAC,SAAS,EAAE;gBAClE,IAAI,CAAC,SAAQ,GAAI,IAAG;gBACpB,IAAI,CAAC,YAAW,GAAI,qDAAW,IAAI,CAAC,SAAS,GAAA;gBAC7C,OAAO,KAAI;;YAEZ,IAAI,CAAC,SAAQ,GAAI,KAAI;YACrB,IAAI,CAAC,YAAW,GAAI;YACpB,OAAO,IAAG;;QAIX,IAAI,IAAI,CAAC,UAAU,CAAC,MAAK,GAAI,IAAI,CAAC,SAAS,EAAE;YAC5C,IAAI,CAAC,SAAQ,GAAI,IAAG;YACpB,IAAI,CAAC,YAAW,GAAI,qDAAW,IAAI,CAAC,SAAS,GAAA;YAC7C,OAAO,KAAI;;QAIZ,IAAI,IAAI,CAAC,SAAQ,GAAI,CAAA,IAAK,IAAI,CAAC,UAAU,CAAC,MAAK,GAAI,IAAI,CAAC,SAAS,EAAE;YAClE,IAAI,CAAC,SAAQ,GAAI,IAAG;YACpB,IAAI,CAAC,YAAW,GAAI,qDAAW,IAAI,CAAC,SAAS,GAAA;YAC7C,OAAO,KAAI;;QAGZ,IAAI,CAAC,SAAQ,GAAI,KAAI;QACrB,IAAI,CAAC,YAAW,GAAI;QACpB,OAAO,IAAG;IACX;aAEA;aAAA,cAAO,sBAAsB,GAAG,IAAG,CAAA;QAElC,IAAI,CAAC,UAAS,GAAI,MAAM,KAAI,CAAA,EAAA,CAAK,MAAK;QAEtC,IAAI,CAAC,QAAQ;QAEb,IAAI,CAAC,OAAK,CAAC,UAAU;IACtB;aACA;aAAA,uBAAgB,IAAG,CAAA;QAClB,IAAM,yBACL,QAAO,IAAI,CAAC,KAAK,EACjB,QAAO,IAAI,CAAC,UAAS;QAEtB,IAAI,CAAC,MAAM,CAAC;IACb;aACA;aAAA,eAAQ,OAAO,aAAa,GAAG,IAAG,CAAA;QACjC,IAAM,yBACL,QAAO,IAAI,CAAC,KAAK,EACjB,QAAO,MAAM,MAAM,CAAC,KAAI;QAEzB,IAAI,CAAC,MAAM,CAAC;IACb;aAEA;aAAA,iBAAU,IAAG,CAAA;QAEZ,IAAI,CAAC,QAAQ;IACd;;mBApKK;;;;;;;;;;;;;+GAUK,CAAA,qDAIA,0DAIA,mEAIA;;;;;;;;;AAgJZ"}