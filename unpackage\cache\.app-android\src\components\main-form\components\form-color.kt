@file:Suppress("UNCHECKED_CAST", "USELESS_CAST", "INAPPLICABLE_JVM_NAME", "UNUSED_ANONYMOUS_PARAMETER", "NAME_SHADOWING", "UNNECESSARY_NOT_NULL_ASSERTION")
package uni.UNIC178CB1
import io.dcloud.uniapp.*
import io.dcloud.uniapp.extapi.*
import io.dcloud.uniapp.framework.*
import io.dcloud.uniapp.runtime.*
import io.dcloud.uniapp.vue.*
import io.dcloud.uniapp.vue.shared.*
import io.dcloud.unicloud.*
import io.dcloud.uts.*
import io.dcloud.uts.Map
import io.dcloud.uts.Set
import io.dcloud.uts.UTSAndroid
import io.dcloud.uniapp.extapi.getStorage as uni_getStorage
import io.dcloud.uniapp.extapi.setStorage as uni_setStorage
open class GenComponentsMainFormComponentsFormColor : VueComponent {
    constructor(__ins: ComponentInternalInstance) : super(__ins) {
        onCreated(fun(): Unit {
            val fieldObj = this.`$props`["data"] as FormFieldData
            this.initFieldData(fieldObj)
        }
        , __ins)
        this.`$watch`(fun(): Any? {
            return this.data
        }
        , fun(obj: FormFieldData) {
            val newValue = obj.value as String
            if (newValue !== this.fieldValue) {
                this.fieldValue = newValue
                this.updateDisplayColor()
            }
        }
        , WatchOptions(deep = true))
    }
    @Suppress("UNUSED_PARAMETER", "UNUSED_VARIABLE")
    override fun `$render`(): Any? {
        val _ctx = this
        val _cache = this.`$`.renderCache
        val _component_form_container = resolveComponent("form-container")
        val _component_main_color_picker = resolveEasyComponent("main-color-picker", GenComponentsMainColorPickerMainColorPickerClass)
        return _cE(Fragment, null, _uA(
            _cV(_component_form_container, _uM("label" to _ctx.fieldName, "show-error" to _ctx.showError, "tip" to _ctx.tip, "error-message" to _ctx.errorMessage, "label-color" to _ctx.labelColor, "background-color" to _ctx.backgroundColor), _uM("input-content" to withSlotCtx(fun(): UTSArray<Any> {
                return _uA(
                    _cE("view", _uM("class" to "color-display-container", "onClick" to _ctx.openColorPicker), _uA(
                        _cE("view", _uM("class" to "color-preview", "style" to _nS(_uM("backgroundColor" to _ctx.displayColor))), null, 4),
                        _cE("text", _uM("class" to "color-text"), _tD(_ctx.displayColor), 1)
                    ), 8, _uA(
                        "onClick"
                    ))
                )
            }
            ), "_" to 1), 8, _uA(
                "label",
                "show-error",
                "tip",
                "error-message",
                "label-color",
                "background-color"
            )),
            _cV(_component_main_color_picker, _uM("ref" to "colorPicker", "onConfirm" to _ctx.onColorConfirm, "onCancel" to _ctx.onColorCancel), null, 8, _uA(
                "onConfirm",
                "onCancel"
            ))
        ), 64)
    }
    open var data: Any? by `$props`
    open var index: Number by `$props`
    open var keyName: String by `$props`
    open var labelColor: String by `$props`
    open var backgroundColor: String by `$props`
    open var fieldName: String by `$data`
    open var fieldValue: String by `$data`
    open var isSave: Boolean by `$data`
    open var save_key: String by `$data`
    open var tip: String by `$data`
    open var varType: String by `$data`
    open var displayColor: String by `$data`
    open var showError: Boolean by `$data`
    open var errorMessage: String by `$data`
    @Suppress("USELESS_CAST")
    override fun data(): Map<String, Any?> {
        return _uM("fieldName" to "", "fieldValue" to "" as String, "isSave" to false, "save_key" to "", "tip" to "", "varType" to "hex", "displayColor" to "#000000", "showError" to false, "errorMessage" to "")
    }
    open var initFieldData = ::gen_initFieldData_fn
    open fun gen_initFieldData_fn(fieldObj: FormFieldData): Unit {
        val fieldKey = fieldObj.key
        val fieldValue = fieldObj.value as String
        this.fieldName = fieldObj.name
        this.fieldValue = fieldValue
        this.isSave = fieldObj.isSave ?: false
        this.save_key = this.keyName + "_" + fieldKey
        val extalJson = fieldObj.extra as UTSJSONObject
        this.tip = extalJson.getString("tip") ?: ""
        this.varType = extalJson.getString("varType") ?: "hex"
        this.updateDisplayColor()
        this.getCache()
    }
    open var updateDisplayColor = ::gen_updateDisplayColor_fn
    open fun gen_updateDisplayColor_fn(): Unit {
        if (this.fieldValue != "") {
            this.displayColor = this.fieldValue as String
        } else {
            if (this.varType == "rgba") {
                this.displayColor = "rgba(0, 0, 0, 1)"
                this.fieldValue = "rgba(0, 0, 0, 1)"
            } else {
                this.displayColor = "#000000"
                this.fieldValue = "#000000"
            }
        }
    }
    open var getCache = ::gen_getCache_fn
    open fun gen_getCache_fn(): Unit {
        if (this.isSave) {
            val that = this
            uni_getStorage(GetStorageOptions(key = this.save_key, success = fun(res: GetStorageSuccess){
                val save_value = res.data
                if (UTSAndroid.`typeof`(save_value) === "string") {
                    that.fieldValue = save_value as String
                    that.updateDisplayColor()
                    val result = FormChangeEvent(index = this.index, value = save_value)
                    this.change(result)
                }
            }
            ))
        }
    }
    open var setCache = ::gen_setCache_fn
    open fun gen_setCache_fn(): Unit {
        if (this.isSave && UTSAndroid.`typeof`(this.fieldValue) === "string") {
            uni_setStorage(SetStorageOptions(key = this.save_key, data = this.fieldValue))
        }
    }
    open var validate = ::gen_validate_fn
    open fun gen_validate_fn(): Boolean {
        if (this.fieldValue == "") {
            this.showError = true
            this.errorMessage = "请选择颜色"
            return false
        }
        val colorValue = this.fieldValue as String
        if (this.varType == "hex") {
            val hexPattern = UTSRegExp("^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})\$", "")
            if (!hexPattern.test(colorValue)) {
                this.showError = true
                this.errorMessage = "颜色格式不正确"
                return false
            }
        } else if (this.varType == "rgba") {
            val rgbaPattern = UTSRegExp("^rgba\\(\\s*\\d+\\s*,\\s*\\d+\\s*,\\s*\\d+\\s*,\\s*[\\d.]+\\s*\\)\$", "")
            if (!rgbaPattern.test(colorValue)) {
                this.showError = true
                this.errorMessage = "颜色格式不正确"
                return false
            }
        }
        this.showError = false
        this.errorMessage = ""
        return true
    }
    open var change = ::gen_change_fn
    open fun gen_change_fn(event: FormChangeEvent): Unit {
        this.fieldValue = event.value as String
        this.updateDisplayColor()
        this.setCache()
        this.`$emit`("change", event)
    }
    open var openColorPicker = ::gen_openColorPicker_fn
    open fun gen_openColorPicker_fn(): Unit {
        val colorPicker = this.`$refs`["colorPicker"] as ComponentPublicInstance
        colorPicker.`$callMethod`("open")
    }
    open var onColorConfirm = ::gen_onColorConfirm_fn
    open fun gen_onColorConfirm_fn(colorData: UTSJSONObject): Unit {
        var selectedColor: String
        if (this.varType == "rgba") {
            selectedColor = colorData.getString("color") ?: "rgba(0, 0, 0, 1)"
        } else {
            selectedColor = colorData.getString("hex") ?: "#000000"
        }
        val result = FormChangeEvent(index = this.index, value = selectedColor)
        this.change(result)
    }
    open var onColorCancel = ::gen_onColorCancel_fn
    open fun gen_onColorCancel_fn(): Unit {}
    companion object {
        var name = "FormColor"
        val styles: Map<String, Map<String, Map<String, Any>>> by lazy {
            _nCS(_uA(
                styles0
            ))
        }
        val styles0: Map<String, Map<String, Map<String, Any>>>
            get() {
                return _uM("color-display-container" to _pS(_uM("flex" to 1, "display" to "flex", "flexDirection" to "row", "alignItems" to "center", "minHeight" to "60rpx", "paddingTop" to "10rpx", "paddingRight" to "10rpx", "paddingBottom" to "10rpx", "paddingLeft" to "10rpx", "borderTopLeftRadius" to "10rpx", "borderTopRightRadius" to "10rpx", "borderBottomRightRadius" to "10rpx", "borderBottomLeftRadius" to "10rpx", "backgroundColor" to "rgba(255,255,255,0.8)")), "color-preview" to _pS(_uM("width" to "60rpx", "height" to "40rpx", "borderTopLeftRadius" to "8rpx", "borderTopRightRadius" to "8rpx", "borderBottomRightRadius" to "8rpx", "borderBottomLeftRadius" to "8rpx", "borderTopWidth" to "1rpx", "borderRightWidth" to "1rpx", "borderBottomWidth" to "1rpx", "borderLeftWidth" to "1rpx", "borderTopStyle" to "solid", "borderRightStyle" to "solid", "borderBottomStyle" to "solid", "borderLeftStyle" to "solid", "borderTopColor" to "#e5e5e5", "borderRightColor" to "#e5e5e5", "borderBottomColor" to "#e5e5e5", "borderLeftColor" to "#e5e5e5", "marginRight" to "20rpx", "boxShadow" to "0 2rpx 4rpx rgba(0, 0, 0, 0.1)")), "color-text" to _pS(_uM("flex" to 1, "fontSize" to "28rpx", "color" to "#333333", "fontFamily" to "monospace")))
            }
        var inheritAttrs = true
        var inject: Map<String, Map<String, Any?>> = _uM()
        var emits: Map<String, Any?> = _uM("change" to null)
        var props = _nP(_uM("data" to _uM(), "index" to _uM("type" to "Number", "default" to 0), "keyName" to _uM("type" to "String", "default" to ""), "labelColor" to _uM("type" to "String", "default" to "#000"), "backgroundColor" to _uM("type" to "String", "default" to "#f1f4f9")))
        var propsNeedCastKeys = _uA(
            "index",
            "keyName",
            "labelColor",
            "backgroundColor"
        )
        var components: Map<String, CreateVueComponent> = _uM("FormContainer" to GenComponentsMainFormComponentsFormContainerClass, "MainColorPicker" to GenComponentsMainFormToolsMainColorPickerClass)
    }
}
