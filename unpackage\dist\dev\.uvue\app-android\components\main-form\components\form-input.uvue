import { FormFieldData, FormChangeEvent } from '@/components/main-form/form_type.uts';
import FormContainer from './form-container.uvue';
const __sfc__ = defineComponent({
    name: "FormInput",
    components: {
        FormContainer
    },
    props: {
        data: {
            type: Object as PropType<FormFieldData>
        },
        index: {
            type: Number,
            default: 0
        },
        keyName: {
            type: String,
            default: ""
        },
        labelColor: {
            type: String,
            default: "#000"
        },
        backgroundColor: {
            type: String,
            default: "#f1f4f9"
        }
    },
    data() {
        return {
            fieldName: "",
            fieldValue: "",
            isSave: false,
            save_key: "",
            tip: "",
            placeholder: "",
            inputmode: "text",
            minLength: 0,
            maxLength: -1,
            showError: false,
            errorMessage: ""
        };
    },
    computed: {},
    watch: {
        data: {
            handler(obj: FormFieldData) {
                // 只处理value的变化，当外部传入的value与当前fieldValue不同时，才更新fieldValue
                // 这避免了用户输入时的循环更新问题
                const newValue = obj.value as string;
                if (newValue !== this.fieldValue) {
                    this.fieldValue = newValue;
                }
            },
            deep: true
        }
    },
    created(): void {
        // 初始化时调用一次即可
        const fieldObj = this.$props["data"] as FormFieldData;
        this.initFieldData(fieldObj);
    },
    methods: {
        // 初始化字段数据（仅在首次加载时调用）
        initFieldData(fieldObj: FormFieldData): void {
            const fieldKey = fieldObj.key;
            const fieldValue = fieldObj.value as string;
            // 设置基本信息
            this.fieldName = fieldObj.name;
            this.fieldValue = fieldValue;
            this.isSave = fieldObj.isSave ?? false;
            this.save_key = this.keyName + "_" + fieldKey;
            // 解析配置信息
            const extalJson = fieldObj.extra as UTSJSONObject;
            this.minLength = extalJson.getNumber("minLength") ?? 0;
            this.maxLength = extalJson.getNumber("maxLength") ?? -1;
            this.placeholder = extalJson.getString("placeholder") ?? "";
            this.inputmode = extalJson.getString("inputmode") ?? "text";
            this.tip = extalJson.getString("tip") ?? "";
            // 获取缓存
            this.getCache();
        },
        getCache(): void {
            if (this.isSave) {
                const that = this;
                uni.getStorage({
                    key: this.save_key,
                    success: (res: GetStorageSuccess) => {
                        const save_value = res.data as string;
                        that.fieldValue = save_value;
                        const result: FormChangeEvent = {
                            index: this.index,
                            value: save_value
                        };
                        this.change(result);
                    }
                });
            }
        },
        setCache(): void {
            if (this.isSave) {
                uni.setStorage({
                    key: this.save_key,
                    data: this.fieldValue
                });
            }
        },
        validate(): boolean {
            // 如果没有设置最大长度限制，只检查最小长度
            if (this.maxLength == -1) {
                if (this.minLength > 0 && this.fieldValue.length < this.minLength) {
                    this.showError = true;
                    this.errorMessage = `输入内容不能少于${this.minLength}个字符`;
                    return false;
                }
                this.showError = false;
                this.errorMessage = "";
                return true;
            }
            // 检查最小长度
            if (this.fieldValue.length < this.minLength) {
                this.showError = true;
                this.errorMessage = `输入内容不能少于${this.minLength}个字符`;
                return false;
            }
            // 检查最大长度
            if (this.maxLength > 0 && this.fieldValue.length > this.maxLength) {
                this.showError = true;
                this.errorMessage = `输入内容不能超过${this.maxLength}个字符`;
                return false;
            }
            this.showError = false;
            this.errorMessage = "";
            return true;
        },
        change(event: FormChangeEvent): void {
            // 更新字段值
            this.fieldValue = event.value as string;
            // 保存缓存
            this.setCache();
            // 触发父组件事件
            this.$emit('change', event);
        },
        onInput(event: UniInputEvent): void {
            const result: FormChangeEvent = {
                index: this.index,
                value: event.detail.value
            };
            this.change(result);
        },
        onBlur(): void {
            // 在失去焦点时进行验证
            this.validate();
        }
    }
});
export default __sfc__;
function GenComponentsMainFormComponentsFormInputRender(this: InstanceType<typeof __sfc__>): any | null {
    const _ctx = this;
    const _cache = this.$.renderCache;
    const _component_form_container = resolveComponent("form-container");
    return _cV(_component_form_container, _uM({
        label: _ctx.fieldName,
        "show-error": _ctx.showError,
        tip: _ctx.tip,
        "error-message": _ctx.errorMessage,
        "label-color": _ctx.labelColor,
        "background-color": _ctx.backgroundColor
    }), _uM({
        "input-content": withSlotCtx((): any[] => [
            _cE("input", _uM({
                class: "form-input-element",
                modelValue: _ctx.fieldValue,
                onInput: [($event: UniInputEvent) => { (_ctx.fieldValue) = $event.detail.value; }, _ctx.onInput],
                type: _ctx.inputmode,
                maxlength: _ctx.maxLength,
                placeholder: _ctx.placeholder,
                onBlur: _ctx.onBlur
            }), null, 40 /* PROPS, NEED_HYDRATION */, ["modelValue", "onInput", "type", "maxlength", "placeholder", "onBlur"])
        ]),
        _: 1 /* STABLE */
    }), 8 /* PROPS */, ["label", "show-error", "tip", "error-message", "label-color", "background-color"]);
}
const GenComponentsMainFormComponentsFormInputStyles = [_uM([["form-input-element", _pS(_uM([["flex", 1], ["minHeight", "60rpx"]]))]])];
//# sourceMappingURL=form-input.uvue.map