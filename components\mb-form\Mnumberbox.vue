<template>
	<view class="Mnumberbox">
		<view class="Mnumberbox-name" :style="{color:color}">
			{{data.name}}
		</view>

		<view class="Mnumberbox-box qShadow1" :style="{backgroundColor:bgColor}">
			<view class="mb-numberboxbtn qShadow1" @click="numberFun('-')">
				-
			</view>
			<view class="mb-numberboxInput">
				<input v-model="mValue" type="number" @input="change" />
			</view>
			<view class="mb-numberboxbtn qShadow1" @click="numberFun('+')">
				+
			</view>
			<view class="Mnumberbox-unit" v-if="data.unit">{{data.unit}}</view>


		</view>

	</view>



</template>

<script>
	export default {
		name: "Mnumberbox",
		data() {
			return {
				mValue: 0,
				saveKey: ""
			};
		},
		watch: {
			data: {

				handler(newValue, oldValue) {
					this.mValue = this.data.value

				},
				deep: true
			}

		},
		created() {


			if (this.data.isSave) {

				this.saveKey = this.keyName + "_" + this.data.key

				uni.getStorage({
					key: this.saveKey,
					success: (res) => {
						this.mValue = res.data
						this.$emit("change", {
							index: this.index,
							value: res.data
						})
					},
					fail: () => {
						this.mValue = this.data.value
					}
				});


			} else {
				this.mValue = this.data.value
			}

		},
		props: {
			data: {
				type: Object,
				default: {}
			},
			keyName: {
				type: String,
				default: ""

			},
			index: {
				type: Number,
				default: 0
			},
			color: {
				type: String,
				default: '#000'
			},
			bgColor: {
				type: String,
				default: "#f1f4f9"

			}
		},
		methods: {
			change(e) {
				let value = this.data.value
				if (e.detail.value == "") {

					value = this.data.min

				}

				if (Number(e.detail.value) >= this.data.max) {

					value = this.data.max
				} else if (Number(e.detail.value) <= this.data.min) {

					value = this.data.min
				} else {
					value = Number(e.detail.value)
				}

				this.$emit("change", {
					index: this.index,
					value: value
				})
				this.mValue = value



				if (this.data.isSave) {
					try {
						uni.setStorageSync(this.saveKey, value);
					} catch (e) {
						// error
					}

				}
			},
			numberFun(type) {
				let value, num = Number(this.data.value)

				if (type == "+") {



					num = num + this.data.step

				} else {
					num = num - this.data.step
				}


				if (Number(num) >= this.data.max) {

					value = this.data.max

				} else if (Number(num) <= this.data.min) {

					value = this.data.min

				} else {
					if (this.data.step % 1 === 0) {
						value = parseInt(num)
					} else {

						value = num.toFixed(1)
					}

				}



				this.$emit("change", {
					index: this.index,
					value: value
				})
				this.mValue = value


				if (this.data.isSave) {
					try {
						uni.setStorageSync(this.saveKey, value);
					} catch (e) {
						// error
					}

				}



			}
		}
	}
</script>

<style>
	.Mnumberbox {
		width: 100%;
		display: flex;
		flex-direction: column;
		/* 
		align-items: center;
		justify-content: space-between; */
		padding: 0 20rpx;
		margin-bottom: 20rpx;

	}

	.Mnumberbox-name {
		width: 100%;
		font-size: 32rpx;
		margin-bottom: 10rpx;
	}

	.Mnumberbox-box {
		width: 100%;
		height: 100rpx;
		border: 1rpx solid #fff;
		box-sizing: border-box;
		padding: 0 10rpx;
		border-radius: 20rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
	}



	.mb-numberboxbtn {
		width: 80rpx;
		height: 80rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		background-color: #e1e1e1;
		border-radius: 10rpx;

	}

	.mb-numberboxInput {
		flex: 1;
		height: 100%;
		background-color: #f2f4f8;
		margin: 0 20rpx;
	}

	.mb-numberboxInput input {
		width: 100%;
		height: 100%;
		text-align: center;
	}

	.Mnumberbox-unit {
		padding: 0 10rpx;
		height: 80rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		background-color: #333;
		color: #fff;
		border-radius: 10rpx;
		margin-left: 20rpx;
	}
</style>