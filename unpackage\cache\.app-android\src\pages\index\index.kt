@file:Suppress("UNCHECKED_CAST", "USELESS_CAST", "INAPPLICABLE_JVM_NAME", "UNUSED_ANONYMOUS_PARAMETER", "NAME_SHADOWING", "UNNECESSARY_NOT_NULL_ASSERTION")
package uni.UNIC178CB1
import io.dcloud.uniapp.*
import io.dcloud.uniapp.extapi.*
import io.dcloud.uniapp.framework.*
import io.dcloud.uniapp.runtime.*
import io.dcloud.uniapp.vue.*
import io.dcloud.uniapp.vue.shared.*
import io.dcloud.unicloud.*
import io.dcloud.uts.*
import io.dcloud.uts.Map
import io.dcloud.uts.Set
import io.dcloud.uts.UTSAndroid
import io.dcloud.uniapp.extapi.navigateTo as uni_navigateTo
open class GenPagesIndexIndex : BasePage {
    constructor(__ins: ComponentInternalInstance, __renderer: String?) : super(__ins, __renderer) {}
    @Suppress("UNUSED_PARAMETER", "UNUSED_VARIABLE")
    override fun `$render`(): Any? {
        val _ctx = this
        val _cache = this.`$`.renderCache
        val _component_main_color_picker = resolveEasyComponent("main-color-picker", GenComponentsMainColorPickerMainColorPickerClass)
        val _component_mainYearmonthPicker = resolveComponent("mainYearmonthPicker")
        val _component_main_datetime_picker = resolveComponent("main-datetime-picker")
        val _component_main_form = resolveEasyComponent("main-form", GenComponentsMainFormMainFormClass)
        return _cE("scroll-view", _uM("class" to "content"), _uA(
            _cE("button", _uM("onClick" to _ctx.openColorPicker), "选择颜色", 8, _uA(
                "onClick"
            )),
            _cE("button", _uM("onClick" to _ctx.openFun), "对话框", 8, _uA(
                "onClick"
            )),
            _cE("button", _uM("onClick" to _ctx.openDateTimePicker), "测试日期时间选择器", 8, _uA(
                "onClick"
            )),
            _cV(_component_main_color_picker, _uM("ref" to "colorPicker", "onCancel" to _ctx.onCancel, "onConfirm" to _ctx.onConfirm), null, 8, _uA(
                "onCancel",
                "onConfirm"
            )),
            _cV(_component_mainYearmonthPicker, _uM("ref" to "yearmonthPicker"), null, 512),
            _cV(_component_main_datetime_picker, _uM("ref" to "datetimePicker", "mode" to _ctx.dateTimeMode, "title" to _ctx.dateTimeTitle, "showSeconds" to _ctx.showSeconds, "quickOptions" to _ctx.quickOptions, "onCancel" to _ctx.onDateTimeCancel, "onConfirm" to _ctx.onDateTimeConfirm), null, 8, _uA(
                "mode",
                "title",
                "showSeconds",
                "quickOptions",
                "onCancel",
                "onConfirm"
            )),
            _cV(_component_main_form, _uM("formData" to _ctx.formConfig, "title" to "用户信息表单", "keyName" to "user_form", "ref" to "mainForm"), null, 8, _uA(
                "formData"
            )),
            _cE("button", _uM("onClick" to _ctx.viewForm), "查看表单1", 8, _uA(
                "onClick"
            ))
        ))
    }
    open var dateTimeMode: String by `$data`
    open var dateTimeTitle: String by `$data`
    open var showSeconds: Boolean by `$data`
    open var quickOptions: UTSArray<DateQuickOption> by `$data`
    open var formConfig: UTSArray<FormFieldData> by `$data`
    @Suppress("USELESS_CAST")
    override fun data(): Map<String, Any?> {
        return _uM("dateTimeMode" to "datetime" as String, "dateTimeTitle" to "选择日期时间" as String, "showSeconds" to false as Boolean, "quickOptions" to _uA<DateQuickOption>(DateQuickOption(label = "现在", value = Date(), autoConfirm = true), DateQuickOption(label = "明天", value = Date(Date.now() + 86400000), autoConfirm = false), DateQuickOption(label = "下周", value = Date(Date.now() + 604800000), autoConfirm = false)), "formConfig" to _uA<FormFieldData>(FormFieldData(key = "username", name = "用户名1", type = "input", value = "", isSave = true, extra = object : UTSJSONObject() {
            var minLength: Number = 0
            var maxLength: Number = 20
            var placeholder = "请输入用户名"
            var tip = "123"
            var inputmode = "digit"
        }), FormFieldData(key = "password", name = "密码", type = "input", value = "", isSave = false, extra = object : UTSJSONObject() {
            var minLength: Number = 6
            var maxLength: Number = 20
            var placeholder = "请输入密码"
            var tip = "密码请自己保管好"
            var inputmode = "number"
        }), FormFieldData(key = "email", name = "邮箱地址", type = "textarea", value = "", isSave = true, extra = object : UTSJSONObject() {
            var minLength: Number = 6
            var maxLength: Number = 20
            var placeholder = "请输入密码"
            var tip = ""
        }), FormFieldData(key = "enable_feature", name = "启用功能", type = "switch", value = 1, isSave = false, extra = object : UTSJSONObject() {
            var varType = "number"
            var tip = "开启后将启用此功能"
        }), FormFieldData(key = "slider", name = "slider测试", type = "slider", value = 10, isSave = true, extra = object : UTSJSONObject() {
            var min: Number = 0
            var max: Number = 100
            var step: Number = 1
        }), FormFieldData(key = "numberbox", name = "数量选择", type = "numberbox", value = 5, isSave = true, extra = object : UTSJSONObject() {
            var min: Number = 1
            var max: Number = 50
            var step: Number = 1
            var unit = "个"
            var tip = "请选择数量"
        }), FormFieldData(key = "themeColor", name = "主题颜色", type = "color", value = "", isSave = false, extra = object : UTSJSONObject() {
            var varType = "hex"
            var tip = "选择您喜欢的主题颜色"
        }), FormFieldData(key = "backgroundColor", name = "背景颜色", type = "color", value = "rgba(255, 0, 0, 0.8)", isSave = true, extra = object : UTSJSONObject() {
            var varType = "rgba"
            var tip = "选择背景颜色，支持透明度"
        }), FormFieldData(key = "birthYearMonth", name = "出生年月", type = "yearmonth", value = "", isSave = true, extra = object : UTSJSONObject() {
            var tip = "请选择您的出生年月1"
        }), FormFieldData(key = "city", name = "所在城市", type = "select", value = "", isSave = true, extra = object : UTSJSONObject() {
            var varType = "string"
            var placeholder = "请选择城市"
            var tip = "选择您所在的城市"
            var options = _uA(
                object : UTSJSONObject() {
                    var text = "北京"
                    var value = "beijing"
                },
                object : UTSJSONObject() {
                    var text = "上海"
                    var value = "shanghai"
                },
                object : UTSJSONObject() {
                    var text = "广州"
                    var value = "guangzhou"
                },
                object : UTSJSONObject() {
                    var text = "深圳"
                    var value = "shenzhen"
                },
                object : UTSJSONObject() {
                    var text = "杭州"
                    var value = "hangzhou"
                }
            )
        }), FormFieldData(key = "level", name = "用户等级", type = "select", value = 1, isSave = true, extra = object : UTSJSONObject() {
            var varType = "int"
            var placeholder = "请选择等级"
            var tip = "选择您的用户等级"
            var options = _uA(
                object : UTSJSONObject() {
                    var text = "初级用户"
                    var value: Number = 1
                },
                object : UTSJSONObject() {
                    var text = "中级用户"
                    var value: Number = 2
                },
                object : UTSJSONObject() {
                    var text = "高级用户"
                    var value: Number = 3
                },
                object : UTSJSONObject() {
                    var text = "VIP用户"
                    var value: Number = 4
                }
            )
        }), FormFieldData(key = "score", name = "评分", type = "select", value = 4.5, isSave = true, extra = object : UTSJSONObject() {
            var varType = "float"
            var placeholder = "请选择评分"
            var tip = "选择您的评分"
            var options = _uA(
                object : UTSJSONObject() {
                    var text = "1.0分"
                    var value: Number = 1.0
                },
                object : UTSJSONObject() {
                    var text = "2.5分"
                    var value: Number = 2.5
                },
                object : UTSJSONObject() {
                    var text = "3.0分"
                    var value: Number = 3.0
                },
                object : UTSJSONObject() {
                    var text = "4.5分"
                    var value: Number = 4.5
                },
                object : UTSJSONObject() {
                    var text = "5.0分"
                    var value: Number = 5.0
                }
            )
        })))
    }
    open var viewForm = ::gen_viewForm_fn
    open fun gen_viewForm_fn() {
        uni_navigateTo(NavigateToOptions(url = "/pages/calendar-test/calendar-test"))
    }
    open var openFun = ::gen_openFun_fn
    open fun gen_openFun_fn() {
        (this.`$refs`["yearmonthPicker"] as ComponentPublicInstance).`$callMethod`("open")
    }
    open var openColorPicker = ::gen_openColorPicker_fn
    open fun gen_openColorPicker_fn() {
        (this.`$refs`["colorPicker"] as ComponentPublicInstance).`$callMethod`("open")
    }
    open var onCancel = ::gen_onCancel_fn
    open fun gen_onCancel_fn() {
        console.log("用户取消选择", " at pages/index/index.uvue:265")
    }
    open var onConfirm = ::gen_onConfirm_fn
    open fun gen_onConfirm_fn(result: UTSJSONObject) {
        console.log(result, " at pages/index/index.uvue:269")
        console.log("选择的颜色:", result["color"], " at pages/index/index.uvue:270")
        console.log("RGBA值:", result["rgba"], " at pages/index/index.uvue:271")
    }
    open var openDateTimePicker = ::gen_openDateTimePicker_fn
    open fun gen_openDateTimePicker_fn() {
        (this.`$refs`["datetimePicker"] as ComponentPublicInstance).`$callMethod`("show")
    }
    open var onDateTimeCancel = ::gen_onDateTimeCancel_fn
    open fun gen_onDateTimeCancel_fn() {
        console.log("用户取消选择日期时间", " at pages/index/index.uvue:281")
    }
    open var onDateTimeConfirm = ::gen_onDateTimeConfirm_fn
    open fun gen_onDateTimeConfirm_fn(result: UTSJSONObject) {
        console.log("选择的日期时间:", result, " at pages/index/index.uvue:286")
        console.log("格式化后的值:", result["formatted"], " at pages/index/index.uvue:287")
        console.log("Date对象:", result["value"], " at pages/index/index.uvue:288")
    }
    companion object {
        val styles: Map<String, Map<String, Map<String, Any>>> by lazy {
            _nCS(_uA(
                styles0
            ), _uA(
                GenApp.styles
            ))
        }
        val styles0: Map<String, Map<String, Map<String, Any>>>
            get() {
                return _uM("content" to _pS(_uM("height" to "100%", "paddingTop" to "20rpx", "paddingRight" to "20rpx", "paddingBottom" to "20rpx", "paddingLeft" to "20rpx")))
            }
        var inheritAttrs = true
        var inject: Map<String, Map<String, Any?>> = _uM()
        var emits: Map<String, Any?> = _uM()
        var props = _nP(_uM())
        var propsNeedCastKeys: UTSArray<String> = _uA()
        var components: Map<String, CreateVueComponent> = _uM("mainYearmonthPicker" to GenComponentsMainFormToolsMainYearmonthPickerClass, "mainDatetimePicker" to GenComponentsMainFormToolsMainDatetimePickerClass)
    }
}
