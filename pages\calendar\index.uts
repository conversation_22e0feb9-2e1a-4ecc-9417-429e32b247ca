import { Lunar, LunarInfoType } from './calendar.uts'

export type DateType = {
	fullDate : string;
	year : number;
	month : number;
	date : number;
	day : number;
	disabled : boolean;
	lunar : string;
	is_today : boolean;
	data ?: LunarInfoType
}

export class Calendar {
	private lunar:Lunar
	constructor() {
		this.lunar =new Lunar()
	}

	getDateInfo(time : string = '') : DateType {
		const nowDate = this.getDate(time)
		const lunar = this.getlunar(nowDate.year, nowDate.month, nowDate.date)
		const item : DateType = nowDate
		item.data = lunar
		return item
	}

	/**
	 * 获取每周数据
	 * @param {Object} dateData
	 */
	getWeeks(dateData : string = '') : Array<Array<DateType>> {
		const dateObj = this.getDate(dateData)
		const year = dateObj.year
		const month = dateObj.month
		let firstDay = new Date(year, month - 1, 0).getDay()
		// 获取本月天数
		let currentDay = new Date(year, month, 0).getDate() 
		// 上个月末尾几天
		const lastMonthDays = this._getLastMonthDays(firstDay, dateObj)
		// 本月天数
		const currentMonthDys = this._currentMonthDys(currentDay, dateObj)
		// 本月剩余天数
		const surplus = 42 - (lastMonthDays.length + currentMonthDys.length)
		// 下个月开始几天
		const nextMonthDays = this._getNextMonthDays(surplus, dateObj)
		// const weeks = []

		// 本月所有日期格子合并
		let days : Array<DateType> = []
		for (let i = 0; i < lastMonthDays.length; i++) {
			const item = lastMonthDays[i]
			days.push(item)
		}
		for (let i = 0; i < currentMonthDys.length; i++) {
			const item = currentMonthDys[i]
			days.push(item)
		}
		for (let i = 0; i < nextMonthDays.length; i++) {
			const item = nextMonthDays[i]
			days.push(item)
		}
		let weeks : Array<Array<DateType>> = []
		// 拼接数组  上个月开始几天 + 本月天数+ 下个月开始几天
		for (let i = 0; i < days.length; i += 7) {
			const item : Array<DateType> = days.slice(i, i + 7)
			weeks.push(item);
		}
		return weeks
	}

	/**
	 * 获取上月剩余天数
	 */
	_getLastMonthDays(firstDay : number, full : DateType) : Array<DateType> {
		let dateArr : Array<DateType> = []
		for (let i = firstDay; i > 0; i--) {
			const month = full.month - 1
			const beforeDate = new Date(full.year, month, -i + 1).getDate()
			let nowDate = full.year + '-' + month + '-' + beforeDate

			let item : DateType = this.getDate(nowDate)
			item.disabled = true

			dateArr.push(item)
		}
		return dateArr
	}

	/**
	 * 获取本月天数
	 */
	_currentMonthDys(dateData : number, full : DateType) : Array<DateType> {

		let dateArr : Array<DateType> = []
		for (let i = 1; i <= dateData; i++) {
			let nowDate = full.year + '-' + full.month + '-' + i
			let item : DateType = this.getDate(nowDate)
			item.disabled = false

			dateArr.push(item)
		}
		return dateArr
	}

	/**
	 * 获取下月天数
	 */
	_getNextMonthDays(surplus : number, full : DateType) : Array<DateType> {
		let dateArr : Array<DateType> = []
		for (let i = 1; i < surplus + 1; i++) {
			const month = full.month + 1
			let nowDate = full.year + '-' + month + '-' + i
			let item : DateType = this.getDate(nowDate)
			item.disabled = true

			dateArr.push(item)
		}
		return dateArr
	}

	/**
	 * 计算阴历日期显示
	 */
	getlunar(year : number, month : number, date : number) : LunarInfoType {
		return this.lunar.solar2lunar(year, month, date)
	}
	

	/**
	 * 获取任意时间
	 */
	getDate(date : string = '', AddDayCount : number = 0, str : string = 'day') : DateType {
		let dd : Date = new Date()
		if (date !== '') {
			const datePart = date.split(" ");
			const dateData = datePart[0].split("-");
			const year = parseInt(dateData[0])
			const month = parseInt(dateData[1])
			const day = parseInt(dateData[2]) 

			dd = new Date(year, month - 1, day)
		}

		switch (str) {
			case 'day':
				dd.setDate(dd.getDate() + AddDayCount);
				break;
			case 'month':
				dd.setMonth(dd.getMonth() + AddDayCount);
				break;
			case 'year':
				dd.setFullYear(dd.getFullYear() + AddDayCount);
				break;
		}

		const y = dd.getFullYear();
		const m = dd.getMonth() + 1;
		const d = dd.getDate();

		let nowDate = y + '-' + m + '-' + d
		const lunarData = this.getlunar(y, m, d)

		const dataObj : DateType = {
			fullDate: nowDate,
			year: y,
			month: m,
			date: d,
			day: dd.getDay() + 1,
			lunar: lunarData.IDayCn,
			is_today: lunarData.isToday,
			disabled: false
		}

		return dataObj
	}

}