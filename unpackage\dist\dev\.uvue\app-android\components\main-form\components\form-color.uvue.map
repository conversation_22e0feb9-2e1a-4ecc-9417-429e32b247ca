{"version": 3, "sources": ["components/main-form/components/form-color.uvue"], "names": [], "mappings": "AAgBC,OAAO,EAAE,aAAa,EAAE,eAAc,EAAE,MAAO,sCAAqC,CAAA;AACpF,OAAO,aAAY,MAAO,uBAAsB,CAAA;AAChD,OAAO,eAAc,MAAO,mCAAkC,CAAA;AAE9D,MAAK,OAAQ,GAAE,eAAA,CAAA;IACd,IAAI,EAAE,WAAW;IACjB,KAAK,EAAE,CAAC,QAAQ,CAAC;IACjB,UAAU,EAAE;QACX,aAAa;QACb,eAAc;KACd;IACD,KAAK,EAAE;QACN,IAAI,EAAE;YACL,IAAI,EAAE,IAAG,IAAK,GAAE,IAAK,QAAQ,CAAC,aAAa,CAAA;SAC3C;QACD,KAAK,EAAE;YACN,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,CAAA;SACT;QACD,OAAO,EAAE;YACR,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,EAAC;SACV;QACD,UAAU,EAAE;YACX,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,MAAK;SACd;QACD,eAAe,EAAE;YAChB,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,SAAQ;SAClB;KACA;IACD,IAAI;QACH,OAAO;YACN,SAAS,EAAE,EAAE;YACb,UAAU,EAAE,EAAC,IAAK,MAAM;YACxB,MAAM,EAAE,KAAK;YACb,QAAQ,EAAE,EAAE;YACZ,GAAG,EAAE,EAAE;YACP,OAAO,EAAE,KAAK;YACd,YAAY,EAAE,SAAS;YACvB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,EAAC;SAChB,CAAA;IACD,CAAC;IACD,QAAQ,EAAE,EAET;IACD,KAAK,EAAE;QACN,IAAI,EAAE;YACL,OAAO,CAAC,GAAG,EAAE,aAAa;gBACzB,wDAAuD;gBACvD,mBAAkB;gBAClB,MAAM,QAAO,GAAI,GAAG,CAAC,KAAI,IAAK,MAAK,CAAA;gBACnC,IAAI,QAAO,KAAM,IAAI,CAAC,UAAU,EAAE;oBACjC,IAAI,CAAC,UAAS,GAAI,QAAO,CAAA;oBACzB,IAAI,CAAC,kBAAkB,EAAC,CAAA;iBACzB;YACD,CAAC;YACD,IAAI,EAAE,IAAG;SACV;KACA;IACD,OAAO,IAAI,IAAG;QACb,aAAY;QACZ,MAAM,QAAO,GAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAA,IAAK,aAAY,CAAA;QACpD,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAA,CAAA;IAC5B,CAAC;IACD,OAAO,EAAE;QACR,qBAAoB;QACpB,aAAa,CAAC,QAAQ,EAAE,aAAa,GAAG,IAAG;YAC1C,MAAM,QAAO,GAAI,QAAQ,CAAC,GAAE,CAAA;YAC5B,MAAM,UAAS,GAAI,QAAQ,CAAC,KAAI,IAAK,MAAK,CAAA;YAE1C,SAAQ;YACR,IAAI,CAAC,SAAQ,GAAI,QAAQ,CAAC,IAAG,CAAA;YAC7B,IAAI,CAAC,UAAS,GAAI,UAAS,CAAA;YAC3B,IAAI,CAAC,MAAK,GAAI,QAAQ,CAAC,MAAK,IAAK,KAAI,CAAA;YACrC,IAAI,CAAC,QAAO,GAAI,IAAI,CAAC,OAAM,GAAI,GAAE,GAAI,QAAO,CAAA;YAE5C,SAAQ;YACR,MAAM,SAAQ,GAAI,QAAQ,CAAC,KAAI,IAAK,aAAY,CAAA;YAChD,IAAI,CAAC,GAAE,GAAI,SAAS,CAAC,SAAS,CAAC,KAAK,CAAA,IAAK,EAAC,CAAA;YAC1C,IAAI,CAAC,OAAM,GAAI,SAAS,CAAC,SAAS,CAAC,SAAS,CAAA,IAAK,KAAI,CAAA;YAErD,SAAQ;YACR,IAAI,CAAC,kBAAkB,EAAC,CAAA;YAExB,OAAM;YACN,IAAI,CAAC,QAAQ,EAAC,CAAA;QACf,CAAC;QAED,SAAQ;QACR,kBAAkB,IAAI,IAAG;YACxB,IAAI,IAAI,CAAC,UAAS,IAAK,EAAE,EAAE;gBAC1B,IAAI,CAAC,YAAW,GAAI,IAAI,CAAC,UAAS,IAAK,MAAK,CAAA;aAC7C;iBAAO;gBACN,kBAAiB;gBACjB,IAAI,IAAI,CAAC,OAAM,IAAK,MAAM,EAAE;oBAC3B,IAAI,CAAC,YAAW,GAAI,kBAAiB,CAAA;oBACrC,IAAI,CAAC,UAAS,GAAI,kBAAiB,CAAA;iBACpC;qBAAO;oBACN,IAAI,CAAC,YAAW,GAAI,SAAQ,CAAA;oBAC5B,IAAI,CAAC,UAAS,GAAI,SAAQ,CAAA;iBAC3B;aACD;QACD,CAAC;QAED,QAAQ,IAAI,IAAG;YACd,IAAI,IAAI,CAAC,MAAM,EAAE;gBAChB,MAAM,IAAG,GAAI,IAAG,CAAA;gBAChB,GAAG,CAAC,UAAU,CAAC;oBACd,GAAG,EAAE,IAAI,CAAC,QAAQ;oBAClB,OAAO,EAAE,CAAC,GAAG,EAAE,iBAAiB,EAAE,EAAC;wBAClC,MAAM,UAAS,GAAI,GAAG,CAAC,IAAG,CAAA;wBAC1B,IAAG,OAAO,UAAS,KAAM,QAAQ,EAAC;4BACjC,IAAI,CAAC,UAAS,GAAI,UAAS,IAAK,MAAK,CAAA;4BACrC,IAAI,CAAC,kBAAkB,EAAC,CAAA;4BACxB,MAAM,MAAM,EAAE,eAAc,GAAI;gCAC/B,KAAK,EAAE,IAAI,CAAC,KAAK;gCACjB,KAAK,EAAE,UAAS;6BACjB,CAAA;4BACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAA,CAAA;yBACnB;oBAED,CAAA;iBACA,CAAA,CAAA;aACF;QACD,CAAC;QAED,QAAQ,IAAI,IAAG;YACd,IAAI,IAAI,CAAC,MAAK,IAAK,OAAO,IAAI,CAAC,UAAS,KAAK,QAAQ,EAAE;gBACtD,GAAG,CAAC,UAAU,CAAC;oBACd,GAAG,EAAE,IAAI,CAAC,QAAQ;oBAClB,IAAI,EAAE,IAAI,CAAC,UAAS;iBACpB,CAAA,CAAA;aACF;QACD,CAAC;QAED,QAAQ,IAAI,OAAM;YACjB,QAAO;YACP,IAAI,IAAI,CAAC,UAAS,IAAK,EAAE,EAAE;gBAC1B,IAAI,CAAC,SAAQ,GAAI,IAAG,CAAA;gBACpB,IAAI,CAAC,YAAW,GAAI,OAAM,CAAA;gBAC1B,OAAO,KAAI,CAAA;aACZ;YAEA,kBAAiB;YACjB,MAAM,UAAS,GAAI,IAAI,CAAC,UAAS,IAAK,MAAK,CAAA;YAC3C,IAAI,IAAI,CAAC,OAAM,IAAK,KAAK,EAAE;gBAC1B,MAAM,UAAS,GAAI,oCAAmC,CAAA;gBACtD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;oBACjC,IAAI,CAAC,SAAQ,GAAI,IAAG,CAAA;oBACpB,IAAI,CAAC,YAAW,GAAI,SAAQ,CAAA;oBAC5B,OAAO,KAAI,CAAA;iBACZ;aACD;iBAAO,IAAI,IAAI,CAAC,OAAM,IAAK,MAAM,EAAE;gBAClC,MAAM,WAAU,GAAI,sDAAqD,CAAA;gBACzE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;oBAClC,IAAI,CAAC,SAAQ,GAAI,IAAG,CAAA;oBACpB,IAAI,CAAC,YAAW,GAAI,SAAQ,CAAA;oBAC5B,OAAO,KAAI,CAAA;iBACZ;aACD;YAEA,IAAI,CAAC,SAAQ,GAAI,KAAI,CAAA;YACrB,IAAI,CAAC,YAAW,GAAI,EAAC,CAAA;YACrB,OAAO,IAAG,CAAA;QACX,CAAC;QAED,MAAM,CAAC,KAAK,EAAE,eAAe,GAAG,IAAG;YAClC,QAAO;YACP,IAAI,CAAC,UAAS,GAAI,KAAK,CAAC,KAAI,IAAK,MAAK,CAAA;YACtC,SAAQ;YACR,IAAI,CAAC,kBAAkB,EAAC,CAAA;YACxB,OAAM;YACN,IAAI,CAAC,QAAQ,EAAC,CAAA;YACd,UAAS;YACT,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAA,CAAA;QAC3B,CAAC;QAED,UAAS;QACT,eAAe,IAAI,IAAG;YACrB,MAAM,WAAU,GAAI,IAAI,CAAC,KAAK,CAAC,aAAa,CAAA,IAAK,uBAAsB,CAAA;YACvE,WAAW,CAAC,WAAW,CAAC,MAAM,CAAA,CAAA;QAC/B,CAAC;QAED,SAAQ;QACR,cAAc,CAAC,SAAS,EAAE,aAAa,GAAG,IAAG;YAC5C,IAAI,aAAa,EAAE,MAAK,CAAA;YAExB,IAAI,IAAI,CAAC,OAAM,IAAK,MAAM,EAAE;gBAC3B,WAAU;gBACV,aAAY,GAAI,SAAS,CAAC,SAAS,CAAC,OAAO,CAAA,IAAK,kBAAiB,CAAA;aAClE;iBAAO;gBACN,UAAS;gBACT,aAAY,GAAI,SAAS,CAAC,SAAS,CAAC,KAAK,CAAA,IAAK,SAAQ,CAAA;aACvD;YAEA,MAAM,MAAM,EAAE,eAAc,GAAI;gBAC/B,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,KAAK,EAAE,aAAY;aACpB,CAAA;YACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAA,CAAA;QACnB,CAAC;QAED,SAAQ;QACR,aAAa,IAAI,IAAG;YACnB,cAAa;QACd,CAAA;KACD;CACD,CAAA,CAAA;;;;;;;;QAjOA,GAAA,CAQiB,yBAAA,EAAA,GAAA,CAAA;YARA,KAAK,EAAE,IAAA,CAAA,SAAS;YAAG,YAAU,EAAE,IAAA,CAAA,SAAS;YAAG,GAAG,EAAE,IAAA,CAAA,GAAG;YAAG,eAAa,EAAE,IAAA,CAAA,YAAY;YAAG,aAAW,EAAE,IAAA,CAAA,UAAU;YAC1H,kBAAgB,EAAE,IAAA,CAAA,eAAe;;YACvB,eAAa,EAAA,WAAA,CACvB,IAGO,GAAA,EAAA,CAAA,EAAA,CAAA;gBAHP,GAAA,CAGO,MAAA,EAAA,GAAA,CAAA;oBAHD,KAAK,EAAC,yBAAyB;oBAAE,OAAK,EAAE,IAAA,CAAA,eAAe;;oBAC5D,GAAA,CAA8E,MAAA,EAAA,GAAA,CAAA;wBAAxE,KAAK,EAAC,eAAe;wBAAE,KAAK,EAAA,GAAA,CAAE,GAAA,CAAA,EAAA,eAAA,EAAA,IAAA,CAAA,YAAA,EAAA,CAAiC,CAAA;;oBACrE,GAAA,CAAkD,MAAA,EAAA,GAAA,CAAA,EAA5C,KAAK,EAAC,YAAY,EAAA,CAAA,EAAA,GAAA,CAAI,IAAA,CAAA,YAAY,CAAA,EAAA,CAAA,CAAA,UAAA,CAAA;;;;;QAM3C,GAAA,CAA2G,4BAAA,EAAA,GAAA,CAAA;YAAxF,GAAG,EAAC,aAAa;YAAE,SAAO,EAAE,IAAA,CAAA,cAAc;YAAG,QAAM,EAAE,IAAA,CAAA,aAAa", "file": "components/main-form/components/form-color.uvue", "sourcesContent": ["<template>\n\t<form-container :label=\"fieldName\" :show-error=\"showError\" :tip=\"tip\" :error-message=\"errorMessage\" :label-color=\"labelColor\"\n\t\t:background-color=\"backgroundColor\">\n\t\t<template #input-content>\n\t\t\t<view class=\"color-display-container\" @click=\"openColorPicker\">\n\t\t\t\t<view class=\"color-preview\" :style=\"{ backgroundColor: displayColor }\"></view>\n\t\t\t\t<text class=\"color-text\">{{ displayColor }}</text>\n\t\t\t</view>\n\t\t</template>\n\t</form-container>\n\n\t<!-- 颜色选择器 -->\n\t<main-color-picker ref=\"colorPicker\" @confirm=\"onColorConfirm\" @cancel=\"onColorCancel\"></main-color-picker>\n</template>\n\n<script lang=\"uts\">\n\timport { FormFieldData, FormChangeEvent } from '@/components/main-form/form_type.uts'\n\timport FormContainer from './form-container.uvue'\n\timport MainColorPicker from './../tools/main-color-picker.uvue'\n\n\texport default {\n\t\tname: \"FormColor\",\n\t\temits: ['change'],\n\t\tcomponents: {\n\t\t\tFormContainer,\n\t\t\tMainColorPicker\n\t\t},\n\t\tprops: {\n\t\t\tdata: {\n\t\t\t\ttype: null as any as PropType<FormFieldData>\n\t\t\t},\n\t\t\tindex: {\n\t\t\t\ttype: Number,\n\t\t\t\tdefault: 0\n\t\t\t},\n\t\t\tkeyName: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"\"\n\t\t\t},\n\t\t\tlabelColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#000\"\n\t\t\t},\n\t\t\tbackgroundColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#f1f4f9\"\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tfieldName: \"\",\n\t\t\t\tfieldValue: \"\" as string,\n\t\t\t\tisSave: false,\n\t\t\t\tsave_key: \"\",\n\t\t\t\ttip: \"\",\n\t\t\t\tvarType: \"hex\",\n\t\t\t\tdisplayColor: \"#000000\",\n\t\t\t\tshowError: false,\n\t\t\t\terrorMessage: \"\"\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\n\t\t},\n\t\twatch: {\n\t\t\tdata: {\n\t\t\t\thandler(obj: FormFieldData) {\n\t\t\t\t\t// 只处理value的变化，当外部传入的value与当前fieldValue不同时，才更新fieldValue\n\t\t\t\t\t// 这避免了用户输入时的循环更新问题\n\t\t\t\t\tconst newValue = obj.value as string \n\t\t\t\t\tif (newValue !== this.fieldValue) {\n\t\t\t\t\t\tthis.fieldValue = newValue\n\t\t\t\t\t\tthis.updateDisplayColor()\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tdeep: true\n\t\t\t}\n\t\t},\n\t\tcreated(): void {\n\t\t\t// 初始化时调用一次即可\n\t\t\tconst fieldObj = this.$props[\"data\"] as FormFieldData\n\t\t\tthis.initFieldData(fieldObj)\n\t\t},\n\t\tmethods: {\n\t\t\t// 初始化字段数据（仅在首次加载时调用）\n\t\t\tinitFieldData(fieldObj: FormFieldData): void {\n\t\t\t\tconst fieldKey = fieldObj.key\n\t\t\t\tconst fieldValue = fieldObj.value as string \n\n\t\t\t\t// 设置基本信息\n\t\t\t\tthis.fieldName = fieldObj.name\n\t\t\t\tthis.fieldValue = fieldValue\n\t\t\t\tthis.isSave = fieldObj.isSave ?? false\n\t\t\t\tthis.save_key = this.keyName + \"_\" + fieldKey\n\n\t\t\t\t// 解析配置信息\n\t\t\t\tconst extalJson = fieldObj.extra as UTSJSONObject\n\t\t\t\tthis.tip = extalJson.getString(\"tip\") ?? \"\"\n\t\t\t\tthis.varType = extalJson.getString(\"varType\") ?? \"hex\"\n\n\t\t\t\t// 更新显示颜色\n\t\t\t\tthis.updateDisplayColor()\n\n\t\t\t\t// 获取缓存\n\t\t\t\tthis.getCache()\n\t\t\t},\n\n\t\t\t// 更新显示颜色\n\t\t\tupdateDisplayColor(): void {\n\t\t\t\tif (this.fieldValue != \"\") {\n\t\t\t\t\tthis.displayColor = this.fieldValue as string\n\t\t\t\t} else {\n\t\t\t\t\t// 根据varType设置默认颜色\n\t\t\t\t\tif (this.varType == \"rgba\") {\n\t\t\t\t\t\tthis.displayColor = \"rgba(0, 0, 0, 1)\"\n\t\t\t\t\t\tthis.fieldValue = \"rgba(0, 0, 0, 1)\"\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.displayColor = \"#000000\"\n\t\t\t\t\t\tthis.fieldValue = \"#000000\"\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tgetCache(): void {\n\t\t\t\tif (this.isSave) {\n\t\t\t\t\tconst that = this\n\t\t\t\t\tuni.getStorage({\n\t\t\t\t\t\tkey: this.save_key,\n\t\t\t\t\t\tsuccess: (res: GetStorageSuccess) => {\n\t\t\t\t\t\t\tconst save_value = res.data\r\n\t\t\t\t\t\t\tif(typeof save_value === 'string'){\r\n\t\t\t\t\t\t\t\tthat.fieldValue = save_value as string\r\n\t\t\t\t\t\t\t\tthat.updateDisplayColor()\r\n\t\t\t\t\t\t\t\tconst result: FormChangeEvent = {\r\n\t\t\t\t\t\t\t\t\tindex: this.index,\r\n\t\t\t\t\t\t\t\t\tvalue: save_value\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tthis.change(result)\r\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tsetCache(): void { \n\t\t\t\tif (this.isSave && typeof this.fieldValue ===\"string\") {\n\t\t\t\t\tuni.setStorage({\n\t\t\t\t\t\tkey: this.save_key,\n\t\t\t\t\t\tdata: this.fieldValue\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tvalidate(): boolean {\n\t\t\t\t// 颜色值验证\n\t\t\t\tif (this.fieldValue == \"\") {\n\t\t\t\t\tthis.showError = true\n\t\t\t\t\tthis.errorMessage = \"请选择颜色\"\n\t\t\t\t\treturn false\n\t\t\t\t}\n\n\t\t\t\t// 根据varType验证颜色格式\n\t\t\t\tconst colorValue = this.fieldValue as string\n\t\t\t\tif (this.varType == \"hex\") {\n\t\t\t\t\tconst hexPattern = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/\n\t\t\t\t\tif (!hexPattern.test(colorValue)) {\n\t\t\t\t\t\tthis.showError = true\n\t\t\t\t\t\tthis.errorMessage = \"颜色格式不正确\"\n\t\t\t\t\t\treturn false\n\t\t\t\t\t}\n\t\t\t\t} else if (this.varType == \"rgba\") {\n\t\t\t\t\tconst rgbaPattern = /^rgba\\(\\s*\\d+\\s*,\\s*\\d+\\s*,\\s*\\d+\\s*,\\s*[\\d.]+\\s*\\)$/\n\t\t\t\t\tif (!rgbaPattern.test(colorValue)) {\n\t\t\t\t\t\tthis.showError = true\n\t\t\t\t\t\tthis.errorMessage = \"颜色格式不正确\"\n\t\t\t\t\t\treturn false\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tthis.showError = false\n\t\t\t\tthis.errorMessage = \"\"\n\t\t\t\treturn true\n\t\t\t},\n\n\t\t\tchange(event: FormChangeEvent): void {\n\t\t\t\t// 更新字段值\n\t\t\t\tthis.fieldValue = event.value as string \n\t\t\t\t// 更新显示颜色\n\t\t\t\tthis.updateDisplayColor()\n\t\t\t\t// 保存缓存\n\t\t\t\tthis.setCache()\n\t\t\t\t// 触发父组件事件\n\t\t\t\tthis.$emit('change', event)\n\t\t\t},\n\n\t\t\t// 打开颜色选择器\n\t\t\topenColorPicker(): void {\n\t\t\t\tconst colorPicker = this.$refs[\"colorPicker\"] as ComponentPublicInstance\n\t\t\t\tcolorPicker.$callMethod(\"open\")\n\t\t\t},\n\n\t\t\t// 颜色选择确认\n\t\t\tonColorConfirm(colorData: UTSJSONObject): void {\n\t\t\t\tlet selectedColor: string\n\n\t\t\t\tif (this.varType == \"rgba\") {\n\t\t\t\t\t// 使用rgba格式\n\t\t\t\t\tselectedColor = colorData.getString(\"color\") ?? \"rgba(0, 0, 0, 1)\"\n\t\t\t\t} else {\n\t\t\t\t\t// 使用hex格式\n\t\t\t\t\tselectedColor = colorData.getString(\"hex\") ?? \"#000000\"\n\t\t\t\t}\n\n\t\t\t\tconst result: FormChangeEvent = {\n\t\t\t\t\tindex: this.index,\n\t\t\t\t\tvalue: selectedColor\n\t\t\t\t}\n\t\t\t\tthis.change(result)\n\t\t\t},\n\n\t\t\t// 颜色选择取消\n\t\t\tonColorCancel(): void {\n\t\t\t\t// 取消选择，不做任何操作\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t.color-display-container {\n\t\tflex: 1;\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t\tmin-height: 60rpx;\n\t\tpadding: 10rpx;\n\t\tborder-radius: 10rpx;\n\t\tbackground-color: rgba(255, 255, 255, 0.8);\n\t}\n\n\t.color-preview {\n\t\twidth: 60rpx;\n\t\theight: 40rpx;\n\t\tborder-radius: 8rpx;\n\t\tborder: 1rpx solid #e5e5e5;\n\t\tmargin-right: 20rpx;\n\t\tbox-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);\n\t}\n\n\t.color-text {\n\t\tflex: 1;\n\t\tfont-size: 28rpx;\n\t\tcolor: #333333;\n\t\tfont-family: monospace;\n\t}\n</style>"]}