@file:Suppress("UNCHECKED_CAST", "USELESS_CAST", "INAPPLICABLE_JVM_NAME", "UNUSED_ANONYMOUS_PARAMETER", "NAME_SHADOWING", "UNNECESSARY_NOT_NULL_ASSERTION")
package uni.UNIC178CB1
import io.dcloud.uniapp.*
import io.dcloud.uniapp.extapi.*
import io.dcloud.uniapp.framework.*
import io.dcloud.uniapp.runtime.*
import io.dcloud.uniapp.vue.*
import io.dcloud.uniapp.vue.shared.*
import io.dcloud.unicloud.*
import io.dcloud.uts.*
import io.dcloud.uts.Map
import io.dcloud.uts.Set
import io.dcloud.uts.UTSAndroid
import io.dcloud.uniapp.extapi.getStorage as uni_getStorage
import io.dcloud.uniapp.extapi.setStorage as uni_setStorage
open class GenComponentsMainFormComponentsFormNumberbox : VueComponent {
    constructor(__ins: ComponentInternalInstance) : super(__ins) {
        onCreated(fun(): Unit {
            val fieldObj = this.`$props`["data"] as FormFieldData
            this.initFieldData(fieldObj)
        }
        , __ins)
        this.`$watch`(fun(): Any? {
            return this.data
        }
        , fun(obj: FormFieldData) {
            val newValue = obj.value as Number
            if (newValue !== this.fieldValue) {
                this.fieldValue = newValue
                this.updateInputValue()
            }
        }
        , WatchOptions(deep = true))
    }
    @Suppress("UNUSED_PARAMETER", "UNUSED_VARIABLE")
    override fun `$render`(): Any? {
        val _ctx = this
        val _cache = this.`$`.renderCache
        val _component_form_container = resolveComponent("form-container")
        return _cV(_component_form_container, _uM("label" to _ctx.fieldName, "show-error" to _ctx.showError, "tip" to _ctx.tip, "error-message" to _ctx.errorMessage, "label-color" to _ctx.labelColor, "background-color" to _ctx.backgroundColor), _uM("input-content" to withSlotCtx(fun(): UTSArray<Any> {
            return _uA(
                _cE("view", _uM("class" to "numberbox-container"), _uA(
                    _cE("view", _uM("class" to "numberbox-btn qShadow1", "onClick" to fun(){
                        _ctx.numberFun("-")
                    }
                    ), _uA(
                        _cE("text", _uM("class" to "numberbox-btn-text"), "-")
                    ), 8, _uA(
                        "onClick"
                    )),
                    _cE("view", _uM("class" to "numberbox-input-wrapper"), _uA(
                        _cE("input", _uM("class" to "numberbox-input", "type" to "number", "modelValue" to _ctx.inputValue, "onInput" to _uA(
                            fun(`$event`: UniInputEvent){
                                _ctx.inputValue = `$event`.detail.value
                            }
                            ,
                            _ctx.onInputChange
                        ), "onBlur" to _ctx.onInputBlur), null, 40, _uA(
                            "modelValue",
                            "onInput",
                            "onBlur"
                        ))
                    )),
                    _cE("view", _uM("class" to "numberbox-btn qShadow1", "onClick" to fun(){
                        _ctx.numberFun("+")
                    }
                    ), _uA(
                        _cE("text", _uM("class" to "numberbox-btn-text"), "+")
                    ), 8, _uA(
                        "onClick"
                    )),
                    if (isTrue(_ctx.unitText)) {
                        _cE("view", _uM("key" to 0, "class" to "numberbox-unit"), _uA(
                            _cE("text", _uM("class" to "numberbox-unit-text"), _tD(_ctx.unitText), 1)
                        ))
                    } else {
                        _cC("v-if", true)
                    }
                ))
            )
        }
        ), "_" to 1), 8, _uA(
            "label",
            "show-error",
            "tip",
            "error-message",
            "label-color",
            "background-color"
        ))
    }
    open var data: Any? by `$props`
    open var index: Number by `$props`
    open var keyName: String by `$props`
    open var labelColor: String by `$props`
    open var backgroundColor: String by `$props`
    open var fieldName: String by `$data`
    open var fieldValue: Number by `$data`
    open var isSave: Boolean by `$data`
    open var save_key: String by `$data`
    open var tip: String by `$data`
    open var minValue: Number by `$data`
    open var maxValue: Number by `$data`
    open var stepValue: Number by `$data`
    open var inputValue: String by `$data`
    open var unitText: String by `$data`
    open var showError: Boolean by `$data`
    open var errorMessage: String by `$data`
    @Suppress("USELESS_CAST")
    override fun data(): Map<String, Any?> {
        return _uM("fieldName" to "", "fieldValue" to 0, "isSave" to false, "save_key" to "", "tip" to "", "minValue" to 0, "maxValue" to 100, "stepValue" to 1, "inputValue" to "0", "unitText" to "", "showError" to false, "errorMessage" to "")
    }
    open var initFieldData = ::gen_initFieldData_fn
    open fun gen_initFieldData_fn(fieldObj: FormFieldData): Unit {
        val fieldKey = fieldObj.key
        val fieldValue = fieldObj.value as Number
        this.fieldName = fieldObj.name
        this.fieldValue = fieldValue
        this.isSave = fieldObj.isSave ?: false
        this.save_key = this.keyName + "_" + fieldKey
        val extalJson = fieldObj.extra as UTSJSONObject
        this.minValue = extalJson.getNumber("min") ?: 0
        this.maxValue = extalJson.getNumber("max") ?: 100
        this.stepValue = extalJson.getNumber("step") ?: 1
        this.tip = extalJson.getString("tip") ?: ""
        this.unitText = extalJson.getString("unit") ?: ""
        this.updateInputValue()
        setTimeout(fun(){
            this.getCache()
        }
        , 500)
    }
    open var updateInputValue = ::gen_updateInputValue_fn
    open fun gen_updateInputValue_fn(): Unit {
        this.inputValue = this.fieldValue.toString(10)
    }
    open var validateValue = ::gen_validateValue_fn
    open fun gen_validateValue_fn(value: Number): Number {
        if (value < this.minValue) {
            return this.minValue
        }
        if (value > this.maxValue) {
            return this.maxValue
        }
        if (this.stepValue % 1 == 0) {
            return Math.round(value)
        } else {
            return UTSNumber.from(value.toFixed(1))
        }
    }
    open var getCache = ::gen_getCache_fn
    open fun gen_getCache_fn(): Unit {
        if (this.isSave) {
            val that = this
            uni_getStorage(GetStorageOptions(key = this.save_key, success = fun(res: GetStorageSuccess){
                val save_value = res.data as Number
                val validatedValue = that.validateValue(save_value)
                that.fieldValue = validatedValue
                that.updateInputValue()
                val result = FormChangeEvent(index = this.index, value = validatedValue)
                this.change(result)
            }
            ))
        }
    }
    open var setCache = ::gen_setCache_fn
    open fun gen_setCache_fn(): Unit {
        if (this.isSave) {
            uni_setStorage(SetStorageOptions(key = this.save_key, data = this.fieldValue))
        }
    }
    open var validate = ::gen_validate_fn
    open fun gen_validate_fn(): Boolean {
        this.showError = false
        this.errorMessage = ""
        return true
    }
    open var change = ::gen_change_fn
    open fun gen_change_fn(event: FormChangeEvent): Unit {
        this.fieldValue = event.value as Number
        this.setCache()
        this.`$emit`("change", event)
    }
    open var numberFun = ::gen_numberFun_fn
    open fun gen_numberFun_fn(type: String): Unit {
        var value: Number
        var num: Number = this.fieldValue
        if (type == "+") {
            num = num + this.stepValue
        } else {
            num = num - this.stepValue
        }
        value = this.validateValue(num)
        this.inputValue = value.toString(10)
        val result = FormChangeEvent(index = this.index, value = value)
        this.change(result)
    }
    open var onInputChange = ::gen_onInputChange_fn
    open fun gen_onInputChange_fn(event: UniInputEvent): Unit {
        val inputStr = event.detail.value as String
        val inputNum = parseFloat(inputStr)
        if (!isNaN(inputNum)) {
            val validatedValue = this.validateValue(inputNum)
            val result = FormChangeEvent(index = this.index, value = validatedValue)
            this.change(result)
        }
    }
    open var onInputBlur = ::gen_onInputBlur_fn
    open fun gen_onInputBlur_fn(): Unit {
        val inputNum = parseFloat(this.inputValue)
        if (isNaN(inputNum)) {
            this.inputValue = this.fieldValue.toString(10)
        } else {
            val validatedValue = this.validateValue(inputNum)
            this.inputValue = validatedValue.toString(10)
            if (validatedValue !== this.fieldValue) {
                val result = FormChangeEvent(index = this.index, value = validatedValue)
                this.change(result)
            }
        }
        this.validate()
    }
    companion object {
        var name = "FormNumberbox"
        val styles: Map<String, Map<String, Map<String, Any>>> by lazy {
            _nCS(_uA(
                styles0
            ))
        }
        val styles0: Map<String, Map<String, Map<String, Any>>>
            get() {
                return _uM("numberbox-container" to _pS(_uM("width" to "100%", "display" to "flex", "flexDirection" to "row", "alignItems" to "center")), "numberbox-btn" to _pS(_uM("width" to "80rpx", "height" to "80rpx", "display" to "flex", "justifyContent" to "center", "alignItems" to "center", "backgroundColor" to "#e1e1e1", "borderTopLeftRadius" to "10rpx", "borderTopRightRadius" to "10rpx", "borderBottomRightRadius" to "10rpx", "borderBottomLeftRadius" to "10rpx")), "numberbox-btn-text" to _pS(_uM("fontSize" to "32rpx", "fontWeight" to "bold", "color" to "#333333")), "numberbox-input-wrapper" to _pS(_uM("flex" to 1, "height" to "80rpx", "backgroundColor" to "#f2f4f8", "marginTop" to 0, "marginRight" to "20rpx", "marginBottom" to 0, "marginLeft" to "20rpx", "borderTopLeftRadius" to "10rpx", "borderTopRightRadius" to "10rpx", "borderBottomRightRadius" to "10rpx", "borderBottomLeftRadius" to "10rpx")), "numberbox-input" to _pS(_uM("width" to "100%", "height" to "100%", "textAlign" to "center", "fontSize" to "32rpx", "borderTopWidth" to "medium", "borderRightWidth" to "medium", "borderBottomWidth" to "medium", "borderLeftWidth" to "medium", "borderTopStyle" to "none", "borderRightStyle" to "none", "borderBottomStyle" to "none", "borderLeftStyle" to "none", "borderTopColor" to "#000000", "borderRightColor" to "#000000", "borderBottomColor" to "#000000", "borderLeftColor" to "#000000", "backgroundColor" to "rgba(0,0,0,0)")), "numberbox-unit" to _pS(_uM("paddingTop" to 0, "paddingRight" to "10rpx", "paddingBottom" to 0, "paddingLeft" to "10rpx", "height" to "80rpx", "display" to "flex", "justifyContent" to "center", "alignItems" to "center", "backgroundColor" to "#333333", "borderTopLeftRadius" to "10rpx", "borderTopRightRadius" to "10rpx", "borderBottomRightRadius" to "10rpx", "borderBottomLeftRadius" to "10rpx", "marginLeft" to "20rpx")), "numberbox-unit-text" to _pS(_uM("color" to "#ffffff", "fontSize" to "28rpx")), "qShadow1" to _pS(_uM("boxShadow" to "0 2rpx 8rpx rgba(0, 0, 0, 0.1)")))
            }
        var inheritAttrs = true
        var inject: Map<String, Map<String, Any?>> = _uM()
        var emits: Map<String, Any?> = _uM("change" to null)
        var props = _nP(_uM("data" to _uM(), "index" to _uM("type" to "Number", "default" to 0), "keyName" to _uM("type" to "String", "default" to ""), "labelColor" to _uM("type" to "String", "default" to "#000"), "backgroundColor" to _uM("type" to "String", "default" to "#f1f4f9")))
        var propsNeedCastKeys = _uA(
            "index",
            "keyName",
            "labelColor",
            "backgroundColor"
        )
        var components: Map<String, CreateVueComponent> = _uM("FormContainer" to GenComponentsMainFormComponentsFormContainerClass)
    }
}
