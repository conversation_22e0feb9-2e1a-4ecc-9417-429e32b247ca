
	// 定义颜色类型
	type ColorInfo = { __$originalPosition?: UTSSourceMapPosition<"ColorInfo", "components/main-form/tools/main-color-picker.uvue", 117, 7>;
		r : number,
		g : number,
		b : number
	}
	type RGBAValues = { __$originalPosition?: UTSSourceMapPosition<"RGBAValues", "components/main-form/tools/main-color-picker.uvue", 122, 7>;
	  r: number,
	  g: number,
	  b: number,
	  a: number
	}
	type RGBValues = { __$originalPosition?: UTSSourceMapPosition<"RGBValues", "components/main-form/tools/main-color-picker.uvue", 128, 7>;
	  r: number,
	  g: number,
	  b: number
	}
	type ColorSeries = { __$originalPosition?: UTSSourceMapPosition<"ColorSeries", "components/main-form/tools/main-color-picker.uvue", 133, 7>;
	  name: string,
	  color: string
	}
	const __sfc__ = defineComponent({
		name: "main-color-picker",
		emits: ['cancel', 'confirm'],
		data() {
			return {
				// 控制弹窗显示
				visible: false as boolean,
				// 当前选中的颜色系列索引
				selectedSeriesIndex: 0 as number,
				// 透明度，范围0-1
				opacity: 1.0 as number,
				// 当前选中的颜色索引
				selectedColorIndex: 0 as number,
				// 基础颜色（可以根据需要修改）
				baseColor: { r: 255.0, g: 0.0, b: 0.0 } as ColorInfo,
				// 随机色种子，用于重新生成随机色
				randomSeed: 0 as number,
				// RGB设置弹窗相关
				showRGBModal: false as boolean,
				tempR: 255 as number,
				tempG: 0 as number,
				tempB: 0 as number,
				// 自定义颜色
				customColor: "" as string
			}
		},
		computed: {
			// 颜色系列列表
			colorSeriesList(): ColorSeries[] {
				return [
					{ name: "随机色", color: "#FF6B35" },
					{ name: "黑白灰", color: "#808080" },
					{ name: "红色", color: "#FF4444" },
					{ name: "橙色", color: "#FF8844" },
					{ name: "黄色", color: "#FFDD44" },
					{ name: "绿色", color: "#44FF44" },
					{ name: "青色", color: "#44FFFF" },
					{ name: "蓝色", color: "#4444FF" },
					{ name: "紫色", color: "#AA44FF" },
					{ name: "粉色", color: "#FF88CC" },
					{ name: "棕色", color: "#AA6644" }
				]
			},

			// 根据选中的系列生成120个颜色（10行12列）
			colorList() : string[] {
				const colors : string[] = []

				for (let i = 0; i < 120; i++) {
					const row = Math.floor(i / 12) // 当前行（0-9）
					const col = i % 12 // 当前列（0-11）

					// 计算位置因子
					const rowFactor = row / 9.0 // 行因子 0-1
					const colFactor = col / 11.0 // 列因子 0-1

					// 基于选中的系列索引确定颜色系列
					let r: number, g: number, b: number

					if (this.selectedSeriesIndex == 0) {
						// 随机色系列 - 每个方块完全随机的RGB值
						const seed1 = (row * 12 + col + this.randomSeed) * 0.1
						const seed2 = (row * 12 + col + this.randomSeed + 100) * 0.13
						const seed3 = (row * 12 + col + this.randomSeed + 200) * 0.17
						r = Math.round((Math.sin(seed1) * 0.5 + 0.5) * 255)
						g = Math.round((Math.sin(seed2) * 0.5 + 0.5) * 255)
						b = Math.round((Math.sin(seed3) * 0.5 + 0.5) * 255)
					} else if (this.selectedSeriesIndex == 1) {
						// 黑白灰系列 - 更细腻的灰度变化
						const totalFactor = (row * 12 + col) / 119.0 // 0到1的完整渐变
						const grayValue = Math.round(totalFactor * 255)
						r = grayValue
						g = grayValue
						b = grayValue
					} else if (this.selectedSeriesIndex == 2) {
						// 红色系列 - 更丰富的红色变化
						const totalFactor = (row * 12 + col) / 119.0 // 0到1
						const brightness = 0.2 + totalFactor * 0.8 // 0.2-1.0的亮度范围
						const saturation = 0.3 + (1 - Math.abs(totalFactor - 0.5) * 2) * 0.7 // 中间饱和度高
						r = Math.round(brightness * 255)
						g = Math.round(brightness * (1 - saturation) * 255)
						b = Math.round(brightness * (1 - saturation) * 255)
					} else {
						// 其他颜色系列 - 确保包含纯色且避免黑色
						const totalFactor = (row * 12 + col) / 119.0 // 0到1

						// 根据系列索引确定基础色相
						let baseHue: number
						if (this.selectedSeriesIndex == 3) baseHue = 30      // 橙色
						else if (this.selectedSeriesIndex == 4) baseHue = 60 // 黄色
						else if (this.selectedSeriesIndex == 5) baseHue = 120 // 绿色
						else if (this.selectedSeriesIndex == 6) baseHue = 180 // 青色
						else if (this.selectedSeriesIndex == 7) baseHue = 240 // 蓝色
						else if (this.selectedSeriesIndex == 8) baseHue = 300 // 紫色
						else if (this.selectedSeriesIndex == 9) baseHue = 330 // 粉色
						else baseHue = 25 // 棕色

						// 色相微调：在基础色相±10度范围内变化
						const hue = baseHue + (colFactor - 0.5) * 20

						// 创造三种类型的颜色变化
						if (totalFactor < 0.4) {
							// 前40%：深色调 - 高饱和度，中低明度（避免太暗）
							const localFactor = totalFactor / 0.4
							const saturation = 0.8 + localFactor * 0.2 // 0.8-1.0
							const value = 0.4 + localFactor * 0.3 // 0.4-0.7（避免太暗）
							const rgb = this.hsvToRgb(hue, saturation, value)
							r = rgb.r
							g = rgb.g
							b = rgb.b
						} else if (totalFactor < 0.6) {
							// 中20%：纯色调 - 最高饱和度，最佳明度
							const localFactor = (totalFactor - 0.4) / 0.2
							const saturation = 1.0 // 最高饱和度
							const value = 0.8 + localFactor * 0.2 // 0.8-1.0（确保亮度足够）
							const rgb = this.hsvToRgb(hue, saturation, value)
							r = rgb.r
							g = rgb.g
							b = rgb.b
						} else {
							// 后40%：浅色调 - 降低饱和度，保持高明度
							const localFactor = (totalFactor - 0.6) / 0.4
							const saturation = 0.8 - localFactor * 0.6 // 0.8-0.2（逐渐降低饱和度）
							const value = 0.9 + localFactor * 0.1 // 0.9-1.0（保持高明度）
							const rgb = this.hsvToRgb(hue, saturation, value)
							r = rgb.r
							g = rgb.g
							b = rgb.b
						}
					}

					// 确保RGB值在0-255范围内
					r = Math.max(0, Math.min(255, r))
					g = Math.max(0, Math.min(255, g))
					b = Math.max(0, Math.min(255, b))

					colors.push(`rgb(${r}, ${g}, ${b})`)
				}

				return colors
			},



			// 最终的RGBA颜色值
			finalColor() : string {
				// 优先使用自定义颜色
				let colorToUse = ""
				if (this.customColor != "") {
					colorToUse = this.customColor
				} else if (this.colorList.length > this.selectedColorIndex) {
					colorToUse = this.colorList[this.selectedColorIndex]
				}

				if (colorToUse != "") {
					// 提取RGB值并添加透明度
					const rgbMatch = colorToUse.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/)
					if (rgbMatch != null) {
						const r = parseInt(rgbMatch[1] as string)
						const g = parseInt(rgbMatch[2] as string)
						const b = parseInt(rgbMatch[3] as string)
						return `rgba(${r}, ${g}, ${b}, ${this.opacity})`
					}
				}
				return `rgba(255, 0, 0, ${this.opacity})`
			},

			// 临时RGB颜色预览
			tempRGBColor() : string {
				return `rgb(${this.tempR}, ${this.tempG}, ${this.tempB})`
			}
		},
		methods: {
			// 颜色系列选择事件
			onSeriesSelect(index: number) {
				this.selectedSeriesIndex = index
				this.selectedColorIndex = 0 // 重置选中的颜色
				this.customColor = "" // 清除自定义颜色

				// 如果选择的是随机色系列，生成新的随机种子
				if (index == 0) { // 随机色现在是第1个按钮，索引为0
					this.randomSeed = Math.floor(Math.random() * 1000)
				}
			},

			// 显示透明度选择器
			showOpacityPicker() {
				const opacityOptions = [
					'100%', '95%', '90%', '85%', '80%', '75%', '70%', '65%', '60%', '55%',
					'50%', '45%', '40%', '35%', '30%', '25%', '20%', '15%', '10%', '5%'
				]

				uni.showActionSheet({
					itemList: opacityOptions,
					success: (res) => {
						const selectedOpacity = (100 - res.tapIndex * 5) / 100
						this.opacity = selectedOpacity
					}
				})
			},

			// 颜色选择事件
			onColorSelect(index : number) {
				this.selectedColorIndex = index
				// 清除自定义颜色，使用新选中的颜色
				this.customColor = ""
			},

			// 显示RGB设置弹窗
			showRGBPicker() {
				// 获取当前颜色的RGB值（优先使用自定义颜色）
				let colorToUse = ""
				if (this.customColor != "") {
					colorToUse = this.customColor
				} else if (this.colorList.length > this.selectedColorIndex) {
					colorToUse = this.colorList[this.selectedColorIndex]
				}

				if (colorToUse != "") {
					const rgbMatch = colorToUse.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/)
					if (rgbMatch != null) {
						this.tempR = parseInt(rgbMatch[1] as string)
						this.tempG = parseInt(rgbMatch[2] as string)
						this.tempB = parseInt(rgbMatch[3] as string)
					} else {
						this.tempR = 255
						this.tempG = 0
						this.tempB = 0
					}
				} else {
					this.tempR = 255
					this.tempG = 0
					this.tempB = 0
				}
				this.showRGBModal = true
			},

			// 关闭RGB设置弹窗
			closeRGBPicker() {
				this.showRGBModal = false
			},

			// RGB弹窗点击事件（阻止冒泡）
			onRGBModalClick() {
				// 空方法，用于阻止事件冒泡
			},

			// 确认RGB设置
			confirmRGBPicker() {
				// 设置自定义颜色
				this.customColor = `rgb(${this.tempR}, ${this.tempG}, ${this.tempB})`
				this.showRGBModal = false
			},

			// R值滑块变化
			onTempRChange(event: UniSliderChangeEvent) {
				this.tempR = event.detail.value as number
			},

			// G值滑块变化
			onTempGChange(event: UniSliderChangeEvent) {
				this.tempG = event.detail.value as number
			},

			// B值滑块变化
			onTempBChange(event: UniSliderChangeEvent) {
				this.tempB = event.detail.value as number
			},

			// R值输入框变化
			onTempRInput(event: UniInputEvent) {
				const value = parseInt(event.detail.value)
				if (!isNaN(value)) {
					this.tempR = Math.max(0, Math.min(255, value))
				}
			},

			// G值输入框变化
			onTempGInput(event: UniInputEvent) {
				const value = parseInt(event.detail.value)
				if (!isNaN(value)) {
					this.tempG = Math.max(0, Math.min(255, value))
				}
			},

			// B值输入框变化
			onTempBInput(event: UniInputEvent) {
				const value = parseInt(event.detail.value)
				if (!isNaN(value)) {
					this.tempB = Math.max(0, Math.min(255, value))
				}
			},

			// 打开弹窗
			open() {
				this.visible = true
			},

			// 关闭弹窗
			close() {
				this.visible = false
			},

			// 点击遮罩层关闭弹窗
			onOverlayClick() {
				this.close()
				this.$emit('cancel')
			},

			// 取消按钮点击事件
			onCancel() {
				this.close()
				this.$emit('cancel')
			},

			// 确定按钮点击事件
			onConfirm() {
				this.close()
				const rgbaValues = this.getRGBAValues()
				this.$emit('confirm', {
					color: this.finalColor,
					rgba: rgbaValues,
					hex: this.rgbToHex(rgbaValues.r, rgbaValues.g, rgbaValues.b)
				})
			},
 
			// 获取RGBA数值
			getRGBAValues() : RGBAValues {
				const rgbaMatch = this.finalColor.match(/rgba\((\d+),\s*(\d+),\s*(\d+),\s*([\d.]+)\)/)
				if (rgbaMatch != null) {
					return {
						r: parseInt(rgbaMatch[1] as string),
						g: parseInt(rgbaMatch[2] as string),
						b: parseInt(rgbaMatch[3] as string),
						a: parseFloat(rgbaMatch[4] as string)
					}
				}
				return { r: 255, g: 0, b: 0, a: 1.0 }
			},

			// HSV转RGB
			hsvToRgb(h: number, s: number, v: number): RGBValues {
				const c: number = v * s
				const x: number = c * (1.0 - Math.abs(((h / 60.0) % 2.0) - 1.0))
				const m: number = v - c

				let r: number = 0.0
				let g: number = 0.0
				let b: number = 0.0

				if (h >= 0 && h < 60) {
					r = c
					g = x
					b = 0.0
				} else if (h >= 60 && h < 120) {
					r = x
					g = c
					b = 0.0
				} else if (h >= 120 && h < 180) {
					r = 0.0
					g = c
					b = x
				} else if (h >= 180 && h < 240) {
					r = 0.0
					g = x
					b = c
				} else if (h >= 240 && h < 300) {
					r = x
					g = 0.0
					b = c
				} else if (h >= 300 && h < 360) {
					r = c
					g = 0.0
					b = x
				}

				const result: RGBValues = {
					r: Math.round((r + m) * 255.0),
					g: Math.round((g + m) * 255.0),
					b: Math.round((b + m) * 255.0)
				}
				return result
			},

			// RGB转十六进制
			rgbToHex(r: number, g: number, b: number): string {
				const toHex = (value: number): string => {
					const hex = Math.round(Math.max(0, Math.min(255, value))).toString(16)
					return hex.length == 1 ? '0' + hex : hex
				}
				return '#' + toHex(r) + toHex(g) + toHex(b)
			},


		}
	})

export default __sfc__
function GenComponentsMainFormToolsMainColorPickerRender(this: InstanceType<typeof __sfc__>): any | null {
const _ctx = this
const _cache = this.$.renderCache
const _component_slider = resolveComponent("slider")

  return isTrue(_ctx.visible)
    ? _cE("view", _uM({
        key: 0,
        class: "picker-overlay",
        onClick: _ctx.onOverlayClick
      }), [
        _cE("view", _uM({
          class: "picker-modal",
          onClick: withModifiers(() => {}, ["stop"])
        }), [
          _cE("view", _uM({ class: "color-picker-container" }), [
            _cE("view", _uM({ class: "navbar" }), [
              _cE("text", _uM({
                class: "nav-btn cancel-btn",
                onClick: _ctx.onCancel
              }), "取消", 8 /* PROPS */, ["onClick"]),
              _cE("text", _uM({ class: "nav-title" }), "颜色选择"),
              _cE("view", _uM({ class: "confirm-btn-container" }), [
                _cE("text", _uM({
                  class: "nav-btn confirm-btn",
                  onClick: _ctx.onConfirm
                }), "确定", 8 /* PROPS */, ["onClick"])
              ])
            ]),
            _cE("view", _uM({ class: "color-series-section" }), [
              _cE("view", _uM({ class: "color-series-buttons" }), [
                _cE(Fragment, null, RenderHelpers.renderList(_ctx.colorSeriesList, (series, index, __index, _cached): any => {
                  return _cE("view", _uM({
                    key: index,
                    class: _nC(["series-button", _uM({
								'active': _ctx.selectedSeriesIndex == index,
								'random-button': index == 0,
								'normal-button': index != 0
							})]),
                    style: _nS(_uM({ backgroundColor: series.color })),
                    onClick: () => {_ctx.onSeriesSelect(index)}
                  }), [
                    _cE("text", _uM({ class: "series-text" }), _tD(series.name), 1 /* TEXT */)
                  ], 14 /* CLASS, STYLE, PROPS */, ["onClick"])
                }), 128 /* KEYED_FRAGMENT */)
              ])
            ]),
            _cE("view", _uM({ class: "color-grid-section" }), [
              _cE("view", _uM({ class: "color-grid" }), [
                _cE(Fragment, null, RenderHelpers.renderList(_ctx.colorList, (color, index, __index, _cached): any => {
                  return _cE("view", _uM({
                    key: index,
                    class: _nC(["color-item", _uM({ 'selected': _ctx.selectedColorIndex == index })]),
                    style: _nS(_uM({ backgroundColor: color })),
                    onClick: () => {_ctx.onColorSelect(index)}
                  }), null, 14 /* CLASS, STYLE, PROPS */, ["onClick"])
                }), 128 /* KEYED_FRAGMENT */)
              ])
            ]),
            _cE("view", _uM({ class: "preview-opacity-section" }), [
              _cE("view", _uM({
                class: "preview-area",
                onClick: _ctx.showRGBPicker
              }), [
                _cE("view", _uM({
                  class: "preview-color",
                  style: _nS(_uM({ backgroundColor: _ctx.finalColor }))
                }), null, 4 /* STYLE */),
                _cE("text", _uM({ class: "rgba-text" }), _tD(_ctx.finalColor), 1 /* TEXT */)
              ], 8 /* PROPS */, ["onClick"]),
              _cE("view", _uM({ class: "opacity-area" }), [
                _cE("text", _uM({ class: "opacity-label" }), "透明度"),
                _cE("view", _uM({
                  class: "opacity-button",
                  onClick: _ctx.showOpacityPicker
                }), [
                  _cE("text", _uM({ class: "opacity-value" }), _tD(Math.round(_ctx.opacity * 100)) + "%", 1 /* TEXT */)
                ], 8 /* PROPS */, ["onClick"])
              ])
            ]),
            isTrue(_ctx.showRGBModal)
              ? _cE("view", _uM({
                  key: 0,
                  class: "rgb-modal-overlay",
                  onClick: _ctx.closeRGBPicker
                }), [
                  _cE("view", _uM({
                    class: "rgb-modal",
                    onClick: _ctx.onRGBModalClick
                  }), [
                    _cE("view", _uM({ class: "rgb-modal-header" }), [
                      _cE("text", _uM({ class: "rgb-modal-title" }), "RGB颜色设置")
                    ]),
                    _cE("view", _uM({ class: "rgb-preview-section" }), [
                      _cE("view", _uM({
                        class: "rgb-preview-color",
                        style: _nS(_uM({ backgroundColor: _ctx.tempRGBColor }))
                      }), null, 4 /* STYLE */),
                      _cE("text", _uM({ class: "rgb-preview-text" }), _tD(_ctx.tempRGBColor), 1 /* TEXT */)
                    ]),
                    _cE("view", _uM({ class: "rgb-controls" }), [
                      _cE("view", _uM({ class: "rgb-control-item" }), [
                        _cE("text", _uM({ class: "rgb-label" }), "R"),
                        _cV(_component_slider, _uM({
                          class: "rgb-slider",
                          min: 0,
                          max: 255,
                          step: 1,
                          value: _ctx.tempR,
                          onChange: _ctx.onTempRChange
                        }), null, 8 /* PROPS */, ["value", "onChange"]),
                        _cE("input", _uM({
                          class: "rgb-input",
                          type: "number",
                          value: _ctx.tempR.toString(),
                          onInput: _ctx.onTempRInput,
                          placeholder: "0-255"
                        }), null, 40 /* PROPS, NEED_HYDRATION */, ["value", "onInput"])
                      ]),
                      _cE("view", _uM({ class: "rgb-control-item" }), [
                        _cE("text", _uM({ class: "rgb-label" }), "G"),
                        _cV(_component_slider, _uM({
                          class: "rgb-slider",
                          min: 0,
                          max: 255,
                          step: 1,
                          value: _ctx.tempG,
                          onChange: _ctx.onTempGChange
                        }), null, 8 /* PROPS */, ["value", "onChange"]),
                        _cE("input", _uM({
                          class: "rgb-input",
                          type: "number",
                          value: _ctx.tempG.toString(),
                          onInput: _ctx.onTempGInput,
                          placeholder: "0-255"
                        }), null, 40 /* PROPS, NEED_HYDRATION */, ["value", "onInput"])
                      ]),
                      _cE("view", _uM({ class: "rgb-control-item" }), [
                        _cE("text", _uM({ class: "rgb-label" }), "B"),
                        _cV(_component_slider, _uM({
                          class: "rgb-slider",
                          min: 0,
                          max: 255,
                          step: 1,
                          value: _ctx.tempB,
                          onChange: _ctx.onTempBChange
                        }), null, 8 /* PROPS */, ["value", "onChange"]),
                        _cE("input", _uM({
                          class: "rgb-input",
                          type: "number",
                          value: _ctx.tempB.toString(),
                          onInput: _ctx.onTempBInput,
                          placeholder: "0-255"
                        }), null, 40 /* PROPS, NEED_HYDRATION */, ["value", "onInput"])
                      ])
                    ]),
                    _cE("view", _uM({ class: "rgb-modal-buttons" }), [
                      _cE("view", _uM({
                        class: "rgb-button rgb-cancel",
                        onClick: _ctx.closeRGBPicker
                      }), [
                        _cE("text", _uM({ class: "rgb-button-text" }), "取消")
                      ], 8 /* PROPS */, ["onClick"]),
                      _cE("view", _uM({
                        class: "rgb-button rgb-confirm",
                        onClick: _ctx.confirmRGBPicker
                      }), [
                        _cE("text", _uM({ class: "rgb-button-text" }), "确定")
                      ], 8 /* PROPS */, ["onClick"])
                    ])
                  ], 8 /* PROPS */, ["onClick"])
                ], 8 /* PROPS */, ["onClick"])
              : _cC("v-if", true)
          ])
        ], 8 /* PROPS */, ["onClick"])
      ], 8 /* PROPS */, ["onClick"])
    : _cC("v-if", true)
}
const GenComponentsMainFormToolsMainColorPickerStyles = [_uM([["picker-overlay", _pS(_uM([["position", "fixed"], ["top", 0], ["left", 0], ["right", 0], ["bottom", 0], ["backgroundColor", "rgba(0,0,0,0.5)"], ["display", "flex"], ["alignItems", "center"], ["justifyContent", "flex-end"], ["zIndex", 1000]]))], ["picker-modal", _pS(_uM([["width", "100%"], ["backgroundColor", "#ffffff"], ["borderTopLeftRadius", "20rpx"], ["borderTopRightRadius", "20rpx"], ["overflow", "hidden"], ["boxShadow", "0 8px 32px rgba(0, 0, 0, 0.3)"]]))], ["color-picker-container", _pS(_uM([["width", "100%"], ["height", "100%"], ["backgroundColor", "#ffffff"], ["display", "flex"], ["flexDirection", "column"]]))], ["navbar", _pS(_uM([["height", 44], ["backgroundColor", "#f8f8f8"], ["borderBottomWidth", 1], ["borderBottomStyle", "solid"], ["borderBottomColor", "#e5e5e5"], ["display", "flex"], ["flexDirection", "row"], ["alignItems", "center"], ["justifyContent", "space-between"], ["paddingTop", 0], ["paddingRight", 10], ["paddingBottom", 0], ["paddingLeft", 10]]))], ["nav-btn", _pS(_uM([["fontSize", 16], ["color", "#007aff"], ["paddingTop", 8], ["paddingRight", 12], ["paddingBottom", 8], ["paddingLeft", 12]]))], ["cancel-btn", _pS(_uM([["color", "#999999"]]))], ["confirm-btn-container", _pS(_uM([["height", 30], ["backgroundColor", "#007aff"], ["borderTopLeftRadius", "8rpx"], ["borderTopRightRadius", "8rpx"], ["borderBottomRightRadius", "8rpx"], ["borderBottomLeftRadius", "8rpx"], ["display", "flex"], ["justifyContent", "center"], ["alignItems", "center"], ["boxShadow", "0 2rpx 8rpx rgba(0, 122, 255, 0.3)"]]))], ["confirm-btn", _pS(_uM([["color", "#ffffff"], ["fontWeight", "bold"]]))], ["nav-title", _pS(_uM([["fontSize", 17], ["color", "#333333"]]))], ["section-title", _pS(_uM([["fontSize", 14], ["color", "#666666"], ["marginBottom", 10]]))], ["color-series-section", _pS(_uM([["paddingTop", "20rpx"], ["paddingRight", "20rpx"], ["paddingBottom", "20rpx"], ["paddingLeft", "20rpx"], ["borderBottomWidth", 1], ["borderBottomStyle", "solid"], ["borderBottomColor", "#f0f0f0"]]))], ["color-series-buttons", _pS(_uM([["display", "flex"], ["flexDirection", "row"], ["flexWrap", "wrap"], ["justifyContent", "space-between"], ["marginTop", "10rpx"], ["paddingTop", 0], ["paddingRight", "10rpx"], ["paddingBottom", 0], ["paddingLeft", "10rpx"]]))], ["series-button", _uM([["", _uM([["height", "60rpx"], ["borderTopLeftRadius", "8rpx"], ["borderTopRightRadius", "8rpx"], ["borderBottomRightRadius", "8rpx"], ["borderBottomLeftRadius", "8rpx"], ["borderTopWidth", 2], ["borderRightWidth", 2], ["borderBottomWidth", 2], ["borderLeftWidth", 2], ["borderTopStyle", "solid"], ["borderRightStyle", "solid"], ["borderBottomStyle", "solid"], ["borderLeftStyle", "solid"], ["borderTopColor", "rgba(0,0,0,0)"], ["borderRightColor", "rgba(0,0,0,0)"], ["borderBottomColor", "rgba(0,0,0,0)"], ["borderLeftColor", "rgba(0,0,0,0)"], ["display", "flex"], ["alignItems", "center"], ["justifyContent", "center"], ["marginBottom", "10rpx"], ["boxSizing", "border-box"]])], [".active", _uM([["borderTopColor", "#007aff"], ["borderRightColor", "#007aff"], ["borderBottomColor", "#007aff"], ["borderLeftColor", "#007aff"], ["boxShadow", "0 0 0 1px #007aff"]])]])], ["random-button", _pS(_uM([["width", "220rpx"]]))], ["normal-button", _pS(_uM([["width", "100rpx"]]))], ["series-text", _pS(_uM([["fontSize", "24rpx"], ["color", "#ffffff"], ["fontWeight", "bold"], ["textShadow", "1px 1px 2px rgba(0, 0, 0, 0.5)"]]))], ["color-grid-section", _pS(_uM([["paddingTop", "20rpx"], ["paddingRight", "20rpx"], ["paddingBottom", "20rpx"], ["paddingLeft", "20rpx"], ["borderBottomWidth", 1], ["borderBottomStyle", "solid"], ["borderBottomColor", "#f0f0f0"]]))], ["color-grid", _pS(_uM([["display", "flex"], ["flexDirection", "row"], ["flexWrap", "wrap"], ["justifyContent", "space-around"], ["alignItems", "flex-start"], ["paddingTop", "15rpx"], ["paddingRight", "15rpx"], ["paddingBottom", "15rpx"], ["paddingLeft", "15rpx"]]))], ["color-item", _uM([["", _uM([["width", "55rpx"], ["height", "40rpx"], ["borderTopLeftRadius", 4], ["borderTopRightRadius", 4], ["borderBottomRightRadius", 4], ["borderBottomLeftRadius", 4], ["borderTopWidth", 2], ["borderRightWidth", 2], ["borderBottomWidth", 2], ["borderLeftWidth", 2], ["borderTopStyle", "solid"], ["borderRightStyle", "solid"], ["borderBottomStyle", "solid"], ["borderLeftStyle", "solid"], ["borderTopColor", "rgba(0,0,0,0)"], ["borderRightColor", "rgba(0,0,0,0)"], ["borderBottomColor", "rgba(0,0,0,0)"], ["borderLeftColor", "rgba(0,0,0,0)"], ["marginBottom", 4], ["flexShrink", 0], ["flexGrow", 0], ["boxSizing", "border-box"]])], [".selected", _uM([["borderTopColor", "#007aff"], ["borderRightColor", "#007aff"], ["borderBottomColor", "#007aff"], ["borderLeftColor", "#007aff"], ["boxShadow", "0 0 0 1px #007aff"]])]])], ["preview-opacity-section", _pS(_uM([["paddingTop", "15rpx"], ["paddingRight", "20rpx"], ["paddingBottom", "15rpx"], ["paddingLeft", "20rpx"], ["display", "flex"], ["flexDirection", "row"], ["alignItems", "center"], ["justifyContent", "space-between"], ["borderBottomWidth", 1], ["borderBottomStyle", "solid"], ["borderBottomColor", "#f0f0f0"]]))], ["preview-area", _pS(_uM([["display", "flex"], ["flexDirection", "row"], ["alignItems", "center"], ["flex", 1]]))], ["preview-color", _pS(_uM([["width", "60rpx"], ["height", "60rpx"], ["borderTopLeftRadius", "30rpx"], ["borderTopRightRadius", "30rpx"], ["borderBottomRightRadius", "30rpx"], ["borderBottomLeftRadius", "30rpx"], ["borderTopWidth", 1], ["borderRightWidth", 1], ["borderBottomWidth", 1], ["borderLeftWidth", 1], ["borderTopStyle", "solid"], ["borderRightStyle", "solid"], ["borderBottomStyle", "solid"], ["borderLeftStyle", "solid"], ["borderTopColor", "#e5e5e5"], ["borderRightColor", "#e5e5e5"], ["borderBottomColor", "#e5e5e5"], ["borderLeftColor", "#e5e5e5"], ["marginRight", "15rpx"], ["boxShadow", "0 2px 8px rgba(0, 0, 0, 0.1)"]]))], ["rgba-text", _pS(_uM([["fontSize", "24rpx"], ["color", "#666666"], ["fontFamily", "monospace"], ["backgroundColor", "#f5f5f5"], ["paddingTop", "8rpx"], ["paddingRight", "12rpx"], ["paddingBottom", "8rpx"], ["paddingLeft", "12rpx"], ["borderTopLeftRadius", "6rpx"], ["borderTopRightRadius", "6rpx"], ["borderBottomRightRadius", "6rpx"], ["borderBottomLeftRadius", "6rpx"]]))], ["opacity-area", _pS(_uM([["display", "flex"], ["flexDirection", "column"], ["alignItems", "center"]]))], ["opacity-label", _pS(_uM([["fontSize", "24rpx"], ["color", "#666666"], ["marginBottom", "8rpx"]]))], ["opacity-button", _pS(_uM([["backgroundColor", "#007aff"], ["paddingTop", "12rpx"], ["paddingRight", "20rpx"], ["paddingBottom", "12rpx"], ["paddingLeft", "20rpx"], ["borderTopLeftRadius", "8rpx"], ["borderTopRightRadius", "8rpx"], ["borderBottomRightRadius", "8rpx"], ["borderBottomLeftRadius", "8rpx"], ["borderTopWidth", "medium"], ["borderRightWidth", "medium"], ["borderBottomWidth", "medium"], ["borderLeftWidth", "medium"], ["borderTopStyle", "none"], ["borderRightStyle", "none"], ["borderBottomStyle", "none"], ["borderLeftStyle", "none"], ["borderTopColor", "#000000"], ["borderRightColor", "#000000"], ["borderBottomColor", "#000000"], ["borderLeftColor", "#000000"]]))], ["opacity-value", _pS(_uM([["fontSize", "26rpx"], ["color", "#ffffff"], ["fontWeight", "bold"]]))], ["rgb-modal-overlay", _pS(_uM([["position", "fixed"], ["top", 0], ["left", 0], ["right", 0], ["bottom", 0], ["backgroundColor", "rgba(0,0,0,0.5)"], ["display", "flex"], ["alignItems", "center"], ["justifyContent", "center"], ["zIndex", 1000]]))], ["rgb-modal", _pS(_uM([["backgroundColor", "#ffffff"], ["borderTopLeftRadius", "12rpx"], ["borderTopRightRadius", "12rpx"], ["borderBottomRightRadius", "12rpx"], ["borderBottomLeftRadius", "12rpx"], ["width", "600rpx"], ["maxHeight", "800rpx"], ["paddingTop", "30rpx"], ["paddingRight", "30rpx"], ["paddingBottom", "30rpx"], ["paddingLeft", "30rpx"], ["boxSizing", "border-box"]]))], ["rgb-modal-header", _pS(_uM([["display", "flex"], ["justifyContent", "center"], ["alignItems", "center"], ["marginBottom", "20rpx"]]))], ["rgb-modal-title", _pS(_uM([["fontSize", "32rpx"], ["fontWeight", "bold"], ["color", "#333333"]]))], ["rgb-preview-section", _pS(_uM([["display", "flex"], ["flexDirection", "row"], ["alignItems", "center"], ["justifyContent", "center"], ["marginBottom", "25rpx"], ["paddingTop", "15rpx"], ["paddingRight", "15rpx"], ["paddingBottom", "15rpx"], ["paddingLeft", "15rpx"], ["backgroundColor", "#f8f8f8"], ["borderTopLeftRadius", "8rpx"], ["borderTopRightRadius", "8rpx"], ["borderBottomRightRadius", "8rpx"], ["borderBottomLeftRadius", "8rpx"]]))], ["rgb-preview-color", _pS(_uM([["width", "60rpx"], ["height", "60rpx"], ["borderTopLeftRadius", "8rpx"], ["borderTopRightRadius", "8rpx"], ["borderBottomRightRadius", "8rpx"], ["borderBottomLeftRadius", "8rpx"], ["borderTopWidth", 1], ["borderRightWidth", 1], ["borderBottomWidth", 1], ["borderLeftWidth", 1], ["borderTopStyle", "solid"], ["borderRightStyle", "solid"], ["borderBottomStyle", "solid"], ["borderLeftStyle", "solid"], ["borderTopColor", "#e5e5e5"], ["borderRightColor", "#e5e5e5"], ["borderBottomColor", "#e5e5e5"], ["borderLeftColor", "#e5e5e5"], ["marginRight", "15rpx"]]))], ["rgb-preview-text", _pS(_uM([["fontSize", "24rpx"], ["color", "#666666"], ["fontFamily", "monospace"]]))], ["rgb-controls", _pS(_uM([["marginBottom", "25rpx"]]))], ["rgb-control-item", _pS(_uM([["display", "flex"], ["flexDirection", "row"], ["alignItems", "center"], ["marginBottom", "20rpx"]]))], ["rgb-label", _pS(_uM([["width", "40rpx"], ["fontSize", "28rpx"], ["fontWeight", "bold"], ["color", "#333333"], ["textAlign", "center"]]))], ["rgb-slider", _pS(_uM([["flex", 1], ["marginTop", 0], ["marginRight", "15rpx"], ["marginBottom", 0], ["marginLeft", "15rpx"]]))], ["rgb-input", _pS(_uM([["width", "120rpx"], ["height", "60rpx"], ["borderTopWidth", 1], ["borderRightWidth", 1], ["borderBottomWidth", 1], ["borderLeftWidth", 1], ["borderTopStyle", "solid"], ["borderRightStyle", "solid"], ["borderBottomStyle", "solid"], ["borderLeftStyle", "solid"], ["borderTopColor", "#e5e5e5"], ["borderRightColor", "#e5e5e5"], ["borderBottomColor", "#e5e5e5"], ["borderLeftColor", "#e5e5e5"], ["borderTopLeftRadius", "6rpx"], ["borderTopRightRadius", "6rpx"], ["borderBottomRightRadius", "6rpx"], ["borderBottomLeftRadius", "6rpx"], ["textAlign", "center"], ["fontSize", "24rpx"], ["paddingTop", 0], ["paddingRight", "10rpx"], ["paddingBottom", 0], ["paddingLeft", "10rpx"], ["boxSizing", "border-box"]]))], ["rgb-modal-buttons", _pS(_uM([["display", "flex"], ["flexDirection", "row"], ["justifyContent", "space-between"]]))], ["rgb-button", _pS(_uM([["width", "45%"], ["height", "70rpx"], ["borderTopLeftRadius", "8rpx"], ["borderTopRightRadius", "8rpx"], ["borderBottomRightRadius", "8rpx"], ["borderBottomLeftRadius", "8rpx"], ["display", "flex"], ["alignItems", "center"], ["justifyContent", "center"]]))], ["rgb-cancel", _pS(_uM([["backgroundColor", "#f5f5f5"], ["borderTopWidth", 1], ["borderRightWidth", 1], ["borderBottomWidth", 1], ["borderLeftWidth", 1], ["borderTopStyle", "solid"], ["borderRightStyle", "solid"], ["borderBottomStyle", "solid"], ["borderLeftStyle", "solid"], ["borderTopColor", "#e5e5e5"], ["borderRightColor", "#e5e5e5"], ["borderBottomColor", "#e5e5e5"], ["borderLeftColor", "#e5e5e5"]]))], ["rgb-confirm", _pS(_uM([["backgroundColor", "#007aff"]]))], ["rgb-button-text", _uM([["", _uM([["fontSize", "28rpx"], ["fontWeight", "bold"]])], [".rgb-cancel ", _uM([["color", "#666666"]])], [".rgb-confirm ", _uM([["color", "#ffffff"]])]])]])]
