<template>
	<view class="Micon">
		<view class="Micon-name" :style="{color:color}">
			{{data.name}}
		</view>
	
		<view class="Micon-box qShadow1" :style="{backgroundColor:bgColor}" @click="selectIcon">
			<view class="mb-defaultIcon" v-if="mValue==''">
				<mb-icons type="Add-1" color="#666" size="60rpx"></mb-icons>
				
			</view>
			
			<view class="mb-showIcon" v-else>
				<text class="mb-showIconText" :style="{fontFamily:data.fontFamily}">{{mValue}}</text>
			</view>
		</view>
		<mb-iconPicker :bgcolor="bgColor" :color="color" :fontFamily="data.fontFamily"  ref="MyIconPicker" @confirm="confirm"></mb-iconPicker>
	</view>
	
	
	
</template>

<script>
	export default {
		name: "Micon",
		props: {
			data: {
				type: Object,
				default: {}
			},
			keyName: {
				type: String,
				default: ""
			
			},
			index: {
				type: Number,
				default: 0
			},
			color: {
				type: String,
				default: '#000'
			},
			bgColor: {
				type: String,
				default: "#f1f4f9"
			
			}
		},
		created() {
			
			
			if(this.data.value!=''){
				this.mValue=this.data.value
			}
			
		
		},
		data() {
			return {
				mValue:""
			};
		},
		watch:{
			data: {
				
				handler(newValue, oldValue) {
					this.mValue= this.data.value
					
				},
				deep: true
			}
			
		},
		methods:{
			selectIcon(){
				this.$refs.MyIconPicker.show=true
			},
			confirm(e){
				this.mValue=e
				this.$emit("change",{index:this.index,value:e})
			}
		}
	}
</script>

<style>
.Micon {
		width: 100%;
		display: flex;
		flex-direction: column;
		padding: 0 20rpx;
		margin-bottom: 20rpx;
	
	}
	
	.Micon-name {
		width: 100%;
		font-size: 32rpx;
		margin-bottom: 10rpx;
	}
	
	.Micon-box {
		width: 150rpx;
		height: 150rpx;
		border-radius: 20rpx;
		overflow: hidden;
		background-color: #f2f4f8;
	}

	.mb-defaultIcon{
		width: 100%;
		height: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
	}
	.mb-showIcon{
		width: 100%;
		height: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
	}
	.mb-showIconText{
		font-size: 80rpx;
	}
</style>